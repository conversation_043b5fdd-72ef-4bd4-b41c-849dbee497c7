function convertYearElements() {
    // แปลงปีในส่วนต่างๆ ของ datepicker
    document.querySelectorAll('.bootstrap-datetimepicker-widget .year, .datepicker-years .year, .bootstrap-datetimepicker-widget th')
        .forEach(el => {
            // ตรวจสอบว่าได้แปลงแล้วหรือยัง
            if (el.dataset.converted === "1") return;
            
            // แปลงปี ค.ศ. เป็น พ.ศ.
            el.textContent = el.textContent.replace(/\b(\d{4})\b/g, (match, year) => {
                const yearNum = parseInt(year, 10);
                // ตรวจสอบว่าเป็นปี ค.ศ. (1900-2100) เพื่อป้องกันการแปลงผิด
                if (yearNum >= 1900 && yearNum <= 2100) {
                    return yearNum + 543;
                }
                return match; // คืนค่าเดิมถ้าไม่ใช่ปี ค.ศ.
            });
            
            // แปลงช่วงปี เช่น "2020-2029" เป็น "2563-2572"
            el.textContent = el.textContent.replace(/(\d{4})\s*-\s*(\d{4})/g, (match, startYear, endYear) => {
                const start = parseInt(startYear, 10);
                const end = parseInt(endYear, 10);
                if (start >= 1900 && start <= 2100 && end >= 1900 && end <= 2100) {
                    return `${start + 543}-${end + 543}`;
                }
                return match;
            });
            
            // ทำเครื่องหมายว่าแปลงแล้ว
            el.dataset.converted = "1";
        });
    
    // แปลงปีใน data attributes ที่ใช้สำหรับการทำงาน
    document.querySelectorAll('.bootstrap-datetimepicker-widget [data-action="selectYear"]')
        .forEach(el => {
            if (el.dataset.yearConverted === "1") return;
            
            // อัพเดท data attributes ถ้ามี
            if (el.dataset.selection) {
                const year = parseInt(el.dataset.selection, 10);
                if (year >= 1900 && year <= 2100) {
                    el.dataset.originalSelection = el.dataset.selection; // เก็บค่าเดิมไว้
                    // ไม่แปลง data-selection เพราะจะทำให้การทำงานผิดพลาด
                }
            }
            
            el.dataset.yearConverted = "1";
        });
    
    // แปลงปีใน picker switch (header)
    document.querySelectorAll('.bootstrap-datetimepicker-widget .picker-switch')
        .forEach(el => {
            if (el.dataset.headerConverted === "1") return;
            
            // แปลงปีในหัวข้อ เช่น "January 2024" เป็น "January 2567"
            el.textContent = el.textContent.replace(/\b(\d{4})\b/g, (match, year) => {
                const yearNum = parseInt(year, 10);
                if (yearNum >= 1900 && yearNum <= 2100) {
                    return yearNum + 543;
                }
                return match;
            });
            
            el.dataset.headerConverted = "1";
        });
}

// ฟังก์ชันสำหรับรีเซ็ตการแปลง (ใช้เมื่อต้องการแปลงใหม่)
function resetYearConversion() {
    document.querySelectorAll('[data-converted="1"], [data-year-converted="1"], [data-header-converted="1"]')
        .forEach(el => {
            delete el.dataset.converted;
            delete el.dataset.yearConverted;
            delete el.dataset.headerConverted;
            
            // คืนค่า data-selection เดิมถ้ามี
            if (el.dataset.originalSelection) {
                el.dataset.selection = el.dataset.originalSelection;
                delete el.dataset.originalSelection;
            }
        });
}

// ฟังก์ชันที่ปรับปรุงแล้วสำหรับใช้กับ MutationObserver
function convertYearElementsWithObserver() {
    convertYearElements();
    
    // สร้าง MutationObserver เพื่อตรวจจับการเปลี่ยนแปลง DOM
    const observer = new MutationObserver((mutations) => {
        let shouldConvert = false;
        
        mutations.forEach((mutation) => {
            // ตรวจสอบว่ามีการเพิ่ม node ใหม่หรือไม่
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // ตรวจสอบว่า node ที่เพิ่มเข้ามาเป็นส่วนของ datepicker หรือไม่
                        if (node.classList?.contains('bootstrap-datetimepicker-widget') ||
                            node.querySelector?.('.bootstrap-datetimepicker-widget') ||
                            node.classList?.contains('datepicker-years') ||
                            node.classList?.contains('year')) {
                            shouldConvert = true;
                        }
                    }
                });
            }
            
            // ตรวจสอบการเปลี่ยนแปลง text content
            if (mutation.type === 'characterData' || 
                (mutation.type === 'childList' && mutation.target.classList?.contains('year'))) {
                shouldConvert = true;
            }
        });
        
        if (shouldConvert) {
            // ใช้ setTimeout เพื่อให้ DOM อัพเดทเสร็จก่อน
            setTimeout(convertYearElements, 10);
        }
    });
    
    // เริ่มการสังเกตการณ์
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
    
    return observer;
}

// ตัวอย่างการใช้งาน
/*
// เรียกใช้เมื่อเปิด datepicker
$('#myDatePicker').on('dp.show', function() {
    setTimeout(convertYearElements, 50);
});

// เรียกใช้เมื่อเปลี่ยน view
$('#myDatePicker').on('dp.update', function() {
    setTimeout(convertYearElements, 50);
});

// หรือใช้กับ MutationObserver
const observer = convertYearElementsWithObserver();

// หยุดการสังเกตการณ์เมื่อไม่ต้องการแล้ว
// observer.disconnect();
*/
