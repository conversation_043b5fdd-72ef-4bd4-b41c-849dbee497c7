#!make
include .env
export $(shell sed 's/=.*//' .env)
CWD := ${shell pwd}

# COLORS
GREEN  := $(shell tput -Txterm setaf 2)
YELLOW := $(shell tput -Txterm setaf 3)
RESET  := $(shell tput -Txterm sgr0)

TARGET_MAX_CHAR_NUM=20

NAME_PROJ := $(shell jq -r '.name' package.json)
VERSION_PROJ := $(shell git rev-parse --short HEAD)
TAG_PROJ := $(shell jq -r '.tag' package.json)
# format date DD/MM/YYYY HH:MM:SS + version 
DEPLOY_VERSION := ${shell date +'%d-%m-%Y-%H:%M:%S'}-${VERSION_PROJ}

ifeq ($(TAG_PROJ), null)
	TAG_PROJ := latest
endif

.PHONY: vendor test

## Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo '  ${YELLOW}make${RESET} ${GREEN}<target>${RESET}'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-_0-9]+:/ { \
		helpMessage = match(lastLine, /^##(.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 3, RLENGTH); \
			printf "  ${YELLOW}%-$(TARGET_MAX_CHAR_NUM)s${RESET} ${GREEN}%s${RESET}\n", helpCommand, helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

## run dev with inspect network
dev:
	@echo "Starting dev server..."
	INSPECT_NETWORK=TRUE SHOW_VERSION=true DEPLOY_VERSION_GIT=${DEPLOY_VERSION} yarn dev

# docker

# info
USERNAME := $(shell jq -r '.hub' package.json)
# lowercase
USERNAME := $(shell echo $(USERNAME) | tr A-Z a-z)

## build for production
build:
	@echo "Building..."
	docker build \
		--build-arg HOST=${HOST} \
		--build-arg PORT=${PORT} \
		-t  '$(NAME_PROJ):${VERSION_PROJ}' \
		--target production .
	@echo "Done!"

## build for production with version building
build-version:
	@echo "Building..."
	make update_version
	@echo "Set ENV to docker"
	docker build \
		--build-arg HOST=${HOST} \
		--build-arg PORT=${PORT} \
		--build-arg SHOW_VERSION=true -t '$(NAME_PROJ):${VERSION_PROJ}' --target production .
	@echo "Done!"

## build for development
build-dev:
	@echo "Building..."
	docker build \
		--build-arg HOST=${HOST} \
		--build-arg PORT=${PORT} \
		--build-arg SHOW_VERSION=true -t '$(NAME_PROJ):${VERSION_PROJ}' --target development .
	@echo "Done!"

## run for development
run-dev:
	@echo "Running..."
	make build-dev
	docker rm -f ${NAME_PROJ} || true
	docker run -d \
		--rm \
		-it \
		--name ${NAME_PROJ} \
		-p 3002:${PORT} \
		-v ${PWD}:/app \
		-w /app \
		'$(NAME_PROJ):${VERSION_PROJ}'
	@echo "Done!"

## run for production
run:
	@echo "Running..."
	make build
	##stop && remove container && remove image
	docker rm -f ${NAME_PROJ} || true
	docker image prune -f
	docker run -d \
		--name ${NAME_PROJ} \
		-p 3002:${PORT} \
		'$(NAME_PROJ):${VERSION_PROJ}'
	@echo "Done!"

## run for production with version building
run-version:
	@echo "Running..."
	make build-version
	docker rm -f ${NAME_PROJ} || true
	docker run -d \
		--name ${NAME_PROJ} \
		-p 3002:${PORT} \
		'$(NAME_PROJ):${VERSION_PROJ}'
	@echo "Done!"

## push image with version production to docker hub
push-version:
	@echo "Pushing...to repository "
	make build
	docker tag '$(NAME_PROJ):${VERSION_PROJ}' ${USERNAME}/$(NAME_PROJ):${VERSION_PROJ}
	docker push ${USERNAME}/$(NAME_PROJ):${VERSION_PROJ}
	docker rmi ${USERNAME}/$(NAME_PROJ):${VERSION_PROJ}
	@echo "Done!"

## push image with tag production to docker hub	
push-release:
	@echo "Releasing..."
	make build
	docker tag '$(NAME_PROJ):${VERSION_PROJ}' ${USERNAME}/$(NAME_PROJ):${TAG_PROJ}
	docker push ${USERNAME}/$(NAME_PROJ):${TAG_PROJ}
	docker rmi ${USERNAME}/$(NAME_PROJ):${TAG_PROJ}
	@echo "Done!"
	


## update version in .env file & package.json
update_version_env:
	if [ -z "${DEPLOY_VERSION_GIT}" ]; then \
		echo "DEPLOY_VERSION_GIT=${DEPLOY_VERSION}" >> .env; \
	else \
		sed -i "" "/^DEPLOY_VERSION_GIT/d" .env; \
		echo "DEPLOY_VERSION_GIT=${DEPLOY_VERSION}" >> .env; \
	fi


## update version in package.json
update_version:
	cat package.json | jq "if has(\"deploy_version\") then .deploy_version = \"${DEPLOY_VERSION}\" else . + {deploy_version: \"${DEPLOY_VERSION}\"} end" > package.json.tmp
	cp package.json package.json.bak
	mv package.json.tmp package.json


# AWS_REGION := $(shell jq -r '.region' secrets/aws.json)
# AWS_USERNAME := $(shell jq -r '.username' secrets/aws.json)
# AWS_ECR_URI := $(shell jq -r '.ecr_uri' secrets/aws.json)
## push image to aws ecr
push-aws-ecr:
	@echo "Pushing to AWS Elastic Container Registry..."
	aws ecr get-login-password \
		--region ${AWS_REGION} | docker login \
		--username ${AWS_USERNAME} \
		--password-stdin https://${AWS_ECR_URI}
	make update_version
	make build
	docker tag '$(NAME_PROJ):${VERSION_PROJ}' ${AWS_ECR_URI}/${NAME_PROJ}:${TAG_PROJ}
	docker push ${AWS_ECR_URI}/${NAME_PROJ}:${TAG_PROJ}
	docker rmi ${AWS_ECR_URI}/${NAME_PROJ}:${TAG_PROJ}
	@echo "Done!"

build-with-params:
	@docker buildx build --platform linux/amd64 --build-arg ENV_FILE=.env.$${APP_ENV} -t $${IMAGE}:$${APP_ENV}-$${TAG} --push .

.PHONY: portal_build
portal_build:
	@aws ecr get-login-password --region $(INFRA_AWS_REGION) | docker login --username AWS --password-stdin https://$(INFRA_AWS_ACCOUNT_NO).dkr.ecr.ap-southeast-1.amazonaws.com
	@docker buildx build --platform linux/amd64 --build-arg ENV_FILE=.env -t $(INFRA_AWS_ACCOUNT_NO).dkr.ecr.ap-southeast-1.amazonaws.com/services-${BUILD_ENV}:$(IMAGE_TAG) --push .
	
.PHONY: build_web
build_web:
	@docker buildx build --no-cache --platform linux/amd64 -f Dockerfile.web --build-arg ENV_FILE=.env.docker \
	--build-arg BUILDERIMG=${FULL_BUILDER_IMAGE} \
	--build-arg DEPLOYIMG=${FULL_DEPLOY_IMAGE} \
	-t $(FULL_IMAGE_REG) --push .