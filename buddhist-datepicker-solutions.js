// วิธีที่ 1: แก้ไข postformat ให้ตรวจสอบก่อนแปลง
moment.defineLocale('th-buddhist', {
    months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),
    monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),
    weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),
    weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),
    weekdaysMin: 'อา_จ_อ_พ_พฤ_ศ_ส'.split('_'),
    longDateFormat: {
        LT: 'H:mm',
        LTS: 'H:mm:ss',
        L: 'DD/MM/YYYY',
        LL: 'D MMMM YYYY',
        LLL: 'D MMMM YYYY เวลา H:mm',
        LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'
    },
    // แก้ไข postformat ให้ตรวจสอบก่อนแปลง
    postformat: function (string) {
        return string.replace(/\b(\d{4})\b/g, function (match, year) {
            const yearNum = parseInt(year, 10);
            // แปลงเฉพาะปี ค.ศ. (1900-2100) เป็น พ.ศ.
            if (yearNum >= 1900 && yearNum <= 2100) {
                return yearNum + 543;
            }
            // ถ้าเป็น พ.ศ. อยู่แล้ว (2443-2643) ไม่ต้องแปลง
            return match;
        });
    }
});

// วิธีที่ 2: ใช้ preparse และ postformat ร่วมกัน
moment.defineLocale('th-buddhist-safe', {
    months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),
    monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),
    weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),
    weekdaysShort: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),
    weekdaysMin: 'อา_จ_อ_พ_พฤ_ศ_ส'.split('_'),
    longDateFormat: {
        LT: 'H:mm',
        LTS: 'H:mm:ss',
        L: 'DD/MM/YYYY',
        LL: 'D MMMM YYYY',
        LLL: 'D MMMM YYYY เวลา H:mm',
        LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'
    },
    // แปลง พ.ศ. กลับเป็น ค.ศ. เมื่อ parse
    preparse: function (string) {
        return string.replace(/\b(\d{4})\b/g, function (match, year) {
            const yearNum = parseInt(year, 10);
            // แปลง พ.ศ. (2443-2643) กลับเป็น ค.ศ.
            if (yearNum >= 2443 && yearNum <= 2643) {
                return yearNum - 543;
            }
            return match;
        });
    },
    // แปลง ค.ศ. เป็น พ.ศ. เมื่อแสดงผล
    postformat: function (string) {
        return string.replace(/\b(\d{4})\b/g, function (match, year) {
            const yearNum = parseInt(year, 10);
            // แปลง ค.ศ. (1900-2100) เป็น พ.ศ.
            if (yearNum >= 1900 && yearNum <= 2100) {
                return yearNum + 543;
            }
            return match;
        });
    }
});

// วิธีที่ 3: ใช้ DOM Manipulation แทน postformat
function initBuddhistDatePicker(selector, options = {}) {
    const defaultOptions = {
        locale: 'th', // ใช้ locale ปกติ
        format: 'DD/MM/YYYY',
        ...options
    };
    
    const $element = $(selector);
    
    // สร้าง datepicker ปกติ
    $element.datetimepicker(defaultOptions);
    
    // ฟังก์ชันแปลงปี
    function convertYearDisplay() {
        $('.bootstrap-datetimepicker-widget').find('.year, .picker-switch, th').each(function() {
            const $this = $(this);
            
            // ตรวจสอบว่าแปลงแล้วหรือยัง
            if ($this.data('year-converted')) return;
            
            // เก็บค่าเดิม
            if (!$this.data('original-text')) {
                $this.data('original-text', $this.text());
            }
            
            // แปลงจากค่าเดิม
            const originalText = $this.data('original-text');
            const convertedText = originalText.replace(/\b(\d{4})\b/g, function(match, year) {
                const yearNum = parseInt(year, 10);
                if (yearNum >= 1900 && yearNum <= 2100) {
                    return yearNum + 543;
                }
                return match;
            });
            
            $this.text(convertedText);
            $this.data('year-converted', true);
        });
    }
    
    // รีเซ็ตการแปลงเมื่อ datepicker ถูกสร้างใหม่
    function resetConversion() {
        $('.bootstrap-datetimepicker-widget').find('[data-year-converted]').each(function() {
            $(this).removeData('year-converted').removeData('original-text');
        });
    }
    
    // Bind events
    $element.on('dp.show', function() {
        resetConversion();
        setTimeout(convertYearDisplay, 50);
    });
    
    $element.on('dp.update', function() {
        setTimeout(convertYearDisplay, 50);
    });
    
    // แปลงการแสดงผลใน input
    $element.on('dp.change', function(e) {
        if (e.date) {
            const buddhistYear = e.date.year() + 543;
            const formatted = e.date.format('DD/MM/') + buddhistYear;
            $element.find('input').val(formatted);
        }
    });
    
    return $element.data('DateTimePicker');
}

// วิธีที่ 4: Custom DatePicker Class
class BuddhistDatePicker {
    constructor(selector, options = {}) {
        this.element = $(selector);
        this.options = {
            locale: 'th',
            format: 'DD/MM/YYYY',
            ...options
        };
        this.picker = null;
        this.init();
    }
    
    init() {
        // สร้าง datepicker
        this.picker = this.element.datetimepicker(this.options);
        
        // Bind events
        this.element.on('dp.show', () => {
            this.resetConversion();
            setTimeout(() => this.convertDisplay(), 50);
        });
        
        this.element.on('dp.update', () => {
            setTimeout(() => this.convertDisplay(), 50);
        });
        
        this.element.on('dp.change', (e) => {
            this.handleDateChange(e);
        });
    }
    
    resetConversion() {
        $('.bootstrap-datetimepicker-widget')
            .find('[data-buddhist-converted]')
            .removeAttr('data-buddhist-converted')
            .each(function() {
                const original = $(this).data('buddhist-original');
                if (original) {
                    $(this).text(original);
                }
            });
    }
    
    convertDisplay() {
        $('.bootstrap-datetimepicker-widget')
            .find('.year, .picker-switch, th')
            .each((i, el) => {
                const $el = $(el);
                
                if ($el.attr('data-buddhist-converted')) return;
                
                // เก็บค่าเดิม
                $el.data('buddhist-original', $el.text());
                
                // แปลงปี
                const converted = $el.text().replace(/\b(\d{4})\b/g, (match, year) => {
                    const y = parseInt(year, 10);
                    return (y >= 1900 && y <= 2100) ? y + 543 : match;
                });
                
                $el.text(converted);
                $el.attr('data-buddhist-converted', 'true');
            });
    }
    
    handleDateChange(e) {
        if (e.date) {
            const buddhistYear = e.date.year() + 543;
            const formatted = e.date.format('DD/MM/') + buddhistYear;
            this.element.find('input').val(formatted);
        }
    }
    
    // Public methods
    getDate() {
        return this.element.data('DateTimePicker').date();
    }
    
    setDate(date) {
        // แปลง พ.ศ. เป็น ค.ศ. ถ้าจำเป็น
        if (typeof date === 'string') {
            date = date.replace(/\b(\d{4})\b/g, (match, year) => {
                const y = parseInt(year, 10);
                return y > 2400 ? y - 543 : match;
            });
        }
        this.element.data('DateTimePicker').date(date);
    }
    
    destroy() {
        this.element.data('DateTimePicker').destroy();
    }
}

// ตัวอย่างการใช้งาน:

// วิธีที่ 1: ใช้ locale ที่แก้ไขแล้ว
$('#datepicker1').datetimepicker({
    locale: 'th-buddhist-safe',
    format: 'DD/MM/YYYY'
});

// วิธีที่ 2: ใช้ฟังก์ชัน helper
initBuddhistDatePicker('#datepicker2', {
    format: 'DD/MM/YYYY',
    showTodayButton: true
});

// วิธีที่ 3: ใช้ class
const picker = new BuddhistDatePicker('#datepicker3', {
    format: 'DD/MM/YYYY',
    showTodayButton: true,
    showClear: true
});
