import en from '@/locales/en.json';
import th from '@/locales/th.json';
import { useTranslation as useLanguageContext } from '@/contexts/LanguageContext';

export function useTranslation() {
  const { locale } = useLanguageContext();

  const translations = locale === 'th' ? th : en;

  const t = (key: string, vars?: Record<string, string | number>): string => {
    const parts = key.split('.');
    let current: unknown = translations;

    for (const part of parts) {
      if (typeof current === 'object' && current !== null && part in current) {
        current = (current as Record<string, unknown>)[part];
      } else {
        return key; // key ไม่เจอ
      }
    }

    if (typeof current === 'string') {
      let str = current;

      if (vars) {
        for (const [k, v] of Object.entries(vars)) {
          const regex = new RegExp(`{${k}}`, 'g'); // ใช้ global replace
          str = str.replace(regex, String(v));
        }
      }

      return str;
    }

    return key;
  };

  return { t, language: locale };
}
