// lib/navigation.ts

export function handleGoBackToLineLiff(systemBrandCode: string) {
  console.log("GoBack called with:", systemBrandCode);

  try {
    if (systemBrandCode === "tcc") {
      const message = {
        method: "close",
        route: "",
      };

      const userAgent = navigator.userAgent || "";

      if (/android/i.test(userAgent)) {
        window.tccObserver?.postMessage(JSON.stringify(message));
      } else if (/iPad|iPhone|iPod/i.test(userAgent)) {
        window.webkit?.messageHandlers?.tccObserver?.postMessage(JSON.stringify(message));
      } else {
        window.close();
        window.location.href = "about:blank";
      }
    } 
    
    else if (systemBrandCode === "sz" || systemBrandCode === "dq") {
      alert("Redirecting to LIFF home URL");
      const homeUrl = process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? "/";
      window.location.assign(homeUrl);
    } 
    
    else {
      alert("Default fallback triggered");
      window.close();
      window.location.href = "about:blank";
    }
  } catch (err) {
    console.error("handleGoBackToLineLiff error:", err);
    alert("Error occurred: " + (err as Error).message);
    window.location.href = "/";
  }
}
