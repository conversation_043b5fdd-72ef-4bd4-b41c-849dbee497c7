import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
// import { getToken } from "next-auth/jwt";

export async function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const token = url.searchParams.get("token");
  const response = NextResponse.next();

  if (!token) {
    return response;
  }

  try {
    const rawCallback = url.pathname + url.search;
    const callbackUrl = encodeURIComponent(`${process.env.NEXT_PUBLIC_BASE_URL}${rawCallback}`);
    const redirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/signin?callbackUrl=${callbackUrl}`;

    return NextResponse.redirect(
      new URL(`/api/auth/signin?callbackUrl=${callbackUrl}`, redirectUrl)
    );
  } catch (error) {
    console.error("Auth middleware error:", error);
    return NextResponse.redirect(new URL("/auth/error", url));
  }
}

export const config = {
  // Apply this middleware to all routes except API routes and static files
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
