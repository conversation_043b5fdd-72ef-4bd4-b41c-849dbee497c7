// import { z } from "zod";
// import { minorClient } from "./minor-api-client";

// // ========== Zod Schemas ==========
// const apiResponseSchema = z.object({
//   data: z.object({
//     accessToken: z.string(),
//     systemBrandCode: z.string(),
//     contentLanguage: z.string(),
//   }),
// });

// const verifyResponseSchema = z.object({
//   accessToken: z.string(),
//   systemBrandCode: z.string(),
//   contentLanguage: z.string(),
// });

// // ========== Types ==========
// type Config = { accessToken?: string };
// type VerifyTokenParams = { token: string, langCode: string; };

// export const createMinorApi = (token: Config) => {


//   return {
//     verifyToken: async ({ token, langCode }: VerifyTokenParams) => {

//       try {
//         // console.log("Before Token Verifying:", token);
//         const response = await minorClient.post("/webview/auth/verify/exchange-token",{token},{
//           headers: {
//             Authorization: `Bearer ${token}`,
//             "Content-Language": langCode
//           },
//         });

//         if (response.status !== 200 || !response.data) {
//           throw new Error("Invalid response from server");
//         }

//         const parsed = apiResponseSchema.parse(response.data);
//         return verifyResponseSchema.parse(parsed.data);

//       } catch (error) {
//         console.error("Token verification failed:", error);
//         throw error;
//       }
//     }
//   };
// };

import { z } from "zod";
import { minorClient } from "./minor-api-client";

// ========== Zod Schemas ==========
const apiResponseSchema = z.object({
  data: z.object({
    accessToken: z.string(),
    systemBrandCode: z.string(),
    contentLanguage: z.string(),
  }),
});

const verifyResponseSchema = z.object({
  accessToken: z.string(),
  systemBrandCode: z.string(),
  contentLanguage: z.string(),
});

// ========== Types ==========
type Config = { accessToken?: string };
type VerifyTokenParams = { token: string; langCode: string };

export const createMinorApi = (_config?: Config) => {
  return {
    verifyToken: async ({ token, langCode }: VerifyTokenParams) => {
      try {
        const response = await minorClient.post("/webview/auth/verify/exchange-token",{ token },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Language": langCode,
            },
          }
        );

        if (response.status !== 200 || !response.data) {
          throw new Error("Invalid response from server");
        }

        const parsed = apiResponseSchema.parse(response.data);
        return verifyResponseSchema.parse(parsed.data);
      } catch (error) {
        console.error("❌ Token verification failed:", error);
        throw error;
      }
    },
  };
};
