import { postRouter } from "@/server/api/routers/post";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { productRouter } from "./routers/product";

import { cardRouter } from "./routers/card";
import { packageRouter } from "./routers/package";
import { checkchargeRouter } from "./routers/checkcharge";
import { packagepurchaseRouter } from "./routers/packagepurchase";
import { cardCategoryRouter } from "./routers/cardcategory";
import { cardtopupRouter } from "./routers/cardtopup";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  product: productRouter,
  card: cardRouter,
  package: packageRouter,
  checkcharge: checkchargeRouter,
  packagepurchase: packagepurchaseRouter,
  cardCategory: cardCategoryRouter,
  cardtopup: cardtopupRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
