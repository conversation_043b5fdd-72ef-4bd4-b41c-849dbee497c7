import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

const PackageItemSchema = z.object({
  packageID: z.number(),
  packageTitle: z.string(),
  packageDetail: z.string(),
  packageImage: z.string(),
  dateActiveEnd: z.string(),
  originalPrice: z.number(),
  discountAmount: z.number(),
  discountPrice: z.number(),
  totalCoupon: z.number(),
  soldPercentage: z.number(),
  isShowProgressBar: z.boolean(),
  nearlyExpirePeriodAlert: z.number(),
  orderingNo: z.number(),
});

const PackageListResponseSchema = z.object({
  code: z.number(),
  data: z.object({
    pageNo: z.number(),
    pageSize: z.number(),
    totalPage: z.number(),
    list: z.array(PackageItemSchema),
  }),
  httpStatus: z.number(),
  message: z.string(),
});

const inputSchema = z.object({
  pageSize: z.number().default(5),
  pageNo: z.number().default(1),
}).optional();


const PackageDetailResponseSchema = z.object({
    code: z.number(),
    data: z.object({
        packageID: z.number(),
        packageTitle: z.string(),
        packageDetail: z.string(),
        packageImage: z.string(),
        termsAndConditions: z.string(),
        dateActiveEnd: z.string(),
        totalCoupon: z.number(),
        originalPrice: z.number(),
        discountPrice: z.number(),
        discountAmount: z.number(),
        status: z.string(),
        soldPercentage: z.number(),
        quotaAccountRemain: z.number(),
        isShowProgressBar: z.boolean(),
        isShowPurchaseRemain: z.boolean(),
        quotaPeriod: z.string(),
        coupon: z.array(
            z.object({
                shopProductID: z.number(),
                validUntil: z.string(),
                validAge: z.string(),
                image: z.string().nullable(),
                detail: z.string(),
                title: z.string(),
                couponAmount: z.number(),
            }),
        ),
    }),
    httpStatus: z.number(),
    message: z.string(),
});

const PackageProductDetailResponseSchema = z.object({
    code: z.number(),
    data: z.object({
        description: z.string(),
        detail: z.string(),
        image: z.string(),
        shopProductID: z.number(),
        title: z.string(),
        validAge: z.string(),
        validUntil: z.string(),
    }),
    httpStatus: z.number(),
    message: z.string(),
});

export const packageRouter = createTRPCRouter({
    getPackageList: protectedProcedure.input(inputSchema).query(async ({ ctx, input }) => {
        const { pageSize = 10, pageNo = 1 } = input ?? {};
        const accessToken = ctx.accessToken;
        const langCode = ctx.session.user.contentLanguage;

        if (!accessToken) throw new Error("No access token available");

        const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/package/list?pageSize=${pageSize}&pageNo=${pageNo}`,
        {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                "Content-Type": "application/json",
                "Content-Language": langCode,
            },
        });

        const newAccessToken = response.headers.get('New-Access-Token');

        if (!response.ok) {
            console.error("Failed to fetch package list:", await response.text());
            throw new Error(`API request failed with status ${response.status}`);
        }

        const data = PackageListResponseSchema.parse(await response.json());

        return {
            data,
            newAccessToken: newAccessToken,
        };
    }),
    getPacakgeDetail: protectedProcedure.input(z.object({ packageId: z.string() })).query(async ({ ctx, input }) => {
        // console.log('getPacakgeDetail');
        const accessToken = ctx.accessToken;
        const langCode = ctx.session.user.contentLanguage;
        // console.log('OLD Access Token:', accessToken);
        // console.log('Package ID B:', input.packageId)
        const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/package/detail/${(input.packageId)}`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Content-Language': langCode,
            },
        });

        const newAccessToken = response.headers.get('New-Access-Token');
        if(!response.ok){
            console.error("Failed to fetch package detail:", await response.text());
            throw new Error(`API request failed with status ${response.status}`);
        }

        const data = PackageDetailResponseSchema.parse(await response.json());
        return { data: data.data, newAccessToken };
    }),
    getPackageProductDetail: protectedProcedure.input(z.object({ shopProductID: z.string() })).query(async ({ ctx, input }) => {
        const accessToken = ctx.accessToken;
        const langCode = ctx.session.user.contentLanguage;
        // console.log("Access Token:", accessToken);
        // console.log("Shop Product ID:", input.shopProductID);   
        const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/package/productdetail/${input.shopProductID}`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Content-Language': langCode,
            },
        });
        const newAccessToken = response.headers.get('New-Access-Token');
        if(!response.ok){   
            console.error("Failed to fetch package product detail:", await response.text());
            throw new Error(`API request failed with status ${response.status}`);
        }
        const data = PackageProductDetailResponseSchema.parse(await response.json());
        return { data: data.data, newAccessToken };
    }),
});
