import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";


interface PackagePurchaseLookupResponse { 
  code: string;
  data: {
    referenceCode: string;
  };
  httpStatus: number;
  message: string;
  serverTime: string;
  reference: string;
}

interface PackagePurchaseConfirmResponse {
    code: string;
    data: {
      authorizeURI: string;
    };
    httpStatus: number;
    message: string;
    serverTime: string;
    reference: string;
}


export const packagepurchaseRouter = createTRPCRouter({ 
  packagePurchaseLookup: protectedProcedure.input(z.object({ packageId: z.string() })).mutation(async ({ ctx, input }) => {

    try {
      const accessToken = ctx.accessToken;
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/package/lookup`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageID: parseInt(input.packageId),
          channel: "Online"
        }),
      });

      const newAccessToken = response.headers.get('New-Access-Token');
      const data = await response.json() as PackagePurchaseLookupResponse;
    //   console.log('Data Response:', data);
      if(!response.ok){
        console.error("Failed to lookup package:", await response.text());
        throw new Error('Failed to lookup package');
      }

      return { data: data, newAccessToken };

    } catch (error) {
      console.error('Lookup failed:', error);
      throw new Error('An error occurred while looking up the package.');
    }
  }),
  packagePurchaseConfirm: protectedProcedure.input(z.object({ referenceCode: z.string(), cardID: z.string() })).mutation(async ({ ctx, input }) => {
    let newAccessToken: string | null = null;
    try {
      const accessToken = ctx.accessToken;
      
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/package/confirm`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          referenceCode: input.referenceCode,
          cardID: parseInt(input.cardID),
        }),
      });
      newAccessToken = response.headers.get('New-Access-Token');
      const data = await response.json() as PackagePurchaseConfirmResponse;
      // console.log('New Access tokenA::', newAccessToken);
      if(!response.ok){
        console.error("Failed to confirm package:", await response.text());
      }
      return { data: data, newAccessToken };

    } catch (error) {
      
      const typedError = error as { message?: string; code?: string | number };

      return {
        error: true,
        message: typedError.message ?? 'An error occurred while confirming the package purchase.',
        newAccessToken: newAccessToken ?? null,
        data: {
          code: typedError.code ?? 500,
        } as PackagePurchaseConfirmResponse,
      };
    }
  }),
});
  
