// import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

interface CardCategoryResponse {
  code: string;
  data: {
    cards: {
      CardStatus: string;
      cardGraphicURL: string;
      cardHidden: boolean;
      cardName: string;
      cardNumber: string;
      cardSystemCategory: string;
      credits: {
        creditAmount: number;
        creditCode: string;
        creditName: string;
      };
      token: string;
    }[];
    serverTime: string;
  };
  httpStatus: number;
  message: string;
  serverTime: string;
  reference: string;
}

export const cardCategoryRouter = createTRPCRouter({
  getCardCategoryList: protectedProcedure.query(async ({ ctx }) => {
    const accessToken = ctx.accessToken;

    try {
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/cards`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
      });
      
      const data = await response.json() as CardCategoryResponse;
      console.log('Card Category response:', data);
      
      const newAccessToken = response.headers.get('New-Access-Token');

      if (!response.ok) {
        console.log('Error response:', response);
        throw new Error('Failed to get card category list');
      }

      return {
        cards: data.data?.cards || [], 
        newAccessToken
      };
    } catch (error) {
      console.error('Fetch error:', error);
      throw new Error('Failed to fetch card category list');
    }
  }),

  // เพิ่ม mutation procedure สำหรับเรียกข้อมูลเดียวกันแต่เป็น mutation
  fetchCardCategoryList: protectedProcedure.mutation(async ({ ctx }) => {
    const accessToken = ctx.accessToken;

    try {
      const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/cards`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
      });
      
      const data = await response.json() as CardCategoryResponse;
      console.log('Card Category response (mutation):', data);
      
      const newAccessToken = response.headers.get('New-Access-Token');

      if (!response.ok) {
        console.log('Error response:', response);
        throw new Error('Failed to get card category list');
      }

      return {
        cards: data.data?.cards || [], 
        newAccessToken
      };
    } catch (error) {
      console.error('Fetch error:', error);
      throw new Error('Failed to fetch card category list');
    }
  })
});
