import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

interface CardTopupResponse {
  code: string;
  data: {
    "3DSRate": number;
    authorizeURI: string;
    requestCardTopupID: number;
  };
  httpStatus: number;
  message: string;
  serverTime: string;
  reference: string;
}

export const cardtopupRouter = createTRPCRouter({
  topupCard: protectedProcedure
    .input(z.object({ 
      cardNumber: z.string(),
      topupAmount: z.number(),
      paymentType: z.string(),
      paymentChannel: z.string(),
      creditCardID: z.number()
    }))
    .mutation(async ({ ctx, input }) => {
      const accessToken = ctx.accessToken; 
      
      try {
        const response = await fetch(`${process.env.MINOR_API_BASE_URL}/webview/wallet/topuponline`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            cardNumber: input.cardNumber,
            topupAmount: input.topupAmount,
            paymentType: input.paymentType,
            paymentChannel: input.paymentChannel,
            creditCardID: input.creditCardID
          })
        });
        
        const newAccessToken = response.headers.get('New-Access-Token');
        const data = await response.json() as CardTopupResponse;
        // console.log('ResData:',data)
        if(!response.ok){
          throw new Error('Failed to process card topup');
        }

        return {
          data,
          newAccessToken
        };
      } catch (err) {
        console.error('Card topup request failed:', err);
        throw new Error('An error occurred while processing card topup.');
      }
    })
});
