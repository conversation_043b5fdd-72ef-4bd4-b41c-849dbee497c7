import { type ReactNode } from "react";

import { auth } from "@/server/auth";

export async function ProtectedRoute({ children }: { children: ReactNode }) {
  const session = await auth();

  if (!session) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Unauthorized</h1>
        <p className="mt-4">You must be logged in to view this page</p>
      </div>
    );
  }

  return <>{children}</>;
}
