import { useEffect, useState } from 'react';

interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'default';
  onClose: () => void;
}

export default function Toast({ message, type = 'default', onClose }: ToastProps) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    // เริ่ม fade out หลัง 2.5 วิ (ก่อนจะปิดจริง)
    const fadeTimer = setTimeout(() => {
      setVisible(false);
    }, 2500);

    // ปิด toast หลัง 3 วิ
    const closeTimer = setTimeout(() => {
      onClose();
    }, 3000);

    return () => {
      clearTimeout(fadeTimer);
      clearTimeout(closeTimer);
    };
  }, [onClose]);

  return (
    <div
      className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
        px-4 py-2 rounded-lg shadow-lg text-white
        transition-opacity duration-500
        ${visible ? 'opacity-100' : 'opacity-0'}
        ${type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-gray-500'}
      `}
    >
      {message}
    </div>
  );
}
