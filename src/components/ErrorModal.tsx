"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import React from "react";

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  redirectTo?: string;
  title?: string;
  description?: string;
  showRedirectButton?: boolean;        // ✅ เพิ่มตัวเลือกว่าจะโชว์ปุ่มที่ 2 ไหม
  redirectButtonText?: string;         // ✅ เปลี่ยนข้อความปุ่ม Redirect ได้
  cancelButtonText?: string;
  type?: string;
  onConfirm?: () => void;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  redirectTo,
  showRedirectButton = false,
  redirectButtonText,
  cancelButtonText,
  onConfirm,
  type
}) => {
  const router = useRouter();

  if (!isOpen) return null;

  const handleRedirect = () => {
    // console.log('redirectTo::',redirectTo)
    if (redirectTo) {
      router.push(redirectTo);
    } else if (onConfirm) {
      onConfirm();
    }
    onClose();
  };


  return (

    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-200"
      />

      {/* Modal content */}
      <div className="bg-white rounded-2xl p-6 w-[90%] max-w-md relative z-50 shadow-xl">
        
        {type == "warning" && (
          <>
            <div className="flex items-center justify-center">
              {/* <Image
                src="/images/package/error-icon.svg"
                alt="Error Icon"
                width={50}
                height={50}
                className="object-contain"
              /> */}
            </div>

            <h3 className="text-xl font-semibold text-center my-4">{title}</h3>

            {/* Description */}
            <div className="space-y-2 text-center mb-4">
              <p className="text-gray-400 text-md">{description}</p>
            </div>

            {/* Buttons */}


            <div className={`flex ${showRedirectButton ? "flex-row gap-4" : "flex-col gap-3"} mt-4`}>
              {showRedirectButton && (

                <Button variant="outline"
                  onClick={onClose}
                  className="w-1/2 text-tertiary py-6 border border-tertiary rounded-md"
                >
                  {cancelButtonText}
                </Button>
              )}
              
              <Button
                onClick={handleRedirect}
                className={`py-6 text-lg ${showRedirectButton ? "w-1/2" : "w-full"} bg-tertiary text-white`}
              >
                {redirectButtonText}
              </Button>
            </div>
          </>
        )}
        
        {type == "error" && (
          <>
            <div className="flex items-center justify-center">
              <Image
                src="/images/package/error-icon.svg"
                alt="Error Icon"
                width={50}
                height={50}
                className="object-contain"
              />
            </div>

            <h3 className="text-xl font-semibold text-center my-4">{title}</h3>

            {/* Description */}
            <div className="space-y-2 text-center mb-4">
              <p className="text-gray-400 text-md">{description}</p>
            </div>

            {/* Buttons */}


            <div className={`flex ${showRedirectButton ? "flex-row gap-4" : "flex-col gap-3"} mt-4`}>
              {showRedirectButton && (
                <Button
                  onClick={onClose}
                  className="w-1/2 py-6 text-lg bg-gray-300 hover:bg-gray-400 text-white"
                >
                  {cancelButtonText}
                </Button>
              )}
              <Button
                onClick={handleRedirect}
                className={`py-6 text-lg ${showRedirectButton ? "w-1/2" : "w-full"} bg-tertiary text-white`}
              >
                {redirectButtonText}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>


  );

};

