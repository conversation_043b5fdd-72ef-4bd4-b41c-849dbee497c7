import Link from "next/link";
import { ShoppingCart } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MobileMenu } from "./mobile-menu";
import { MobileSearch } from "./mobile-search";

export const Header = () => {
  return (
    <header className="sticky top-0 z-10 border-b bg-background">
      <div className="container mx-auto flex h-16 max-w-6xl items-center justify-between px-4 py-4">
        <div className="flex items-center gap-6">
          <Link href="#" className="flex items-center gap-2 font-bold">
            <ShoppingCart className="h-5 w-5 text-primary" />
            <span className="text-primary">ShopMart</span>
          </Link>
          {/* Desktop Navigation */}
          <nav className="hidden gap-6 md:flex">
            <Link
              href="/"
              className="text-sm font-medium underline-offset-4 hover:underline"
            >
              Home
            </Link>
            <Link
              href="/product"
              className="text-sm font-medium underline-offset-4 hover:underline"
            >
              Products
            </Link>
            <Link
              href="#"
              className="text-sm font-medium underline-offset-4 hover:underline"
            >
              Categories
            </Link>
            <Link
              href="#"
              className="text-sm font-medium underline-offset-4 hover:underline"
            >
              Deals
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative hidden w-full max-w-sm items-center sm:flex">
            <Input
              type="search"
              placeholder="Search products..."
              className="pr-10"
            />
            <Button
              size="sm"
              variant="ghost"
              className="absolute right-0 top-0 h-full px-3"
            >
              <span className="sr-only">Search</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.3-4.3" />
              </svg>
            </Button>
          </div>
          {/* Mobile Search */}
          <MobileSearch />
          <Button size="icon" variant="outline" className="relative">
            <ShoppingCart className="h-5 w-5" />
            <span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
              3
            </span>
          </Button>
          {/* Mobile Menu */}
          <MobileMenu />
        </div>
      </div>
    </header>
  );
};
