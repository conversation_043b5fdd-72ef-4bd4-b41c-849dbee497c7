import Link from "next/link";

export const Footer = () => {
  return (
    <footer className="border-t py-4 md:py-6">
      <div className="container mx-auto flex max-w-6xl flex-col items-center justify-between gap-3 px-4 md:flex-row md:gap-4">
        <p className="text-center text-xs text-muted-foreground sm:text-sm md:text-left">
          &copy; {new Date().getFullYear()} ShopMart. All rights reserved.
        </p>
        <nav className="flex gap-4">
          <Link
            href="#"
            className="text-xs text-muted-foreground hover:underline sm:text-sm"
          >
            Terms
          </Link>
          <Link
            href="#"
            className="text-xs text-muted-foreground hover:underline sm:text-sm"
          >
            Privacy
          </Link>
          <Link
            href="#"
            className="text-xs text-muted-foreground hover:underline sm:text-sm"
          >
            Contact
          </Link>
        </nav>
      </div>
    </footer>
  );
};
