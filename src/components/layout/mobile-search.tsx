"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";

export function MobileSearch() {
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button size="icon" variant="ghost" className="sm:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-5 w-5"
          >
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.3-4.3" />
          </svg>
          <span className="sr-only">Search</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="top" className="h-[30%] sm:h-[20%]">
        <SheetHeader className="mb-4">
          <SheetTitle>Search Products</SheetTitle>
        </SheetHeader>
        <div className="flex gap-2">
          <Input
            type="search"
            placeholder="Search products..."
            className="flex-1"
            autoFocus
          />
          <Button type="submit">Search</Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}
