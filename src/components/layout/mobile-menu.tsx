"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";

export function MobileMenu() {
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button size="icon" variant="ghost" className="md:hidden">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-5 w-5"
          >
            <line x1="4" x2="20" y1="12" y2="12" />
            <line x1="4" x2="20" y1="6" y2="6" />
            <line x1="4" x2="20" y1="18" y2="18" />
          </svg>
          <span className="sr-only">Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[80%] sm:w-[350px]">
        <SheetHeader className="mb-6">
          <SheetTitle>Menu</SheetTitle>
        </SheetHeader>
        <div className="mb-6">
          <Input
            type="search"
            placeholder="Search products..."
            className="mb-4"
          />
        </div>
        <nav className="flex flex-col gap-4">
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Home
          </Link>
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Products
          </Link>
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Categories
          </Link>
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Deals
          </Link>
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            My Account
          </Link>
          <Link
            href="#"
            className="text-base font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Cart
          </Link>
        </nav>
      </SheetContent>
    </Sheet>
  );
}
