'use client';

import React, { createContext, useCallback, useEffect, useState } from 'react';
import en from '@/locales/en.json';
import th from '@/locales/th.json';

type Locale = 'en' | 'th';
type Messages = typeof en;

const messages: Record<Locale, Messages> = { en, th };

export const LanguageContext = createContext<{
  locale: Locale;
  t: (key: string, vars?: Record<string, string | number>) => string;
  setLocale: (locale: Locale) => void;
} | null>(null);

export const LanguageProvider = ({
  children,
  initialLocale = 'th',
}: {
  children: React.ReactNode;
  initialLocale?: Locale;
}) => {
  const [locale, setLocale] = useState<Locale | null>(null);

  useEffect(() => {
    const htmlLang = document.documentElement.lang;
    const lang = htmlLang === 'en' || htmlLang === 'th' ? htmlLang : initialLocale;
    setLocale(lang);
  }, [initialLocale]);

  useEffect(() => {
    if (locale) {
      document.documentElement.lang = locale;
    }
  }, [locale]);

  const t = useCallback(
    (key: string, vars?: Record<string, string | number>): string => {
      if (!locale) return key;

      const parts = key.split('.');
      let current: unknown = messages[locale];

      for (const part of parts) {
        if (typeof current === 'object' && current !== null && part in current) {
          current = (current as Record<string, unknown>)[part];
        } else {
          return key;
        }
      }

      if (typeof current === 'string') {
        let str = current;
        if (vars) {
          for (const [k, v] of Object.entries(vars)) {
            const regex = new RegExp(`{${k}}`, 'g');
            str = str.replace(regex, String(v));
          }
        }
        return str;
      }

      return key;
    },
    [locale]
  );

  return (
    <LanguageContext.Provider value={{ locale: locale ?? initialLocale, t, setLocale }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useTranslation = () => {
  const context = React.useContext(LanguageContext);
  if (!context) {
    throw new Error('useTranslation must be used within a LanguageProvider');
  }
  return context;
};
