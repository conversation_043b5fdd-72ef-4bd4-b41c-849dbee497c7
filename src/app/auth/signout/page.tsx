"use client";

import { signOut } from "next-auth/react";
import { useEffect } from "react";

export default function SignOutPage() {
  const logout = async () => {
    await fetch('/api/set-token', {
      method: 'POST',
      credentials: 'include',
      body: JSON.stringify({ token: '' })
    });
    await signOut({ redirectTo: "/" });
  };

  useEffect(() => {
    void logout();
  }, []);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <h1 className="text-2xl font-bold">Signing Out...</h1>
    </div>
  );
}
