export const derivedCallbackUrl = (callbackUrl?: string | null) => {
  if (!callbackUrl) return { token: null, callbackUrl: null };

  const decodedUrl = decodeURIComponent(callbackUrl);

  try {
    const url = new URL(decodedUrl);
    const token = url.searchParams.get("token");
    const langCode = url.searchParams.get("langCode");

    if (token) {
      url.searchParams.delete("token");
    }
    if (langCode) {
      url.searchParams.delete("langCode");
    }

    return { token, langCode, callbackUrl: url.toString() };
  } catch (error) {
    console.error("Invalid callbackUrl:", error);
    return { token: null,langCode: null, callbackUrl: decodedUrl };
  }
};
