'use client';

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from '@/contexts/LanguageContext';
import { api } from "@/trpc/react";


interface TopUpData {
  fromCard: {
    id: string;
    cardNumber: string;
    cardType: string;
    isDefault: boolean;
  };
  toCard: {
    id: string;
    cardNumber: string;
    balance: string;
  };
  amount: string;
}

interface CreditCard {
  cardID: number;
  cardNumber: string;
  cardType: string;
  isDefault: boolean;
}

interface CategoryCard {
  cardNumber: string;
  cardGraphicURL?: string;
  credits?: {
    creditAmount?: string;
  };
}


export default function ConfirmTopUpPage() {
  const { t } = useTranslation();
  const router = useRouter();

  // State management
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [categoryCardGraphicURL, setCategoryCardGraphicURL] = useState<string | null>(null);
  const [categoryCardImgError, setCategoryCardImgError] = useState<boolean>(false);
  
  // รับค่า parameters จาก URL
  const params = useSearchParams();
  const cardID = params.get('cardID');
  const categoryCardNumber = params.get('categoryCardNumber');
  const amount = params.get('amount');
  
  // แสดงค่าใน console.log
  useEffect(() => {
    // ดึงข้อมูลจาก sessionStorage
    try {
      if (amount) {
        sessionStorage.setItem("topupAmount", amount);
      }
      const storedCreditCardList = sessionStorage.getItem('storedCreditCardList');
      const storedCategoryCardList = sessionStorage.getItem('storedCategoryCardList');
      
      if (storedCreditCardList) {
        const creditCardList = JSON.parse(storedCreditCardList) as CreditCard[];
        
        // หาบัตรที่มี cardID ตรงกับที่ส่งมา
        if (cardID) {
          const matchedCard = creditCardList.find((card: CreditCard) => card.cardID.toString() === cardID);
          if (matchedCard) {
            
            // อัปเดต topUpData ด้วยข้อมูลจริง
            setTopUpData(prev => ({
              ...prev,
              fromCard: {
                id: matchedCard.cardID.toString(),
                cardNumber: matchedCard.cardNumber,
                cardType: matchedCard.cardType.toLowerCase(),
                isDefault: matchedCard.isDefault
              },
              // อัปเดตค่า amount จาก URL params
              amount: amount ?? "0"
            }));
          }
        }
      }
      
      if (storedCategoryCardList) {
        const categoryCardList = JSON.parse(storedCategoryCardList) as CategoryCard[];
        
        // หาบัตรที่มี cardNumber ตรงกับที่ส่งมา
        if (categoryCardNumber) {
          const matchedCategoryCard = categoryCardList.find((card: CategoryCard) => card.cardNumber === categoryCardNumber);
          if (matchedCategoryCard) {
            
            // เก็บค่า cardGraphicURL ถ้ามี
            if (matchedCategoryCard.cardGraphicURL) {
              setCategoryCardGraphicURL(matchedCategoryCard.cardGraphicURL);
            }
            
            // อัปเดต topUpData ส่วน toCard
            setTopUpData(prev => ({
              ...prev,
              toCard: {
                id: matchedCategoryCard.cardNumber,
                cardNumber: matchedCategoryCard.cardNumber,
                balance: matchedCategoryCard.credits?.creditAmount?.toString() ?? "0.00"
              }
            }));
          }
        }
      }
    } catch (error) {
      console.error('Error reading from sessionStorage:', error);
    }
  }, [cardID, categoryCardNumber, amount]);

  // Mock top-up data - replace with actual data from previous page
  const [topUpData, setTopUpData] = useState<TopUpData>({
    fromCard: {
      id: '',
      cardNumber: '',
      cardType: '',
      isDefault: false
    },
    toCard: {
      id: '',
      cardNumber: '',
      balance: '0.00'
    },
    amount: '0.00'
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // สร้าง mutation hook สำหรับการเรียกใช้ API
  const topupCardMutation = api.cardtopup.topupCard.useMutation({
    onError: (error) => {
      //console.error('Top-up confirmation error:', error);
      router.push('/something-went-wrong');
    },
    onSuccess: async (data) => {
      // Log ข้อมูลที่ได้รับกลับมา
      //console.log('Topup Response:', data);
      
      // Set token ใหม่
      if (data.newAccessToken) {
        try {
          const res = await fetch('/api/set-token', {
            method: 'POST',
            credentials: 'include',
            body: JSON.stringify({ token: data.newAccessToken }),
          });
          const tokenData = await res.json() as { message: string };
          //console.log('[Set Token Success]', tokenData);
        } catch (err) {
          //console.error('[Set Token Error]', err);
        }
      }

      // ตรวจสอบ code ว่าเป็น "1" หรือไม่
      if (Number(data.data.code) === 1) {
        // ตรวจสอบ authorizeURI และทำการ redirect
        if (data.data.data.authorizeURI) {
          //console.log('Redirecting to authorizeURI:', data.data.data.authorizeURI);
          window.location.href = data.data.data.authorizeURI;
        } else {
          //console.log('No authorizeURI, redirecting to fail page');
         // router.push('/topup-fail-balance-limit');
         router.push('/something-went-wrong');
        }
      } else {
        
        if(Number(data.data.code) === -2191009) {
          // ถ้า code ไม่เท่ากับ "1" ให้ไปที่หน้า topup-fail-balance-limit
          router.push('/topup-fail-balance-limit');
        } else {
          router.push('/something-went-wrong');
        }
        
      }
    }
  });

  const handleConfirm = async () => {
    setIsSubmitting(true);

    const currentUrl = typeof window !== "undefined" ? window.location.href : "/";
    console.log(currentUrl);
    console.log('before set');
    await fetch('/api/set-back-url', {
      method: 'POST',
      credentials: 'include',
      body: JSON.stringify({ backUrl: currentUrl}),
    })
      .then(res => res.json())
      .then(data => {
        console.log('[Set backurl]', data);
      })
      .catch(err => console.error('[Set backurl Error]', err));


    try {
      // เรียกใช้ API cardtopup ที่เราสร้าง
      await topupCardMutation.mutateAsync({
        cardNumber: topUpData.toCard.cardNumber,
        topupAmount: parseFloat(topUpData.amount),
        paymentType: "Omise",
        paymentChannel: "CreditCard",
        creditCardID: parseInt(topUpData.fromCard.id)
      });
    } catch (error) {
      //console.log(error)
      router.push('/something-went-wrong');
      // Error จะถูกจัดการโดย onError callback ใน useMutation
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCardIcon = (cardType: string) => {

    return (
          <div className="w-16 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
            <Image
                src={`/images/credit-card/${(cardType).toLowerCase()}.png`}
                alt={cardType}
                width={40}
                height={40}
                className="rounded" 
              />
          </div>
        );
  };

  const getToCardIcon = () => {
    return (
      <div className="w-16 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
        <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
          <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
        </div>
      </div>
    );
  };

  return (
    <>
      {pageLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
          </div>
          <h1 className="text-lg font-medium mt-1">{t('confirm_topup')}</h1>
        </div>

        <div className="flex flex-col p-6 space-y-8 flex-1 w-full max-w-md mx-auto">
          {/* From Card Section */}
          <div className="flex flex-col items-left space-y-4">
            
            <div className="flex items-center space-x-3">
              {getCardIcon(topUpData.fromCard.cardType)}

              <div>
                <p className="text-gray-500 text-sm m-0">
                  Credit card <span className="font-medium">{topUpData.fromCard.cardNumber}</span>
                </p>

                {topUpData.fromCard.isDefault && (
                  <span className="mt-1 px-3 py-1 bg-teal-100 text-teal-600 rounded-full text-xs font-medium">
                    Default
                  </span>
                )}
              </div>
            </div>


            {/* Arrow Down */}
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center">
              <ArrowDown className="h-6 w-6 text-teal-600" />
            </div>

            {/* To Card Section */}
            <div className="flex flex-col items-left space-y-2">
              <div className="w-16 h-10 flex items-center justify-center">
                {!categoryCardImgError && categoryCardGraphicURL ? (
                  <img 
                    src={categoryCardGraphicURL}
                    alt="Card Category"
                    className="rounded"
                    onError={() => setCategoryCardImgError(true)}
                  />
                ) : (
                  getToCardIcon()
                )}
              </div>
              <div className="text-left">
                <p className="text-gray-500 text-sm">{topUpData.toCard.cardNumber}</p>
                <p className="text-lg font-semibold">
                  {parseFloat(topUpData.toCard.balance).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} <span className="text-sm font-normal text-gray-500">{t('topup_thb')}</span>
                </p>
              </div>
            </div>
          </div>

          {/* Amount Section */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 text-lg">{t('topup_amount')}</span>
              <span className="text-2xl font-semibold">
                {parseFloat(topUpData.amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} <span className="text-lg font-normal text-gray-500">{t('topup_thb')}</span>
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleConfirm}
            className="w-full bg-quaternary text-white py-6 text-lg rounded-lg disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              t('topup_confirm')
            )}
          </Button>
        </footer>
      </div>
    </>
  );
}
