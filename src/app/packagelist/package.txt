{(isLoading || dataJson.length === 0) && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          {/* <div className="text-white text-lg">Loading...</div> */}
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>
            {/* <Image className="animate-bounce" src="/images/animate/mascot-loading.svg" width={100} height={100} alt="Loading animation"></Image>
            <div className="text-white text-lg">Loading...</div> */}
          </div>
        </div>
      )}
      {/* Header */}
      {/* rounded-b-xl */}
      <div style={{ backgroundImage: `url(${bgImage})` }} className="h-40 p-4 text-white bg-cover relative">
        <div className="absolute top-4 left-4">
          <ArrowLeft className="h-6 w-6 mt-2" onClick={handleGoBackClick}/>
        </div>
        <div className="mt-6 ml-4">
          <h1 className="text-2xl font-bold mb-1 mt-14 text-secondary">Coupon Packages</h1>
          <p className="text-sm opacity-90">Buy now and save big on every bite!</p>
        </div>
        <div className="absolute top-16 right-4 bg-white rounded-full">
          <div className="rounded-full">
            <Image src="/images/package/coupon-package.svg" width={60} height={60} alt="Coupon icon"></Image>
          </div>
        </div>
      </div>

      
      {/* Coupon Cards */}
      
      <div className="p-4 space-y-4">
      
      {
        dataJson.map((item, index) => {

          // const [animatedPercentage, setAnimatedPercentage] = useState(0);
          // useEffect(() => {
          //   let start = 0;
          //   const end = item.soldPercentage; // ค่านี้เป็นค่าที่ได้รับจาก mock data
          //   const duration = 1000; // ระยะเวลา animation
          //   const stepTime = 10; // เพิ่มค่าในแต่ละรอบ (1000ms / 10ms = 100 รอบ)
  
          //   const increment = (end - start) / (duration / stepTime); // การคำนวณค่าเพิ่มในแต่ละรอบ
  
          //   const interval = setInterval(() => {
          //     start += increment;
  
          //     if (start >= end) {
          //       start = end;
          //       clearInterval(interval);
          //     }
  
          //     setAnimatedPercentage(Math.floor(start)); // อัพเดทค่า
          //   }, stepTime);
  
          //   return () => clearInterval(interval); // ล้าง interval เมื่อ component ถูก unmount
          // }, [item.soldPercentage]);

          return (

            <Link href={`/packagedetail/${item.packageID}`} className='cursor-pointer' key={item.packageID} onClick={goToPackageDetail}>
              <Card className="overflow-hidden border-0 shadow-xs rounded-lg mb-4 border border-gray-300 bg-gray-100">
                <div className="relative">

                  {
                    item.packageImage ? 
                      <>
                        <img
                        src={item.packageImage}
                        alt="Steak meal"
                        className="w-full h-56 object-fill rounded-lg"/>
                        
                        <div className="absolute bottom-0 left-0">
                          <Image src="/images/package/coupon-icon.svg" width={60} height={60} alt="Coupon icon"></Image>
                        </div>
                      </>
                     :
                    <div className="w-full h-56 bg-gray-300 flex items-center justify-center rounded-lg">
                      <span className="text-gray-600">No Image</span>
                    </div>
                  }
                  
                </div>

                <CardContent className="p-4">                
                  <div className="flex justify-between items-start mb-2">
                    <h2 className="text-lg font-bold text-primary">{item.packageTitle}</h2>
                    <Badge className="bg-orange-700 hover:bg-orange-700 rounded-2xl">
                      <Image src="/images/package/coupon-icon2.svg" width={25} height={25} alt="coupon icon"></Image>
                      &nbsp;X{item.totalCoupon}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    {item.packageDetail.length > 100 ? item.packageDetail.slice(0, 100) + "..." : item.packageDetail}
                  </p>
                  {item.isShowProgressBar && ( 
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-orange-700 font-normal">Going fast, Grab yours now!</span>
                      </div>
                      <div className="relative w-full">
                        <Progress value={item.soldPercentage} className="h-3 bg-gray-300 w-full mt-2" />
                        {item.soldPercentage == 100 ? 
                          <Image className="absolute -top-3 transition-all duration-300"
                            style={{
                              left: `calc(${item.soldPercentage}% - 1.4rem)`
                            }}
                            src="/images/package/mascot.svg" width={35} height={35} alt="coupon icon">
                          </Image>
                        :
                          <Image className="absolute -top-3 transition-all duration-300"
                            style={{
                              left: `calc(${item.soldPercentage}% - 1rem + 0.2rem)`
                            }}
                            src="/images/package/fire-icon.svg" width={22} height={22} alt="fire icon">
                          </Image>
                      }
                        
                      </div>
                    </div>
                  )}
                  
                </CardContent>

                <CardFooter className="flex justify-between items-end">
                {/* items-end */}
                  <div>
                    {
                      item.discountAmount != 0 && (
                        <span className="text-red-700 line-through text-sm block -ml-2">{item.originalPrice.toFixed(2)} THB</span>
                      )
                    }
                    <div className="text-xl font-bold -ml-2 text-quaternary">
                      {item.discountedPrice.toFixed(2)} THB
                    </div>
                  </div>
                  {item.nearlyExpirePeriodAlert > 0 ? (
                    <span className="text-orange-700 font-medium text-sm">Only {item.nearlyExpirePeriodAlert} days left!</span>
                  )
                  : 
                  <div className="text-gray-500 text-sm text-right"> 
                    <span className="text-gray-400">Available until </span>
                    {item.dateActiveEnd}  
                  </div>
                  }
                  
                </CardFooter>
              </Card>
            </Link>
          )
        })
      }
        {/* <Card className="overflow-hidden border-0 shadow-xs rounded-lg">
          <div className="relative">
            <img
              src="https://www.fashionisland.co.th/wp-content/uploads/2023/10/Sizzler-ProOct2023-1.jpg"
              alt="Steak meal"
              className="w-full h-56 object-fill rounded-lg"
            />
            <div className="absolute bottom-0 left-0">
              <img src="/images/package-list/coupon-icon.svg" alt="Coupon icon" className="w-16 h-16"/>
            </div>
          </div>

          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-lg font-bold text-primary">50% Discount for steak lover!</h2>
              <Badge className="bg-orange-500 hover:bg-orange-600"><Ticket className="w-4 h-4" />&nbsp;X5</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Calling all steak enthusiasts! Indulge in your favorite premium cuts at half the price with ou...
            </p>
            
            <div className="flex items-center">
              <span className="text-gray-400 line-through text-sm mr-2">200.00 THB</span>
              <span className="text-xl font-bold text-quaternary">150.00 THB</span>
              <span className="ml-auto text-orange-600 font-medium text-sm">Only 3 days left!</span>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-0 shadow-xs rounded-lg">
          <div className="relative">
            <img
              src="https://www.terminal21.co.th/pattaya/wp-content/uploads/t21-pattaya-sizzler-valentine-share-526x275.jpg"
              alt="Steak meal"
              className="w-full h-56 object-fill rounded-lg"
            />
            <div className="absolute bottom-0 left-0">
              <img src="/images/package-list/coupon-icon.svg" alt="Coupon icon" className="w-16 h-16"/>
            </div>
          </div>

          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-lg font-bold text-primary">50% Discount for steak lover!</h2>
              <Badge className="bg-orange-500 hover:bg-orange-600"><Ticket className="w-4 h-4" />&nbsp;X5</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Calling all steak enthusiasts! Indulge in your favorite premium cuts at half the price with ou...
            </p>
            
            <div className="flex items-center">
              <span className="text-gray-400 line-through text-sm mr-2">200.00 THB</span>
              <span className="text-xl font-bold text-quaternary">150.00 THB</span>
              <span className="ml-auto text-orange-600 font-medium text-sm">Only 3 days left!</span>
            </div>
          </CardContent>
        </Card> */}
      </div>