
'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, PlusCircle, ArrowLeft } from 'lucide-react';
import Image from 'next/image';
// import Link from "next/link";
// import { useTransition } from 'react';
// import { useRouter } from "next/navigation";
import { api } from "@/trpc/react";
import { Input } from '@/components/ui/input';
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession , getSession } from "next-auth/react";
import { handleGoBackToLineLiff } from "@/lib/navigation";

interface CardItem {
    cardID: number;
    cardNumber: string;
    cardType: string;
    expireDate: string;
    isDefault: boolean;
}

export default function CheckoutPayment() 
{
    const { t } = useTranslation();

    const { data: session, status } = useSession();
    const [selectedCard, setSelectedCard] = useState<string>('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [systemBrandCode, setSystemBrandCode] = useState<string>("");
      
    useEffect(() => {
      void (async () => {
          let brandCode = "";

          const session = await getSession();
          brandCode = session?.user.systemBrandCode ?? "";

          if (!brandCode && typeof document !== "undefined") {
              const regex = /(?:^|;\s*)brand=([^;]+)/;
              const match = regex.exec(document.cookie);
              if (match) brandCode = match[1] ?? "";
          }

          setSystemBrandCode(brandCode.toLowerCase());
      })();
  }, []);
      

    const goBackToChekcout = () => {
        window.location.assign("/checkout");
    };

    const goToAddCard = () => {
        window.location.assign("/addcard");
      };

    const handleNext = () => {

        const selectedCardObj = cardList?.cards.find(card => card.cardNumber === selectedCard);
        // if (selectedCardObj?.cardID) {
        setIsSubmitting(true)
        //     updateDefaultCard.mutate({cardID: selectedCardObj.cardID});
        // }
        window.location.assign(`/checkout?cardID=${selectedCardObj?.cardID}&cardNumber=${selectedCardObj?.cardNumber}&cardType=${selectedCardObj?.cardType}&expireDate=${selectedCardObj?.expireDate}&isDefault=${selectedCardObj?.isDefault}`);
    };

    const { data: cardList, isLoading, error } = api.card.getCreditCardList.useQuery();

    // Token update effect
    useEffect(() => {
        if (cardList?.newAccessToken) {
            fetch('/api/set-token', {
                method: 'POST',
                credentials: 'include',
                body: JSON.stringify({ token: cardList.newAccessToken }),
            })
                .then(res => res.json())
                .then(data => {
                    console.log('[Set Token]', data);
                })
                .catch(err => console.error('[Set Token Error]', err));
        }
    }, [cardList?.newAccessToken]);
    
    // Check for missing token in a separate useEffect
    useEffect(() => {
        if (cardList && !cardList.newAccessToken) {
            console.error('No access token received, redirecting to error page');
            window.location.assign('/auth/error');
        }
    }, [cardList]);

    useEffect(() => {
        if (cardList?.cards && cardList.cards.length > 0) {
          sessionStorage.setItem('noFirstCard', '1');
        } else {
          sessionStorage.setItem('noFirstCard', '0');
        }
      }, [cardList]);

    // Debug output
    useEffect(() => {
        if (error) {
            console.error('API Error:', error);
        }
    }, [error]);

    useEffect(() => {
        if (cardList?.cards && cardList.cards.length > 0) {
            // Find the default card
            const defaultCard = cardList.cards.find(card => card.isDefault);
            if (defaultCard) {
                setSelectedCard(defaultCard.cardNumber);
            } else if (cardList.cards[0]) {
                // If no default card, select the first one
                setSelectedCard(cardList.cards[0].cardNumber);
            }
        }
    }, [cardList?.cards]);

    if(isLoading) {
        return (
            <div className="flex flex-col min-h-screen bg-white animate-pulse">
                {/* Header Skeleton */}
                <div className="flex items-center justify-center p-4 relative">
                    <div className="absolute top-4 left-4">
                        <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="h-7 w-36 bg-gray-200 rounded mt-1"></div>
                </div>

                <div className="px-4 mt-6 flex-1 overflow-auto">
                    <div className="space-y-4 mb-14">
                        {/* Card Skeletons */}
                        {[1, 2].map((item) => (
                        <div key={item} className="p-4 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="flex items-center">
                                        <div className="w-10 h-8 bg-gray-200 rounded-md mr-3"></div>
                                        <div className="h-5 w-40 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
                            </div>
                        </div>
                        ))}
                    </div>

                    {/* Add Card Link Skeleton */}
                    <div className="flex items-center py-2">
                        <div className="h-6 w-6 bg-gray-200 rounded-full mr-2"></div>
                        <div className="h-5 w-48 bg-gray-200 rounded"></div>
                        <div className="h-5 w-5 bg-gray-200 rounded-full ml-auto"></div>
                    </div>
                </div>

                {/* Footer Skeleton */}
                <footer className="fixed bottom-0 left-0 w-full bg-white p-4">
                    <div className="h-14 w-full bg-gray-200 rounded-lg"></div>
                </footer>
            </div>
        )
    }
    
    if(error){
        return (  
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src={`/images/package/${systemBrandCode}/mascot-sad.svg`} width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={() => handleGoBackToLineLiff(systemBrandCode)}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <>
            {/* {isLoading && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
                <div className="rounded-full">
                    <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>
                </div>
                </div>
            ) } */}
            <div className="flex flex-col min-h-screen bg-white">
                {/* {(isLoading) && (
                    <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
                        <div className="rounded-full">
                            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation"></Image>
                        </div>
                    </div>
                )} */}
                {/* Header */}
                <div className="flex items-center justify-center p-4 relative text-gray-700">
                    <div className="absolute top-4 left-4">
                        <ArrowLeft className="h-6 w-6 mt-2" onClick={() => goBackToChekcout()} />
                    </div>
                    <h1 className="text-base font-medium mt-1">{t('my_credit_cards')}</h1>
                </div>

                <div className="px-4 mt-6 flex-1 overflow-auto">

                    <div className="space-y-4 mb-14">

                        {/* {cardList.cards.map((card: CardItem, index) => (
                        {card.cardNumber}
                    ))} */}
                        {cardList?.cards && cardList.cards.length > 0 ? (
                            cardList.cards.map((card: CardItem) => (
                                <div
                                    key={card.cardID}
                                    className={`p-4 rounded-lg border ${selectedCard === card.cardNumber ? 'border-accent-2' : 'border-gray-200'}`}
                                >
                                    <label className="flex items-center justify-between cursor-pointer">
                                        <div className="flex items-center space-x-3">
                                            <div className="flex items-center">
                                                <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                                                    <Image
                                                        src={`/images/credit-card/${card.cardType.toLowerCase()}.png`}
                                                        alt={card.cardType}
                                                        width={40}
                                                        height={40}
                                                        className="rounded"
                                                    />
                                                </div>
                                                <span className="text-sm">{card.cardNumber}</span>
                                                {card.isDefault && (
                                                    <span className="ml-3 px-3 py-1 text-alert-label bg-alert-label-background rounded-full text-xs">
                                                        {t('default_card')}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <Input
                                            type="radio"
                                            name="card-selection"
                                            value={card.cardNumber}
                                            checked={selectedCard === card.cardNumber}
                                            onChange={(e) => setSelectedCard(e.target.value)}
                                            className="h-6 w-6 cursor-pointer accent-quaternary"
                                            disabled={isSubmitting}
                                        />
                                    </label>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                {/* No cards found. Add a card to get started. */}
                            </div>
                        )}
                    </div>

                    {/* <Button variant="outline" className="w-full justify-start text-gray-600 mb-6"> */}

                    {/* </Button> */}
                    {!isLoading && cardList?.cards && cardList.cards.length < 3 && (
                        <div onClick={goToAddCard} className="flex items-center py-2 hover:bg-gray-100 rounded-md transition-colors text-sm">
                            <PlusCircle className="h-6 w-6 mr-2 text-accent-2" />
                            {t('add_credit_debit_card')}
                            <ChevronLeft className="h-5 w-5 ml-auto rotate-180" />
                        </div>
                    )}

                </div>


                <footer className="fixed bottom-0 left-0 w-full bg-white p-4 flex justify-between items-center">
                    {/* <Button
                        className="w-full bg-[#81C784] hover:bg-[#66BB6A] text-white py-6 text-lg rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!selectedCard || selectedCard === ''}
                        onClick={() => handleNext()}
                    >
                        Next
                    </Button> */}
                    <Button
                        className="w-full bg-tertiary text-white py-6 text-lg rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!selectedCard || selectedCard === '' || isSubmitting}
                        onClick={() => handleNext()}
                    >
                        {isSubmitting ? (
                            <div className="flex items-center justify-center">
                                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
                            </div>
                        ) : (
                            t('next')
                        )}
                    </Button>
                </footer>
            </div>
        </>
    );
}
