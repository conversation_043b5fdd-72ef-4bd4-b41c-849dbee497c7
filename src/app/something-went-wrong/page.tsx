'use client';

import React, {useEffect, useState} from 'react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession, getSession } from "next-auth/react";
import { handleGoBackToLineLiff } from "@/lib/navigation";

// import { CircleCheck } from 'lucide-react';


export default function SomethingWentWrongPage() {
    const { t } = useTranslation();
    const { data: session, status } = useSession();
   const [systemBrandCode, setSystemBrandCode] = useState<string>("");
     
    useEffect(() => {
      void (async () => {
          let brandCode = "";

          const session = await getSession();
          brandCode = session?.user.systemBrandCode ?? "";

          if (!brandCode && typeof document !== "undefined") {
              const regex = /(?:^|;\s*)brand=([^;]+)/;
              const match = regex.exec(document.cookie);
              if (match) brandCode = match[1] ?? "";
          }

          setSystemBrandCode(brandCode.toLowerCase());
      })();
  }, []);

    
    return (
        <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4">
            {/* Content at the top and center */}
            <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
                <div className="flex items-center justify-center mb-10">
                    {/* className='animate-bounce' */}
                    <Image src={`/images/package/${systemBrandCode}/mascot-sad.svg`} width={100} height={100} alt="Loading animation"></Image>
                </div>

                <div className="text-center space-y-4 mb-8">
                    <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>
                    <div className="space-y-1">
                        <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                    </div>
                </div>

                {/* Buttons fixed at the bottom */}

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={() => handleGoBackToLineLiff(systemBrandCode)}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        </div>
    );
};
