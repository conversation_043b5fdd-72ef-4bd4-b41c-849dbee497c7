'use client';


import React, { useEffect, useState } from 'react';
import { useTranslation } from '@/contexts/LanguageContext';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";

export default function TopupSuccessPage() {
    const { t } = useTranslation();
    const { data: session, status } = useSession();
    const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";
    
    const params = useSearchParams();
    // const amount = params.get("amount");
    // const datetime = params.get("datetime") ?? new Date().toISOString();

    const [amount, setAmount] = useState<string>("0.00");
    const [datetime, setDatetime] = useState<string>(new Date().toISOString());

    useEffect(() => {
        const storedAmount = sessionStorage.getItem("topupAmount");
        if (storedAmount) {
            setAmount(storedAmount);
        }

        const now = new Date().toISOString();
        // sessionStorage.setItem("globalDatetime", now);
        setDatetime(now);
    }, []);

    const handleGoBackToWebApp = () => {

        if(systemBrandCode == "tcc") {
            // const message = "openPage:MyWallet";
            const message = {
                "method": "close",
                "route" : "MyWallet"
            }

            const userAgent = navigator.userAgent;
        
            if (/android/i.test(userAgent)) {
                window.tccObserver?.postMessage(JSON.stringify(message));
            } else if (/iPad|iPhone|iPod/.test(userAgent)) {
                window.webkit?.messageHandlers?.tccObserver?.postMessage(JSON.stringify(message));
            }

        } else {
            // window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
        }
        
    };
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);

        // ตรวจสอบว่าเป็น valid date หรือไม่
        if (isNaN(date.getTime())) {
            return "";
        }

        const year = date.getFullYear();
        const month = `${date.getMonth() + 1}`.padStart(2, "0");
        const day = `${date.getDate()}`.padStart(2, "0");
        const hours = `${date.getHours()}`.padStart(2, "0");
        const minutes = `${date.getMinutes()}`.padStart(2, "0");
        
        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };
    if (status !== "loading") {
        return (
            <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4">
                {/* Content at the top and center */}
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-6">
                        <Image src="/images/tcc/success-icon.svg" width={82} height={82} alt="Icon success"></Image>
                    </div>
                    <div className="text-center space-y-4">
                    <h2 className="text-2xl font-semibold text-gray-800">
                        {t('success')}
                    </h2>
                    <div className="space-y-1">
                        <p className="text-gray-400">
                        {t('successfully_top_up')}
                        </p>
                        <p className="text-gray-400 text-sm">
                            {formatDate(datetime)}
                        </p>
                    </div>
                    <h1 className="text-3xl font-semibold text-gray-800">
                        {amount} {t('thb')}
                    </h1>
                    </div>
                </div>
                {/* Buttons fixed at the bottom */}
                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-color text-white text-base font-semibold py-6 rounded-md" onClick={handleGoBackToWebApp}>
                        {t('done')}
                    </Button>
                </div>
            </div>
        );
    }
    
}
