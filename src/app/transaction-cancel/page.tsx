'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession, getSession } from "next-auth/react";
import Cookies from 'js-cookie';
import { handleGoBackToLineLiff } from "@/lib/navigation";

export default function PaymentSuccessPage() {
  const { t } = useTranslation();
  const { data: session } = useSession();
  
  // State for system brand code
  const [systemBrandCode, setSystemBrandCode] = useState<string>("");

  useEffect(() => {
    void (async () => {
      let brandCode = session?.user.systemBrandCode ?? "";

      if (!brandCode && typeof document !== "undefined") {
        const regex = /(?:^|;\s*)brand=([^;]+)/;
        const match = regex.exec(document.cookie);
        if (match) brandCode = match[1] ?? "";
      }

      setSystemBrandCode(brandCode.toLowerCase());
    })();
  }, [session]);

  // Handle Try Again button
  const handleTryAgain = () => {
    const encodedUrl = Cookies.get('backUrl');
    const backUrl = encodedUrl ? atob(encodedUrl) : '/';
    if (backUrl) {
      window.location.href = backUrl;
    } else {
      window.close(); // fallback
    }
  };

  return (
    <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4 w-full max-w-md mx-auto">
      {/* Content at the top and center */}
      <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
        <div className="flex items-center justify-center mb-10">
          <Image
            src={`/images/package/${systemBrandCode || 'default'}/mascot-sad.svg`}
            width={100}
            height={100}
            alt="Loading animation"
          />
        </div>

        <div className="text-center space-y-4 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800">{t('transaction_canceled')}</h2>
          <div className="space-y-1">
            <p className="text-gray-400">{t('transaction_canceled_description')}</p>
          </div>
        </div>
      </div>

      {/* Buttons fixed at the bottom */}
      <div className="w-full max-w-md space-y-4 mb-14">
        <Button
          className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md"
          onClick={handleTryAgain}
        >
          {t('try_again')}
        </Button>

        <Button
          variant="outline"
          className="w-full text-gray-500 py-6 border border-gray-300 rounded-md"
          
          onClick={() => {
              handleGoBackToLineLiff(systemBrandCode);
            }}
        >
          {t('done')}
        </Button>
      </div>
    </div>
  );
}
