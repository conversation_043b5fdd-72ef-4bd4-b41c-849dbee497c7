'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from "@/components/ui/label";
import { ArrowLeft, Trash } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from "next/navigation";
import * as Switch from '@radix-ui/react-switch';
import { api } from "@/trpc/react";
import { useTranslation } from '@/contexts/LanguageContext';
import Toast from '@/components/ui/toast';
import { useErrorModal } from "@/components/ErrorModalContext";

interface CreditCard {
    cardID: number;
    cardNumber: string;
    cardType: string;
    expireDate: string;
    isDefault: boolean;
}


export default function MyCreditCardsPage() {
    const { t } = useTranslation();
    const router = useRouter();
    const [pageLoading, setPageLoading] = useState(true);
    const [isDefaultCardToggling, setIsDefaultCardToggling] = useState(false);
    
    const [cardData, setCardData] = useState<CreditCard>();
    const [toast, setToast] = useState<{ message: string; type?: 'success' | 'error' | 'default'} | null>(null);
    const { showError } = useErrorModal();

    
    useEffect(() => {
        const storedData = sessionStorage.getItem('cardData');
        if (storedData) {
            setCardData(JSON.parse(storedData) as CreditCard) ;
        }
    }, []);

    // console.log(cardData);

    useEffect(() => {
        const timer = setTimeout(() => {
            setPageLoading(false);
        }, 500);
        return () => clearTimeout(timer);
    }, []);

    // const goBackToMyCardList = () => {
    //     window.location.assign("/mycardlist");
    // };


    const handleDefaultCardToggle = async (checked: boolean) => {
        // setIsDefaultCardToggling(true);
        setPageLoading(true);
        try {
            // อัปเดต state ใน local ก่อน
            setCardData(prev => prev ? { ...prev, isDefault: checked } : prev);

            // ยิง API ไปอัปเดต backend
            await updateDefaultCard.mutateAsync({ cardID: cardData?.cardID ?? 0 });

            // ถ้าอยาก delay ให้ UI smooth
            await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
            console.error("Error updating default card:", error);
            // setIsDefaultCardToggling(false);
            setPageLoading(false);
        }
    };

    const handleConfirmDeleteCard = () => {
        showError({
            title: "Are you sure you want to delete your default card?",
            description: "Once removed, the first available card on your list will become your new default.",
            showRedirectButton: true,
            redirectButtonText: "Delete",
            // redirectTo: null,
            onConfirm: handleDeleteCard,
            cancelButtonText: "Cancel",
            type: "warning"
        });
    };

    const handleDeleteCard = () => {
        deleteDefaultCard.mutate({cardID: cardData?.cardID ?? 0});
    };

    const updateDefaultCard = api.card.updateDefaultCreditCard.useMutation({
        onSuccess: async (data) => {
            try {

            if (data.newAccessToken) {
                const res = await fetch('/api/set-token', {
                    method: 'POST',
                    credentials: 'include',
                    body: JSON.stringify({ token: data.newAccessToken }),
                });
                // console.log('Resp::',data.data.code);
                const result = await res.json() as { message: string };;
                console.log('[Set Token]', result);
                setPageLoading(false);
                if(data.data.code === 1) {    
                    setToast({ message: 'Updated Change', type: 'default' });
                } else {
                    setToast({ message: 'Update failed.', type: 'error' });
                    // setIsDefaultCardToggling(false);
                }             
            }
            } catch (err) {
                console.error('[Set Token Error]', err);
                setToast({ message: 'Failed to update token', type: 'error' });
            }
        },
        onError: (error) => {
            console.error('[Update Default Card Error]', error);
            // setIsDefaultCardToggling(false);
            setPageLoading(false);
            setToast({ message: 'Update failed. Please try again.', type: 'error' });
        },
    });


    const deleteDefaultCard = api.card.deleteCreditCard.useMutation({
        
        onSuccess: async (data) => {
            setPageLoading(true);
            // console.log(data);
            // console.log(data.newAccessToken);
            if(data.newAccessToken){
                await fetch('/api/set-token', {
                    method: 'POST',
                    credentials: 'include',
                    body: JSON.stringify({ token: data.newAccessToken }),
                })
                .then(res => res.json())
                .then(data => {
                    // console.log("Delete success");
                    // setPageLoading(false);
                    window.location.assign("/creditcard-setting");
                    // setToast({ message: 'Delete success', type: 'default' });
                    console.log('[Set Token DDD]', data);
                })
                .catch(err => {
                    // setIsSubmitting(false);
                    console.error('[Set Token Error]', err);
                });
            }
            // router.push("/checkout");
        }
    });
    
    // const { data: cardList, isLoading, error } = api.card.getCreditCardList.useQuery();

    // const isCardExpired = (expireDate: string) => {
    //     const [day, month, year] = expireDate.split('/');
    //     const cardExpiry = new Date(parseInt(`20${year}`), parseInt(month) - 1, parseInt(day));
    //     return cardExpiry < new Date();
    // };

    return (
        <>
            {pageLoading && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
                    <div className="rounded-full">
                        <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
                    </div>
                </div>
            )}

            <div className="flex flex-col min-h-screen bg-white w-full max-w-md mx-auto">
                {/* Header */}
                <div className="bg-white flex items-center justify-center p-4 relative text-gray-700 border-b border-gray-200">
                    <div className="absolute top-4 left-4">
                        <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
                    </div>
                    <h1 className="text-lg font-medium mt-1">My credit cards</h1>
                </div>

                <div className="flex flex-col p-6 space-y-6 flex-1">
                    {/* Credit Card Display */}
                    <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                        <div className="space-y-4">
                            {/* Card Number Section */}
                            <div>
                                <h3 className="text-teal-500 text-sm font-medium mb-2">Credit card number</h3>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-400 text-base tracking-wider">
                                        {cardData?.cardNumber}
                                    </span>
                                    <div className="text-gray-400 font-bold text-lg">
                                        {/* {getCardTypeIcon(creditCard.cardType)} */}
                                        {/* <div className="absolute right-3 top-1/2 transform -translate-y-1/2"> */}
                                            <Image
                                            src={`/images/credit-card/${(cardData?.cardType ?? '').toLowerCase()}.png`}
                                            alt={cardData?.cardType ?? ''}
                                            width={40}
                                            height={40}
                                            className="rounded" 
                                            />
                                        {/* </div> */}
                                    </div>
                                </div>
                            </div>

                            {/* Expiry Date Section */}
                            <div>
                                <h3 className="text-teal-500 text-sm font-medium mb-2">Expiry date</h3>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-400 text-base">
                                        {cardData?.expireDate}
                                    </span>
                                    {/* {isCardExpired(cardData.expireDate) && (
                                        <div className="flex items-center space-x-1">
                                            <span className="text-red-500 text-sm font-medium">Expired</span>
                                            <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                                <span className="text-white text-xs font-bold">!</span>
                                            </div>
                                        </div>
                                    )} */}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Manage Card Section */}
                    <div className="space-y-6">
                        {/* <h2 className="text-lg font-medium text-gray-800">Manage card</h2> */}
                        
                        {/* Set as Default Card */}
                        {/* <div className="bg-white rounded-lg p-4 border border-gray-200"> */}             
                            <>
                                <div className="flex items-center justify-between pt-4">
                                <Label htmlFor="isDefaultCard" className="text-gray-700 text-sm font-medium">
                                    {t('set_as_default_card')}
                                </Label>
                                <Switch.Root
                                    className="w-16 h-9 bg-gray-300 rounded-full text-white relative data-[state=checked]:bg-[#6FCF97]"
                                    checked={cardData?.isDefault}
                                    onCheckedChange={handleDefaultCardToggle}
                                    disabled={isDefaultCardToggling || cardData?.isDefault}
                                >
                                    <Switch.Thumb className="block w-7 h-7 bg-white rounded-full transition-transform translate-x-1 data-[state=checked]:translate-x-8 data-[disabled]:opacity-40" />
                                </Switch.Root>
                                </div>

                                <hr className="border-t border-gray-300" />
                                <div className="flex items-center space-x-2 text-red-500" onClick={handleConfirmDeleteCard}>
                                    <Trash className="w-5 h-5" />
                                    <Label className="cursor-pointer">Delete Card</Label>
                                </div>
                                <hr className="border-t border-gray-300 " />
                            </>
                        {/* </div> */}

                        {/* Delete Card */}
                        
                        
                    </div>                   
                </div>

                {/* 
                <footer className="fixed bottom-0 left-0 w-full bg-white p-4 flex justify-between items-center">
                    <Button className="w-full bg-white text-red-600 border border-gray-200 py-6 text-lg rounded-lg disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleDeleteCard}>Delete Card</Button>
                </footer> */}
            </div>

            <div className="p-6">
                {toast && (
                    <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={() => setToast(null)}
                    />
                )}
            </div>
            
        </>
    );
}
