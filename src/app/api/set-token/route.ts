// import { NextResponse } from 'next/server';

// const COOKIE_NAME = 'accessToken';
// const ONE_DAY_IN_SECONDS = 60 * 60 * 24; // 1 Day

// export async function POST(req: Request) {
//   try {
//     const { token: newAccessToken } = await req.json();
//     const response = NextResponse.json({
//       message: "Token set successfully",
//       status: "success"
//     });

//     if (!newAccessToken) {
//       clearAccessTokenCookie(response);
//       return response;
//     }

//     setAccessTokenCookie(response, newAccessToken);
//     return response;

//   } catch (error) {
//     console.error('Error setting token:', error);
//     return NextResponse.json({ error: "Failed to set token" }, { status: 500 });
//   }
// }

// function setAccessTokenCookie(response: NextResponse, token: string) {
//   response.cookies.set({
//     name: COOKIE_NAME,
//     value: token,
//     path: '/',
//     httpOnly: true,
//     maxAge: ONE_DAY_IN_SECONDS,
//     sameSite: 'strict',
//     secure: true,
//   });
// }

// function clearAccessTokenCookie(response: NextResponse) {
//   response.cookies.set({
//     name: COOKIE_NAME,
//     value: '',
//     path: '/',
//     httpOnly: true,
//     maxAge: 0,
//     sameSite: 'none',
//     secure: true,
//   });
// }
import { z } from "zod";
import { NextResponse } from 'next/server';

const COOKIE_NAME = 'accessToken';
const ONE_DAY_IN_SECONDS = 60 * 60 * 24; // 1 Day

const TokenSchema = z.object({
  token: z.string().nullable(),
});

export async function POST(req: Request) {
  try {
    // const body = await req.json();
    // const { token: newAccessToken } = TokenSchema.parse(body);

    const body = TokenSchema.parse(await req.json());
    const newAccessToken = body.token;

    const response = NextResponse.json({
      message: "Token set successfully"
    });

    if (!newAccessToken) {
      clearAccessTokenCookie(response);
      return response;
    }

    setAccessTokenCookie(response, newAccessToken);
    return response;

  } catch (error) {
    console.error('Error setting token:', error);
    return NextResponse.json({ error: "Failed to set token" }, { status: 500 });
  }
}

function setAccessTokenCookie(response: NextResponse, token: string) {
  response.cookies.set({
    name: COOKIE_NAME,
    value: token,
    path: '/',
    httpOnly: true,
    maxAge: ONE_DAY_IN_SECONDS,
    sameSite: 'strict',
    secure: true,
  });
}

function clearAccessTokenCookie(response: NextResponse) {
  response.cookies.set({
    name: COOKIE_NAME,
    value: '',
    path: '/',
    httpOnly: true,
    maxAge: 0,
    sameSite: 'strict',
    secure: true,
  });
}
