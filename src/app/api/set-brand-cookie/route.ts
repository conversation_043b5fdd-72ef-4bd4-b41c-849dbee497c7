import { z } from "zod";
import { NextResponse } from "next/server";


const COOKIE_NAME = 'brand';
const ONE_DAY_IN_SECONDS = 60 * 60 * 24;

const TokenSchema = z.object({
  brand: z.string().nullable(),
});

export async function POST(req: Request) {
  try{
    const body = TokenSchema.parse(await req.json());
    

    const response = NextResponse.json({ success: true });
    const brandCode = body.brand ?? "";
    setBrandCookie(response, brandCode);
  
    return response;

  } catch (error) {
    console.error('Error setting brand:', error);
    return NextResponse.json({ error: "Failed to set brand" }, { status: 500 });
  }
   
}

function setBrandCookie(response: NextResponse, brand: string) {
  response.cookies.set({
    name: COOKIE_NAME,
    value: brand,
    path: '/',
    httpOnly: true,
    maxAge: ONE_DAY_IN_SECONDS,
    sameSite: 'strict',
    secure: true,
  });
}