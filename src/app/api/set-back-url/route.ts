import { z } from "zod";
import { NextResponse } from "next/server";

const COOKIE_NAME = 'backUrl';
const ONE_DAY_IN_SECONDS = 60 * 60 * 24;

const BackUrlSchema = z.object({
  backUrl: z.string().nullable(),
});

export async function POST(req: Request) {
  try {
    const body = BackUrlSchema.parse(await req.json());
    const backUrl = body.backUrl ?? "/";

    // Base64 encode to safely store in cookie
    const encodedUrl = Buffer.from(backUrl).toString('base64');

    const response = NextResponse.json({ success: true });
    response.cookies.set({
      name: COOKIE_NAME,
      value: encodedUrl,
      path: '/',
      httpOnly: false,
      maxAge: ONE_DAY_IN_SECONDS,
      sameSite: 'strict',
      secure: true,
    });

    return response;
  } catch (error) {
    console.error('Error setting backUrl:', error);
    return NextResponse.json({ error: "Failed to set backUrl" }, { status: 500 });
  }
}
