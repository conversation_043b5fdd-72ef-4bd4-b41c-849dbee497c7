'use client';

import { api } from "@/trpc/react";
import { useRouter, useParams } from "next/navigation";
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useEffect,useState } from "react";
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession, getSession } from "next-auth/react";
import { handleGoBackToLineLiff } from "@/lib/navigation";

export default function CheckChargeStatusPage() {

    const { t } = useTranslation();
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { data: chargeData, isLoading, error } = api.checkcharge.getChargeStatus.useQuery({id});
    const { data: session, status } = useSession();
    
    const [systemBrandCode, setSystemBrandCode] = useState<string>("");
    
    useEffect(() => {
      void (async () => {
          let brandCode = "";

          const session = await getSession();
          brandCode = session?.user.systemBrandCode ?? "";

          if (!brandCode && typeof document !== "undefined") {
              const regex = /(?:^|;\s*)brand=([^;]+)/;
              const match = regex.exec(document.cookie);
              if (match) brandCode = match[1] ?? "";
          }

          setSystemBrandCode(brandCode.toLowerCase());
      })();
    }, []);

    useEffect(() => {
        const handleChargeStatus = async () => {
            if (!chargeData) return;

            try {
                if (chargeData.newAccessToken) {
                    const res = await fetch('/api/set-token', {
                        method: 'POST',
                        credentials: 'include',
                        body: JSON.stringify({ token: chargeData.newAccessToken }),
                    });

                    const result = await res.json() as { message: string };
                    console.log('[Set Token]', result);
                }

                if (chargeData.data.code === 1) {
                    console.log('Success:', chargeData.data.data.requestType);

                    if (chargeData.data.data.requestType === "CaptureCharge") {
                        if(systemBrandCode === "tcc") {
                            window.location.assign('/creditcard-setting');
                        } else {
                            window.location.assign('/mycard');
                        }
                        
                    } else if(chargeData.data.data.requestType === "CheckCharge:OrderCardTopup"){
                        //console.log(chargeData.data);
                        window.location.assign('/topup-success');
                    } else {
                        window.location.assign('/payment-success');
                    }
                } else if(Number(chargeData.data.code) === -300060 || Number(chargeData.data.code) === -9001007) {
                    window.location.assign('/transaction-cancel');
                } else {
                    router.push('/something-went-wrong');
                }

            } catch (err) {
                console.error('[Handle Charge Status Error]', err);
                //window.location.assign('/transaction-cancel');
                router.push('/something-went-wrong');
            }
        };

        void handleChargeStatus();

    }, [chargeData]);

    // Check for missing token in a separate useEffect
    useEffect(() => {
        if (chargeData && !chargeData.newAccessToken) {
            console.error('No access token received, redirecting to error page');
            window.location.assign('/auth/error');
        }
    }, [chargeData]);

    if (isLoading) return <div>Loading...</div>;
    
    if(error){
        return (  
            <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
                <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
                    <div className="flex items-center justify-center mb-10">
                        <Image src="/images/package/mascot-sad.svg" width={100} height={100} alt="Loading animation"></Image>
                    </div>

                    <div className="text-center space-y-4 mb-8">
                        <h2 className="text-xl font-semibold text-gray-800">{t('something_went_wrong')}</h2>

                        <div className="space-y-1">
                            <p className="text-gray-400">{t('this_page_is_currently_not_available')}</p>
                            {/* <p className="text-gray-400">please try again</p> */}
                        </div>
                    </div>
                </div>

                <div className="w-full max-w-md space-y-4 mb-8">
                    <Button className="w-full bg-quaternary hover:bg-quaternary text-white py-6 rounded-md" onClick={() => handleGoBackToLineLiff(systemBrandCode)}>
                        Go back to home page
                    </Button>
                </div>
            </div>
        )
    }
    
    return ""
}
