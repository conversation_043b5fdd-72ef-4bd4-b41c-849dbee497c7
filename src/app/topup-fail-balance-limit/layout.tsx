// import { Footer } from "@/components/layout/footer";
// import { Header } from "@/components/layout/header";
import { ProtectedRoute } from "@/components/protected-route";

export default function TopupSuccessLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <ProtectedRoute>
      <div className="flex min-h-dvh flex-col ">
        {/* <Header /> */}
        {children}
        {/* <Footer /> */}
      </div>
    </ProtectedRoute>
  );
}
