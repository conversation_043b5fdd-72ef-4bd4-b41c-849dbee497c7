'use client';

import { useEffect, useState } from "react";
import React from 'react';
import { useTranslation } from '@/contexts/LanguageContext';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useSession } from "next-auth/react";

export default function TopupSuccessPage() {
    const { t } = useTranslation();
    
    const [cardNumber, setCardNumber] = useState<string | null>(null);
    
    const { data: session, status } = useSession();
    const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";

    useEffect(() => {
        const cardNumberTopUp = sessionStorage.getItem('cardNumberTopUp');
        setCardNumber(cardNumberTopUp);
    }, []);

    const handleGoBackToTopUp = () => {
        window.location.assign(`/topup-creditcard?cardnumber=${cardNumber}`);
    }

    const handleGoBackToWebApp = () => {

        if(systemBrandCode == "tcc") {
            // const message = "openPage:MyWallet";
            const message = {
                "method": "close",
                "route" : "MyWallet"
            }

            const userAgent = navigator.userAgent;
        
            if (/android/i.test(userAgent)) {
                window.tccObserver?.postMessage(JSON.stringify(message));
            } else if (/iPad|iPhone|iPod/.test(userAgent)) {
                window.webkit?.messageHandlers?.tccObserver?.postMessage(JSON.stringify(message));
            }

        } else {
            // window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
        }
    
    };

  return (
    <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4">
        {/* Content at the top and center */}
        <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md">
            <div className="flex items-center justify-center mb-6">
                <Image src="/images/tcc/error-icon.svg" width={82} height={82} alt="Icon success"></Image>
            </div>
            <div className="text-center space-y-4">
                <h2 className="text-2xl font-semibold text-gray-800">
                    {t('balance_limit_reached')}
                </h2>
                <div className="space-y-1">
                    <p className="text-gray-400">
                        {t('balance_limit_sub_title_1')}
                    </p>
                    <p className="text-gray-400">
                        {t('balance_limit_sub_title_2')}
                    </p>
                </div>
            </div>
        </div>
        {/* Buttons fixed at the bottom */}
        <div className="w-full max-w-md space-y-4 mb-8">
            <Button className="w-full bg-quaternary hover:bg-color text-white text-base font-semibold py-6 rounded-md" onClick={handleGoBackToTopUp}>
                {t('try_again')}
            </Button>

            <Button variant="outline" className="w-full text-gray-500 text-base font-semibold py-6 border border-gray-300 rounded-md" onClick={handleGoBackToWebApp}>
                {t('done')}
            </Button>
        </div>
    </div>
  );
}
