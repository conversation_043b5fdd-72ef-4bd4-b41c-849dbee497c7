import "@/styles/globals.css";

import { GeistSans } from "geist/font/sans";
import { type Metadata } from "next";

import { TRPCReactProvider } from "@/trpc/react";
import { auth } from "@/server/auth";
import { SessionProvider } from "next-auth/react";
import { cn } from "@/lib/utils";
import { ErrorModalProvider } from "@/components/ErrorModalContext";
import { LanguageProvider } from "@/contexts/LanguageContext";

// export const metadata: Metadata = {
//   title: "Minor",
//   description: "",
//   icons: [{ rel: "icon", url: "/favicon.ico" }],
// };
export async function generateMetadata(): Promise<Metadata> 
{
  const session = await auth();
  const brandCode = session?.user.systemBrandCode;
  
  let title = "Minor";
  
  if (brandCode === "SZ") {
    title = "Sizzler";
  } else if (brandCode === "DQ") {
    title = "Dairy Queen";
  } else if (brandCode === "TCC") {
    title = "The Coffee Club";
  }
  
  return {
    title: title,
    description: "",
    icons: [{ rel: "icon", url: "/favicon.ico" }],
  };
}

async function getTheme(systemBrandCode?: string) 
{
  // let theme = systemBrandCode === "SZ" ? "theme-red" : "theme-blue";
  const sysCode = systemBrandCode;
  let theme = '';
  if(sysCode === "SZ"){
    theme = "theme-sz";
  } else if(sysCode === "DQ"){
    theme = "theme-dq";
  } else if(sysCode === "TCC"){
    theme = "theme-tcc";
  } else {
    theme = ""
  }
  return theme;
}

async function getLangugageCode(languageCode?: string) 
{
  const langCode = languageCode;
  let language = '';
  language =  langCode == "en" ? "en" : "th";
  return language;
}

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await auth();
  const theme = await getTheme(session?.user.systemBrandCode);
  const language = await getLangugageCode(session?.user.contentLanguage);
  const lang = language === "th" ? "th" : "en";

  return (
    <html lang={lang} className={cn(GeistSans.variable, theme)}>
      <body>
        <SessionProvider session={session}>
          <TRPCReactProvider>
            <LanguageProvider initialLocale={lang}>
              <ErrorModalProvider>
                {children}
              </ErrorModalProvider>
            </LanguageProvider>
            {/* <ProtectedRoute>
              <div className="flex min-h-dvh flex-col">
                {children}
              </div>
            </ProtectedRoute> */}
          </TRPCReactProvider>
        </SessionProvider>
      </body>
    </html>
  );
}


