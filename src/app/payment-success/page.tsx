'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useTranslation } from '@/contexts/LanguageContext';
import { useSession } from "next-auth/react";

// import { CircleCheck } from 'lucide-react';


const goToPackageList = () => {
  window.location.assign('/packagelist');
};

export default function PaymentSuccessPage () {
  const { t } = useTranslation();
  const { data: session, status } = useSession();
  console.log(status);
  const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";

  const goToMyCouponLineLiff = () => {

    if(systemBrandCode == "tcc") {
        // const message = "openPage:MyCouponPackage";
        const message = {
          "method": "close", // action ที่จะทำ
          "route" : "MyCouponPackage" // navigate ไปหน้าไหน
        }

        const userAgent = navigator.userAgent;
      
        if (/android/i.test(userAgent)) {
          window.tccObserver?.postMessage(JSON.stringify(message));
        } else if (/iPad|iPhone|iPod/.test(userAgent)) {
            window.webkit?.messageHandlers?.tccObserver?.postMessage(JSON.stringify(message));
        }

      } else {
        window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_COUPON_LIFF_URL ?? '/');
      }
  };

  return (
    <div className="flex flex-col items-center justify-between min-h-screen bg-white p-4">
      {/* Content at the top and center */}
      <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md pt-16">
        <div className="flex items-center justify-center mb-10">
        {/* className='animate-bounce' */}
            <Image src={`/images/package/${systemBrandCode}/mascot-success.svg`} width={100} height={100} alt="Loading animation"></Image>
        </div>
        
        <div className="text-center space-y-4 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800">
            {t('successfully_paid')}
            </h2>
          
          <div className="space-y-1">
            {/* <p className="text-gray-400">You can now enjoy all the discount!</p> */}
            <p className="text-gray-400">
              {/* To view purchased coupon packages,<br />
              please check &apos;My Coupon&apos;. */}
              {t('to_view_purchased_coupon_packages')}
            </p>
          </div>
        </div>
      </div>
      
      {/* Buttons fixed at the bottom */}
      <div className="w-full max-w-md space-y-4 mb-14">
        <Button className="w-full bg-quaternary hover:bg-color text-white py-6 rounded-md" onClick={goToMyCouponLineLiff}>
          {t('view_my_package')}
        </Button>
        
        <Button variant="outline" className="w-full text-gray-500 py-6 border border-gray-300 rounded-md" onClick={goToPackageList}>
          {t('browse_more')}
        </Button>
      </div>
    </div>
  );
};