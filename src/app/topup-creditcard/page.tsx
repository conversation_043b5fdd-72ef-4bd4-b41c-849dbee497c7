'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft, ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from '@/contexts/LanguageContext';
import { useSearchParams } from 'next/navigation';
import { api } from "@/trpc/react";
import { useErrorModal } from "@/components/ErrorModalContext";
import { useSession } from "next-auth/react";

interface TopUpFormData {
  amount: string;
  customAmount: string;
}

// เพิ่ม interface สำหรับ API responses
interface CreditCard {
  cardID: number;
  cardNumber: string;
  cardType: string;
  isDefault: boolean;
}

interface CardListResponse {
  cards: CreditCard[];
  newAccessToken?: string;
}

// เพิ่ม interface สำหรับ card ที่ได้จาก categoryListResponse
interface CategoryCard {
  cardNumber: string;
  cardGraphicURL?: string;
  credits?: {
    creditAmount?: number;
  };
}

interface CategoryCardDisplay {
  cardNumber: string;
  creditAmount: number;
  cardGraphicURL?: string;
}

interface CategoryListResponse {
  cards: CategoryCard[];
  newAccessToken?: string;
}

const PRESET_AMOUNTS = [300, 500, 1000, 2000, 5000, 10000];

export default function TopUpPage() {
  const { t } = useTranslation();
  const { data: session, status } = useSession();
  const systemBrandCode = session?.user.systemBrandCode.toLowerCase() ?? "";

  const router = useRouter();
  const params = useSearchParams();
  const { showError } = useErrorModal();

  const cardNumber = params.get("cardnumber");
  const cardID = params.get("cardID");
  // State management
  const [formData, setFormData] = useState<TopUpFormData>({
    amount: '',
    customAmount: ''
  });

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [displayCardNumber, setDisplayCardNumber] = useState<string>('');
  const [displayCardType, setDisplayCardType] = useState<string>('');
  const [isApiLoading, setIsApiLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false); // เพิ่มตัวแปร state สำหรับติดตามว่าเกิด error แล้วหรือไม่
  const [selectedCategoryCard, setSelectedCategoryCard] = useState<CategoryCardDisplay | null>(null);
  const [imgError, setImgError] = useState<boolean>(false); // เพิ่มตัวแปร state สำหรับติดตามว่ารูปโหลดไม่สำเร็จหรือไม่
  // ใช้ useQuery แต่กำหนดให้ไม่ทำงานอัตโนมัติตอน mount (enabled: false)
  const { 
    data: cardList,
    refetch: refetchCardList
  } = api.card.getCreditCardList.useQuery(undefined, { enabled: false });
  
  const { refetch: refetchCardCategory } = api.cardCategory.getCardCategoryList.useQuery(
    undefined, { enabled: false }
  );
  

  const handleGoBackToWebApp = () => {

    if(systemBrandCode == "tcc") {
      //  const message = "openPage:TopupMethod";
      const message = {
          "method": "close",
          "route" : ""
      }

      const userAgent = navigator.userAgent;
  
      if (/android/i.test(userAgent)) {
          window.tccObserver?.postMessage(JSON.stringify(message));
      } else if (/iPad|iPhone|iPod/.test(userAgent)) {
          window.webkit?.messageHandlers?.tccObserver?.postMessage(JSON.stringify(message));
      }

    } else {
      // window.location.assign(process.env.NEXT_PUBLIC_GO_BACK_HOME_LIFF_URL ?? '/');
    }
    
  };

  // ฟังก์ชันสำหรับ update token เพื่อลดความซ้ำซ้อนของโค้ด
  const updateToken = async (token: string) => {
    try {
      const res = await fetch('/api/set-token', {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({ token }),
      });
      const data = await res.json() as { message: string };
      return data;
    } catch (err) {
      console.error('[Set Token Error]', err);
      throw err;
    }
  };

  // Initialize and fetch data sequentially
  useEffect(() => {
    const fetchDataSequentially = async () => {
      // ถ้าเคยเกิด error แล้ว ไม่ต้องเรียก API อีก
      if (hasError) return;
      
      // เพิ่มตัวแปรภายในฟังก์ชันเพื่อติดตามสถานะ error แบบทันที
      let hasErrorLocal = false;
      
      try {
        setIsApiLoading(true);
        
        // First API call: Get credit card list
        const cardListResult = await refetchCardList();
        // เพิ่มการตรวจสอบ undefined
        if (!cardListResult?.data) {
          throw new Error('Failed to fetch credit card list');
        }
        
        const cardListResponse = cardListResult.data as CardListResponse;
        // console.log('cardListResponse::',cardListResponse)
        // Store card list in sessionStorage
        sessionStorage.setItem('storedCreditCardList', JSON.stringify(cardListResponse?.cards ?? []));
        
        // Update token if available from first API call
        if (cardListResponse?.newAccessToken) {
          await updateToken(cardListResponse.newAccessToken);
        }
        
        // Second API call: Get card category list (only after first call completes)
        const cardCategoryResult = await refetchCardCategory();
        // เพิ่มการตรวจสอบ undefined
        if (!cardCategoryResult?.data) {
          throw new Error('Failed to fetch card category list');
        }
        
        const categoryListResponse = cardCategoryResult.data as CategoryListResponse;
        
        // เก็บ Card Category List ใน sessionStorage
        // console.log('storedCategoryCardList::',categoryListResponse)
        sessionStorage.setItem('storedCategoryCardList', JSON.stringify(categoryListResponse?.cards ?? []));
        
        // ตรวจสอบและเลือก card จาก categoryListResponse
        if (categoryListResponse?.cards && Array.isArray(categoryListResponse.cards) && categoryListResponse.cards.length > 0) {
          const firstCard = categoryListResponse.cards[0];
          // ดูว่ามีการส่ง cardNumber มาหรือไม่
          if (cardNumber) {

            sessionStorage.setItem('cardNumberTopUp', cardNumber);

            // ค้นหา card ที่ตรงกับ cardNumber ที่ส่งมา
            const matchedCard = categoryListResponse.cards.find((card: CategoryCard) => card.cardNumber === cardNumber);
            if (matchedCard) {
              // ถ้าเจอ ให้ใช้ card นั้น
              setSelectedCategoryCard({
                cardNumber: matchedCard.cardNumber,
                creditAmount: matchedCard.credits?.creditAmount ?? 0,
                cardGraphicURL: matchedCard.cardGraphicURL
              });
            } else if (firstCard) {
              // ถ้าไม่เจอ ใช้ card แรก
              setSelectedCategoryCard({
                cardNumber: firstCard.cardNumber,
                creditAmount: firstCard.credits?.creditAmount ?? 0,
                cardGraphicURL: firstCard.cardGraphicURL
              });
            }
          } else if (firstCard) {
            // ถ้าไม่มี cardNumber ให้ใช้ card แรก
            setSelectedCategoryCard({
              cardNumber: firstCard.cardNumber,
              creditAmount: firstCard.credits?.creditAmount ?? 0,
              cardGraphicURL: firstCard.cardGraphicURL
            });
          }
        }
        
        // Update token if available from second API call
        if (categoryListResponse?.newAccessToken) {
          await updateToken(categoryListResponse.newAccessToken);
        }
        
      } catch (error) {
        console.error('API Error:', error);
        
        // ตั้งค่าตัวแปรภายในฟังก์ชันเป็น true ทันที
        hasErrorLocal = true;
        
        // ตั้งค่า state hasError เป็น true เพื่อป้องกันไม่ให้ยิง API ซ้ำอีกในครั้งถัดไป
        setHasError(true);
        
        // แสดงหน้า error modal พร้อมกำหนดให้ปุ่ม done redirect ไปยัง /auth/error
        showError({
          title: t('something_went_wrong'),
          description: t('this_page_is_currently_not_available'),
          showRedirectButton: false, // เปลี่ยนเป็น false เพื่อให้แสดงปุ่มเดียว
          redirectButtonText: t('done'),
          redirectTo: '/auth/error',
          cancelButtonText: '',
          type: "error"
        });
        
        // ไม่ต้อง return ทันทีเพื่อให้เข้า finally block ซึ่งจะตรวจสอบด้วย hasErrorLocal
      } finally {
        // ตั้งค่า isApiLoading เป็น false เฉพาะเมื่อไม่เกิด error
        // ใช้ hasErrorLocal แทน hasError เพราะเป็นตัวแปรภายในฟังก์ชันที่อัปเดตค่าทันที
        if (!hasErrorLocal) {
          setIsApiLoading(false);
        }
      }
    };

    // เพิ่ม void operator เพื่อป้องกัน eslint warning เกี่ยวกับ promise
    void fetchDataSequentially();
    
    // เพิ่ม hasError เข้าไปใน dependency เพื่อให้ useEffect รู้ว่าต้องไม่เรียก API อีกเมื่อเกิด error
  }, [refetchCardList, refetchCardCategory, showError, t, hasError, cardNumber]);

  // Set display card number based on the cardList data
  useEffect(() => {
    if (cardList?.cards && Array.isArray(cardList.cards) && cardList.cards.length > 0) {
      let cardToUse;
      
      // ถ้ามี cardID จาก params ให้หาการ์ดที่มี ID ตรงกัน
      if (cardID) {
        cardToUse = cardList.cards.find(card => card.cardID?.toString() === cardID);
      }
      
      // ถ้าไม่มีหรือหาไม่เจอ ใช้การ์ด default หรือการ์ดแรก
      if (!cardToUse) {
        cardToUse = cardList.cards.find(card => card.isDefault === true) ?? cardList.cards[0];
      }
      
      if (cardToUse) {
        setDisplayCardNumber(cardToUse.cardNumber ?? '');
        setDisplayCardType(cardToUse.cardType ?? '');
      }
    } else {
      setDisplayCardNumber('');
      setDisplayCardType('');
    }
  }, [cardList?.cards, cardID]);

  // Event handlers
  const handleAmountSelect = (amount: number) => {
    setFormData(prev => ({
      ...prev,
      amount: amount.toString(),
      customAmount: amount.toString()
    }));
  };

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, ''); // Only allow numbers
    setFormData(prev => ({
      ...prev,
      customAmount: value,
      amount: value
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // หา cardID จาก cardList โดยเทียบกับ displayCardNumber
      const selectedCard = cardList?.cards?.find(card => card.cardNumber === displayCardNumber);
      const selectedCardID = selectedCard?.cardID?.toString() ?? "";
      
      // เพิ่ม URL parameters สำหรับส่งไปหน้า confirmtopup
      const params = new URLSearchParams({
        cardID: selectedCardID,
        categoryCardNumber: selectedCategoryCard?.cardNumber ?? "",
        amount: formData.amount
      });
      
      // ให้ redirect ไปยังหน้า confirmtopup พร้อมกับส่ง parameters
      router.push(`/confirmtopup?${params.toString()}`);

    } catch (error) {
      console.error('Top-up error:', error);
      // Handle error - show error modal
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.amount && parseInt(formData.amount) > 0 && !(formData.customAmount && (parseInt(formData.customAmount) < 300 || parseInt(formData.customAmount) > 10000));

  const goToMyCardList = () => {
    // ส่ง cardnumber ที่ได้จาก API ไปด้วย เพื่อให้สามารถส่งกลับมาได้
    window.location.assign(`/mycardlist-topup?cardnumber=${cardNumber ?? ""}`);
  };

  const goToAddCard = () => {
    window.location.assign("/addcard");
  };
  
  if(isApiLoading || status === "loading") {
    return (
      <div className="flex flex-col min-h-screen bg-white animate-pulse">
        {/* Header Skeleton */}
        <div className="flex items-center justify-center p-4 relative">
          <div className="absolute top-4 left-4">
            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
          </div>
          <div className="h-7 w-36 bg-gray-200 rounded mt-1"></div>
        </div>

        <div className="px-4 mt-6 flex-1 overflow-auto">
          {/* Amount Section Skeleton */}
          <div className="space-y-4 mb-6">
            <div className="h-6 w-40 bg-gray-200 rounded"></div>
            <div className="p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-8 bg-gray-200 rounded"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                    <div className="h-6 w-20 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="h-5 w-5 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
          
          {/* Payment Method Skeleton */}
          <div className="space-y-4 mb-6">
            <div className="h-6 w-40 bg-gray-200 rounded"></div>
            <div className="p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-6 bg-gray-200 rounded"></div>
                  <div className="h-4 w-48 bg-gray-200 rounded"></div>
                </div>
                <div className="h-5 w-5 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
          
          {/* Amount Input Skeleton */}
          <div className="space-y-4 mb-6">
            <div className="h-6 w-48 bg-gray-200 rounded"></div>
            <div className="mt-10 relative">
              <div className="h-8 w-full bg-gray-200 rounded"></div>
            </div>
            
            {/* Preset Buttons Skeleton */}
            <div className="grid grid-cols-3 gap-3 mt-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="h-12 bg-gray-200 rounded-full"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer Skeleton */}
        <div className="fixed bottom-0 left-0 w-full bg-white p-4">
          <div className="h-14 w-full bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    )
  }
  
  return (
    <>

      <div className="flex flex-col min-h-screen bg-gray-50 w-full max-w-md mx-auto">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={handleGoBackToWebApp} />
          </div>
          <h1 className="text-lg font-medium mt-1">{t('topup_title')}</h1>
        </div>

        <div className="flex flex-col p-6 space-y-6 flex-1">
          {/* Top up to Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">{t('topup_to')}</h2>

            {/* Credit Card Display */}
            <div 
              className="bg-white rounded-lg p-4 border border-gray-200 cursor-pointer"
              onClick={() => window.location.assign(`/selectcard?cardNumber=${selectedCategoryCard?.cardNumber ?? ""}`)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {!imgError && selectedCategoryCard?.cardGraphicURL ? (
                    <div className="w-12 h-8 rounded flex items-center justify-center">
                      <img
                        src={selectedCategoryCard.cardGraphicURL}
                        alt="Card Category"
                        className="rounded" 
                        onError={() => setImgError(true)}
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-8 bg-gray-800 rounded flex items-center justify-center">
                      <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                      </div>
                    </div>
                  )}
                  <div>
                    <p className="text-gray-500 text-sm">{selectedCategoryCard?.cardNumber ?? ""}</p>
                    <p className="text-lg font-semibold">
                      {selectedCategoryCard ? selectedCategoryCard.creditAmount.toLocaleString() : "0"} <span className="text-sm font-normal">{t('topup_thb')}</span>
                    </p>
                  </div>
                </div>
                <ChevronLeft className="h-5 w-5 ml-auto rotate-180 text-[#8AD8D4]" />
              </div>
            </div>
          </div>

          {/* Payment Method Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">{t('topup_payment_method')}</h2>

            {cardList?.cards && Array.isArray(cardList.cards) && cardList.cards.length > 0 ? (
              /* กรณีมี credit card */
              <div 
                className={`bg-white rounded-lg p-4 border border-gray-200 cursor-pointer`}
                onClick={goToMyCardList}
              >
                <label className="flex items-center justify-between cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <div className="w-10 h-8 rounded-md flex items-center justify-center mr-3">
                        <Image
                          src={`/images/credit-card/${displayCardType ? displayCardType.toLowerCase() : 'mastercard'}.png`}
                          alt={displayCardType || 'Credit Card'}
                          width={40}
                          height={40}
                          className="rounded"
                        />
                      </div>
                      <span className="text-sm">{displayCardNumber}</span>
                      <span className="ml-3 px-3 py-1 text-alert-label bg-alert-label-background rounded-full text-xs">
                        {t('default_card')}
                      </span>                       
                    </div>
                  </div>
                  <ChevronLeft className="h-5 w-5 ml-auto rotate-180 text-[#8AD8D4]" />
                </label>
              </div>
            ) : (
              /* กรณีไม่มี credit card */
              <div
                className="bg-white rounded-lg p-4 border border-gray-200 cursor-pointer"
                onClick={goToAddCard}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-6 bg-gray-200 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-500">💳</div>
                    </div>
                    <span className="text-gray-500">{displayCardNumber || t('topup_select_card') }</span>
                  </div>
                  <ChevronLeft className="h-5 w-5 ml-auto rotate-180 text-[#8AD8D4]" />
                </div>
              </div>
            )}
          </div>

          {/* Enter Amount Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">{t('topup_enter_amount')}</h2>

            {/* Custom Amount Input */}
            <div className="relative mt-10">
              <input
                type="text"
                value={formData.customAmount}
                onChange={handleCustomAmountChange}
                className="w-full bg-transparent text-2xl font-medium text-gray-800 border-0 border-b-2 border-gray-300 focus:border-orange-500 focus:outline-none pb-2 pr-16 transition-colors duration-200"
                inputMode="numeric"
                placeholder=" "
                autoComplete="off"
                id="amount-input"
              />
              <label
                htmlFor="amount-input"
                className={`absolute left-0 transition-all duration-200 pointer-events-none ${
                  formData.customAmount
                    ? 'top-0 text-sm text-orange-500 transform -translate-y-6'
                    : 'top-2 text-lg text-gray-400'
                }`}
              >
                {t('topup_amount')}
              </label>
              <div className="absolute right-0 top-2">
                <span className="text-lg font-medium text-gray-600">{t('topup_thb')}</span>
              </div>
              {
                (formData.customAmount && (parseInt(formData.customAmount) < 300 || parseInt(formData.customAmount) > 10000)) && (
                  <span className="text-xs text-red-600">Please enter an amount between 300 and 10,000 THB</span>
                )
              }
              
            </div>
            
            {/* Preset Amount Buttons */}
            <div className="grid grid-cols-3 gap-3">
              {PRESET_AMOUNTS.map((amount) => (
                <Button
                  key={amount}
                  variant="outline"
                  onClick={() => handleAmountSelect(amount)}
                  className={`h-12 rounded-full border-2 ${
                    formData.amount === amount.toString()
                      ? 'border-[#4FC3F7] bg-[#4FC3F7]/10 text-[#4FC3F7]'
                      : 'border-gray-300 text-gray-600 hover:border-gray-400'
                  }`}
                >
                  {amount.toLocaleString()}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleSubmit}
            className="w-full bg-quaternary text-white py-6 text-lg rounded-lg disabled:opacity-50"
            disabled={!isFormValid || isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Image src="/images/animate/loading0.svg" width={50} height={50} alt="Loading animation" />
              </div>
            ) : (
              t('topup_next')
            )}
          </Button>
        </footer>
      </div>
    </>
  );
}
