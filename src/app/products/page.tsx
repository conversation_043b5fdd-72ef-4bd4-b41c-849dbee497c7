import {
  Pa<PERSON><PERSON>,
  Pa<PERSON>ationContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { api } from "@/trpc/server";
import Image from "next/image";
import Link from "next/link";

export default async function ProductPage() {
  const productList = await api.product.list();

  return (
    <main className="flex flex-1 justify-center">
      <section className="w-full max-w-6xl px-4 py-4 md:py-8">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center sm:gap-0">
            <h1 className="text-center text-xl font-bold tracking-tight sm:text-left sm:text-2xl">
              Products
            </h1>
            <div className="flex items-center justify-center gap-2 sm:justify-end">
              <span className="text-sm text-muted-foreground">Sort by:</span>
              <select className="rounded-md border border-input bg-background px-2 py-1 text-sm">
                <option>Newest</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Best Rated</option>
              </select>
            </div>
          </div>
          <div className="mx-auto grid w-full grid-cols-2 gap-3 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
            {productList.map((i) => (
              <div
                key={i}
                className="group relative overflow-hidden rounded-lg border"
              >
                <Link href="#" className="absolute inset-0 z-10">
                  <span className="sr-only">View Product</span>
                </Link>
                <div className="aspect-square overflow-hidden bg-muted"></div>
                <div className="p-2 sm:p-4">
                  <h3 className="truncate text-center text-sm font-medium text-primary sm:text-base">
                    Product {i + 1}
                  </h3>
                  <p className="text-center text-xs text-muted-foreground sm:text-sm">
                    Category
                  </p>
                  <div className="mt-1 flex items-center justify-between sm:mt-2">
                    <span className="text-sm font-medium sm:text-base">
                      ${(19.99 + i * 5).toFixed(2)}
                    </span>
                    <div className="flex items-center gap-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3 w-3 text-yellow-400 sm:h-4 sm:w-4"
                      >
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                      </svg>
                      <span className="text-xs sm:text-sm">
                        {(3.5 + i * 0.1).toFixed(1)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Pagination className="mt-6 md:mt-8">
            <PaginationContent className="flex flex-wrap justify-center">
              <PaginationItem>
                <PaginationPrevious href="#" />
              </PaginationItem>
              <PaginationItem className="hidden sm:inline-block">
                <PaginationLink href="#" isActive>
                  1
                </PaginationLink>
              </PaginationItem>
              <PaginationItem className="hidden sm:inline-block">
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem className="hidden sm:inline-block">
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem className="hidden sm:inline-block">
                <PaginationEllipsis />
              </PaginationItem>
              <PaginationItem>
                <PaginationNext href="#" />
              </PaginationItem>
            </PaginationContent>
            <div className="mt-2 text-center text-sm text-muted-foreground sm:hidden">
              Page 1 of 10
            </div>
          </Pagination>
        </div>
      </section>
    </main>
  );
}
