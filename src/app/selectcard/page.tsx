'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { useSearchParams } from "next/navigation";
import { useTranslation } from '@/contexts/LanguageContext';

interface CreditCard {
  id?: string;
  cardNumber: string;
  cardType?: string;
  balance?: string;
  isSelected?: boolean;
  cardGraphicURL?: string;
  credits?: {
    creditAmount: number;
    creditCode: string;
    creditName: string;
  };
}

export default function SelectCardPage() {
  const { t } = useTranslation();
  const router = useRouter();
  
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [pageLoading, setPageLoading] = useState(true);
  const [imgError, setImgError] = useState<boolean>(false);
  const [cardData, setCardData] = useState<CreditCard[]>();
  const params = useSearchParams();
  const cardNumber = params.get("cardNumber");

  useEffect(() => {
    const storedData = sessionStorage.getItem('storedCategoryCardList');
    if (storedData) {
      const parsed = JSON.parse(storedData) as CreditCard;
      if (Array.isArray(parsed)) {
        setCardData(parsed);
        // ✅ ใช้ cardNumber จาก URL เป็น default selection ถ้ามี
        if (cardNumber && (parsed as CreditCard[]).some(card => card.cardNumber === cardNumber))  {
          setSelectedCardId(cardNumber);
        } else if (parsed.length > 0) {
          setSelectedCardId((parsed[0] as CreditCard).cardNumber);
        }
      } else {
        setCardData([parsed]);
        // ✅ ใช้ cardNumber จาก URL เป็น default selection ถ้ามี
        if (cardNumber && parsed.cardNumber === cardNumber) {
          setSelectedCardId(cardNumber);
        } else {
          setSelectedCardId(parsed.cardNumber);
        }
      }
    }
  }, [cardNumber]); // ✅ เพิ่ม cardNumber เป็น dependency

  console.log(cardData);
  console.log('Selected Card ID:', selectedCardId);
  console.log('Card Number from URL:', cardNumber);

  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Event handlers
  const handleCardSelect = (cardNumber: string) => {
    console.log('Selecting card:', cardNumber);
    setSelectedCardId(cardNumber);
  };

  const handleConfirm = () => {
    const selectedCard = cardData?.find(card => card.cardNumber === selectedCardId);
    if (selectedCard) {
      sessionStorage.setItem('selectedCard', JSON.stringify(selectedCard));
      console.log('Selected card confirmed:', selectedCard);
    }
    // console.log(selectedCardId);
    // router.back();
    window.location.assign(`/topup-creditcard?cardnumber=${selectedCardId}`);
  };

  return (
    <>
      {pageLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 z-50">
          <div className="rounded-full">
            <Image src="/images/animate/loading0.svg" width={100} height={100} alt="Loading animation" />
          </div>
        </div>
      )}

      <div className="flex flex-col min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-[#DDF1F0] flex items-center justify-center p-4 relative text-gray-700">
          <div className="absolute top-4 left-4">
            <ArrowLeft className="h-6 w-6 mt-2 cursor-pointer" onClick={() => router.back()} />
          </div>
          <h1 className="text-lg font-medium mt-1">{t('select_card')}</h1>
        </div>

        <div className="flex flex-col p-6 space-y-6 flex-1">
          {/* Top up to Section */}
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-gray-800">{t('topup_to')}</h2>
            
            {/* Credit Cards List */}
            <div className="space-y-4">
              {cardData?.map((card) => (
                <div
                  key={card.cardNumber}
                  className={`bg-white rounded-lg p-4 border-2 cursor-pointer transition-all ${
                    selectedCardId === card.cardNumber  // ✅ เปลี่ยนจาก cardNumber เป็น selectedCardId
                      ? 'border-orange-400 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleCardSelect(card.cardNumber)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {/* ✅ แก้ไข Radio Button ให้ใช้ selectedCardId */}
                      <Input
                        type="radio"
                        name="card-selection"
                        value={card.cardNumber}
                        checked={selectedCardId === card.cardNumber} // ✅ เปลี่ยนจาก cardNumber เป็น selectedCardId
                        onChange={() => handleCardSelect(card.cardNumber)}
                        className="h-6 w-6 cursor-pointer accent-quaternary"
                      />
                      
                      {/* Card Image */}
                      <div className="w-16 h-10 rounded-lg flex items-center justify-center relative overflow-hidden">
                        {!imgError && card?.cardGraphicURL ? (
                          <div className="w-12 h-8 rounded flex items-center justify-center">
                            <img
                              src={card.cardGraphicURL}
                              alt="Card Category"
                              className="rounded" 
                              onError={() => setImgError(true)}
                            />
                          </div>
                        ) : (
                          <div className="w-12 h-8 bg-gray-800 rounded flex items-center justify-center">
                            <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                              <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Card Details */}
                      <div>
                        <p className="text-gray-500 text-sm">{card.cardNumber}</p>
                        <p className="text-lg font-semibold">
                          {card.credits?.creditAmount ?? 0} <span className="text-sm font-normal text-gray-500">{t('topup_thb')}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white p-4 border-t border-gray-200">
          <Button
            onClick={handleConfirm}
            disabled={!selectedCardId}
            className="w-full bg-quaternary text-white py-6 text-lg rounded-lg disabled:bg-gray-400"
          >
            {t('select_ok')}
          </Button>
        </footer>
      </div>
    </>
  );
}