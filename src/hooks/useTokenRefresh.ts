// // hooks/useAccessToken.ts
// import { useEffect, useRef, useState } from 'react';

// export function useTokenRefresh(newToken?: string | null) {
//   const lastToken = useRef<string | null>(null);
//   const [isTokenReady, setIsTokenReady] = useState(false);

//   useEffect(() => {
//     const updateToken = async () => {
//       if (!newToken || newToken === lastToken.current) {
//         setIsTokenReady(true);
//         return;
//       }

//       lastToken.current = newToken;

//       try {
//         const res = await fetch('/api/set-token', {
//           method: 'POST',
//           credentials: 'include',
//           body: JSON.stringify({ token: newToken }),
//         });

//         const data = await res.json();
//         console.log('[Set Token Success]', data);
//       } catch (err) {
//         console.error('[Set Token Error]', err);
//       } finally {
//         setIsTokenReady(true);
//       }
//     };

//     updateToken();
//   }, [newToken]);

//   return isTokenReady;
// }
