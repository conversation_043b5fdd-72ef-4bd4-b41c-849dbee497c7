ARG BUILDERIMG='none'
ARG DEPLOYIMG='none'

# Build Stage
FROM ${BUILDERIMG} AS builder
WORKDIR /home/<USER>

# Copy source code
COPY . .

COPY .env.docker  /.env

# Set environment
ARG NODE_ENV
ENV NODE_ENV=${NODE_ENV}

RUN set -e && export $(grep -v '^#' /.env | xargs) \
    && npm install -g pnpm@9.11.0 \
    && pnpm install --frozen-lockfile \
    && pnpm build 

# Production Stage
FROM ${DEPLOYIMG} AS runner
WORKDIR /home/<USER>

# Install pnpm in the runner stage
RUN npm install -g pnpm@9.11.0

# Copy built files from builder stage
COPY --from=builder /home/<USER>/home/<USER>

# Expose port
EXPOSE ${PORT}

# Start the application
CMD ["pnpm", "start"]
