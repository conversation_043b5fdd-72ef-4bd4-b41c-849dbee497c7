<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090914 at Thu Oct 28 09:49:19 2021
 By www-data
Copyright &#194;&#169; 2009, 2013 Cadson Demak Co. Ltd., Thailand. All Rights Reserved.
</metadata>
<defs>
<font id="SukhumvitSet-Text" horiz-adv-x="0" >
  <font-face 
    font-family="Sukhumvit Set"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 0 0 0 2 0 4"
    ascent="800"
    descent="-200"
    x-height="483"
    cap-height="674"
    bbox="-581 -417 1113 1021"
    underline-thickness="50"
    underline-position="-75"
    unicode-range="U+000D-U+FB02"
  />
<missing-glyph horiz-adv-x="515" 
d="M60 0v673h395v-673h-395zM119 59h276v556h-276v-556z" />
    <glyph glyph-name="uni0E5A" unicode="&#xe2f;&#xe2f;" horiz-adv-x="727" 
d="M381 0l-0.000976562 421.997c-6.66699 -14 -16 -27 -28 -39s-25.833 -22.333 -41.5 -31s-32.667 -15.5 -51 -20.5s-37.5 -7.5 -57.5 -7.5c-30 0 -55.5 4.83301 -76.5 14.5s-38 23.334 -51 41.001s-22.5 38.5 -28.5 62.5s-9 50.667 -9 80v38h73v-39
c0 -46 8.5 -80.333 25.5 -103s44.167 -34 81.5 -34c20.667 0 41.167 4.66699 61.5 14s38.333 21.5 54 36.5s28.334 31.833 38.001 50.5s14.5 37.667 14.5 57v18h71v-177c16.667 6 32.167 15 46.5 27s26.833 25.333 37.5 40s19.167 30 25.5 46s9.5 31.333 9.5 46v18h72v-560
h-76v422c-10 -21.333 -25 -40.666 -45 -57.999s-43.333 -30.666 -70 -39.999v-324h-76z" />
    <glyph glyph-name="uni0E4F.liga" unicode="&#xe4f;&#x22;" horiz-adv-x="546" 
d="M273 26c-29.333 0 -57 5.66699 -83 17s-48.667 26.666 -68 45.999s-34.5 42 -45.5 68s-16.5 53.667 -16.5 83s5.5 57 16.5 83s26.167 48.667 45.5 68s42 34.5 68 45.5s53.667 16.5 83 16.5s57 -5.5 83 -16.5s48.667 -26.167 68 -45.5s34.5 -42 45.5 -68
s16.5 -53.667 16.5 -83s-5.5 -57 -16.5 -83s-26.167 -48.667 -45.5 -68s-42 -34.666 -68 -45.999s-53.667 -17 -83 -17zM273 58c25.333 0 49 4.66699 71 14s41.333 22.333 58 39s29.667 36 39 58s14 45.667 14 71c0 24.667 -4.66699 48.167 -14 70.5
s-22.333 41.666 -39 57.999s-36 29.333 -58 39s-45.667 14.5 -71 14.5s-49 -4.83301 -71 -14.5s-41.167 -22.667 -57.5 -39s-29.333 -35.666 -39 -57.999s-14.5 -45.833 -14.5 -70.5c0 -25.333 4.83301 -49 14.5 -71s22.667 -41.333 39 -58s35.5 -29.667 57.5 -39
s45.667 -14 71 -14zM273 119c-17.333 0 -33.333 3.16895 -48 9.50195s-27.5 15 -38.5 26s-19.667 23.667 -26 38s-9.5 29.833 -9.5 46.5c0 17.333 3.16699 33.333 9.5 48s15 27.5 26 38.5s23.833 19.667 38.5 26s30.334 9.5 47.001 9.5s32.5 -3.16699 47.5 -9.5
s28 -15 39 -26s19.667 -23.833 26 -38.5s9.5 -30.334 9.5 -47.001c0 -17.333 -3.16699 -33.333 -9.5 -48s-15 -27.334 -26 -38.001s-23.833 -19.167 -38.5 -25.5s-30.334 -9.5 -47.001 -9.5zM273 166.002c20 0 37 7.00098 51 21.001s21 31.667 21 53
c0 19.333 -7 36.166 -21 50.499s-31.333 21.5 -52 21.5c-19.333 0 -36.333 -7.16699 -51 -21.5s-22 -31.5 -22 -51.5c0 -19.333 7.16699 -36.333 21.5 -51s31.833 -22 52.5 -22zM232 491.003h-42l-9 196h60zM358 491.003h-42l-9 196h60z" />
    <glyph glyph-name="uni0E24.liga" unicode="&#xe24;&#xe32;" horiz-adv-x="949" 
d="M154 91c0 -18.667 8.6709 -28.001 26.0039 -28.001h54v-63h-73c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-84 71c6 20.667 16.333 41 31 61s32.834 37.667 54.501 53s46.167 27.666 73.5 36.999
s56.666 14 87.999 14c40 0 74.667 -6 104 -18s53.666 -29 72.999 -51c22.667 18.667 49.5 34.834 80.5 48.501s65.5 20.5 103.5 20.5c34 0 64.333 -5.33301 91 -16s49.167 -25.667 67.5 -45s32.166 -42.166 41.499 -68.499s14 -55.166 14 -86.499v-553h-76v553
c0 49.333 -12.333 86.833 -37 112.5s-59.334 38.5 -104.001 38.5c-32 0 -60.333 -5.5 -85 -16.5s-45.334 -25.167 -62.001 -42.5c6 -13.333 10.333 -27.833 13 -43.5s4 -31.834 4 -48.501v-553h-76v553c0 49.333 -13 86.833 -39 112.5s-63.667 38.5 -113 38.5
c-39.333 0 -74 -8.83301 -104 -26.5s-51 -40.5 -63 -68.5l81 -74c-38 -17.333 -57 -47.333 -57 -90v-154z" />
    <glyph glyph-name="uni0E26.liga" unicode="&#xe26;&#xe32;" horiz-adv-x="953" 
d="M174 84c0 -26 -7.49707 -46.5029 -22.4971 -61.5029s-35.167 -22.5 -60.5 -22.5h-59v63h41c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-96 75c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499
s46.334 27.833 74.001 37.5s57.167 14.5 88.5 14.5c39.333 0 73.333 -5.83301 102 -17.5s52.667 -28.167 72 -49.5c23.333 18 50.166 33.667 80.499 47s64.5 20 102.5 20c34 0 64.333 -5.33301 91 -16s49 -25.667 67 -45s31.667 -42.166 41 -68.499s14 -55.166 14 -86.499
v-553h-76v553c0 49.333 -12.333 86.833 -37 112.5s-59.334 38.5 -104.001 38.5c-30.667 0 -58.334 -5.5 -83.001 -16.5s-45.334 -24.833 -62.001 -41.5c12 -28 18 -59 18 -93v-553h-76v553c0 49.333 -12.833 86.833 -38.5 112.5s-62.834 38.5 -111.501 38.5
c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l93 -79c-38.667 -16.667 -58 -46.667 -58 -90v-155z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="535" 
d="M455 0h-73.999v423h-214v-423h-72v423h-77v60h77v14c0 27.333 3.5 52.5 10.5 75.5s17.833 42.667 32.5 59s33.167 29 55.5 38s48.833 13.5 79.5 13.5c25.333 0 48.5 -2.83301 69.5 -8.5s38.5 -12.167 52.5 -19.5l-17 -54c-13.333 7.33301 -28.833 12.833 -46.5 16.5
s-35.834 5.5 -54.501 5.5c-41.333 0 -70 -11.667 -86 -35s-24 -54 -24 -92v-13h288v-483z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="568" 
d="M308 483l0.00195312 -61.001h-142v-422h-72v422h-76v61h76v14c0 26 4.66699 50.333 14 73s23 42.334 41 59.001s40.333 29.834 67 39.501s57.667 14.5 93 14.5c31.333 0 62.166 -3.33301 92.499 -10s59.166 -15.667 86.499 -27v-646h-75v608
c-15.333 5.33301 -31 9.16602 -47 11.499s-35 3.5 -57 3.5c-48 0 -83.833 -11.833 -107.5 -35.5s-35.5 -54.167 -35.5 -91.5v-13h142z" />
    <glyph glyph-name=".notdef" horiz-adv-x="515" 
d="M60 0v673h395v-673h-395zM119 59h276v556h-276v-556z" />
    <glyph glyph-name=".null" 
 />
    <glyph glyph-name=".null" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="195" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="258" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="248" 
d="M163 521l-11 -333h-55l-11 333v153h77v-153zM68 46c0 15.333 4.83301 28.333 14.5 39s23.5 16 41.5 16s31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16s-31.833 5.33301 -41.5 16s-14.5 23.667 -14.5 39z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="329" 
d="M118 452h-48l-10 236h68zM259 452h-47l-11 236h68z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="566" 
d="M69 447h105l44 203h63l-44 -203h145l44 203h63l-44 -203h93v-54h-104l-30 -137h93v-54h-104l-22 -101l-22 -101h-64l22 101l22 101h-144l-22 -101l-22 -101h-64l22 101l22 101h-93v54h105l14.5 68.5l14.5 68.5h-93v54zM195 256h146l30 137h-146z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="566" 
d="M255 -9c-44 0 -80.3301 3.49414 -108.997 10.4941s-53.334 15.833 -74.001 26.5l19 61c19.333 -9.33301 42.5 -17.166 69.5 -23.499s58.5 -9.5 94.5 -9.5v246c-20 6.66699 -40.833 14.5 -62.5 23.5s-41.334 20.5 -59.001 34.5s-32.167 31.333 -43.5 52s-17 46 -17 76
c0 22 3.5 42.667 10.5 62s18 36.5 33 51.5s34 27.333 57 37s50.167 15.834 81.5 18.501v93h51v-91c29.333 -1.33301 56.333 -5.16602 81 -11.499s47 -14.5 67 -24.5l-18 -60c-18 8 -38.333 14.667 -61 20s-45.667 8.66602 -69 9.99902v-235
c22 -7.33301 43.833 -15.666 65.5 -24.999s41.167 -21.166 58.5 -35.499s31.5 -31.666 42.5 -51.999s16.5 -45.166 16.5 -74.499c0 -53.333 -16.833 -94.5 -50.5 -123.5s-77.834 -46.833 -132.501 -53.5v-94h-51v91zM420.003 172.994c0 16.667 -3 30.833 -9 42.5
s-14.167 21.834 -24.5 30.501s-22.5 16.167 -36.5 22.5s-28.667 12.166 -44 17.499v-226c34 5.33301 61.5 17 82.5 35s31.5 44 31.5 78zM141.003 488.993c0 -16 2.99902 -29.832 8.99902 -41.499s14 -21.834 24 -30.501s22 -16.167 36 -22.5s29 -12.5 45 -18.5v214
c-39.333 -4 -68.166 -14.833 -86.499 -32.5s-27.5 -40.5 -27.5 -68.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="768" 
d="M535 660h63l-366 -660h-63zM105 484c0 -16 1.5 -31.667 4.5 -47s7.66699 -28.833 14 -40.5s14.5 -21 24.5 -28s22 -10.5 36 -10.5s26.167 3.5 36.5 10.5s18.666 16.333 24.999 28s11 25 14 40s4.5 30.5 4.5 46.5s-1.5 31.667 -4.5 47s-7.66699 29 -14 41
s-14.666 21.5 -24.999 28.5s-22.5 10.5 -36.5 10.5s-26 -3.66699 -36 -11s-18.167 -16.833 -24.5 -28.5s-11 -25 -14 -40s-4.5 -30.5 -4.5 -46.5zM44 484c0 23.333 3 45.5 9 66.5s15 39.5 27 55.5s26.667 28.833 44 38.5s37.333 14.5 60 14.5s42.667 -4.83301 60 -14.5
s32 -22.5 44 -38.5s21 -34.5 27 -55.5s9 -43.167 9 -66.5s-3 -45.5 -9 -66.5s-15 -39.5 -27 -55.5s-26.667 -28.833 -44 -38.5s-37.333 -14.5 -60 -14.5s-42.667 4.83301 -60 14.5s-32 22.5 -44 38.5s-21 34.5 -27 55.5s-9 43.167 -9 66.5zM504 166
c0 -16 1.5 -31.667 4.5 -47s7.66699 -28.833 14 -40.5s14.5 -21 24.5 -28s22 -10.5 36 -10.5s26.167 3.5 36.5 10.5s18.666 16.333 24.999 28s11 25 14 40s4.5 30.5 4.5 46.5s-1.5 31.667 -4.5 47s-7.66699 29 -14 41s-14.666 21.5 -24.999 28.5s-22.5 10.5 -36.5 10.5
s-26 -3.66699 -36 -11s-18.167 -16.833 -24.5 -28.5s-11 -25 -14 -40s-4.5 -30.5 -4.5 -46.5zM443 166c0 23.333 3 45.5 9 66.5s15 39.5 27 55.5s26.667 28.833 44 38.5s37.333 14.5 60 14.5s42.834 -4.83301 60.501 -14.5s32.5 -22.5 44.5 -38.5s21 -34.5 27 -55.5
s9 -43.167 9 -66.5s-3 -45.5 -9 -66.5s-15 -39.5 -27 -55.5s-26.833 -28.833 -44.5 -38.5s-37.834 -14.5 -60.501 -14.5s-42.667 4.83301 -60 14.5s-32 22.5 -44 38.5s-21 34.5 -27 55.5s-9 43.167 -9 66.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="630" 
d="M473 48c-51.333 -38 -112.999 -56.999 -184.999 -56.999c-38.667 0 -72 5 -100 15s-51.167 23.5 -69.5 40.5s-31.833 36.833 -40.5 59.5s-13 46.667 -13 72c0 24 3.66699 45.5 11 64.5s17.166 36.5 29.499 52.5s26.5 30.667 42.5 44s32.667 25.666 50 36.999l-11 15
c-19.333 26 -35.833 50.167 -49.5 72.5s-20.5 48.166 -20.5 77.499c0 42 14.167 76.167 42.5 102.5s68.5 39.5 120.5 39.5c27.333 0 51 -3.83301 71 -11.5s36.5 -18 49.5 -31s22.5 -28.167 28.5 -45.5s9 -35.666 9 -54.999c0 -20 -4 -38.167 -12 -54.5
s-18.667 -31.5 -32 -45.5s-28.833 -27.167 -46.5 -39.5l-55.5 -37.5l180 -216c13.333 20.667 23.166 43.667 29.499 69s9.5 52 9.5 80v48h74v-48c0 -40.667 -5.66699 -78 -17 -112s-27.666 -64.667 -48.999 -92l76 -92h-82zM291.001 60.001
c27.333 0 52.498 3.33203 75.498 9.99902s43.167 17.334 60.5 32.001l-187 225c-27.333 -20 -50.166 -41.333 -68.499 -64s-27.5 -50.334 -27.5 -83.001c0 -37.333 12.833 -66.666 38.5 -87.999s61.834 -32 108.501 -32zM188.999 539
c0 -20.667 4.49902 -40.335 13.499 -59.002s22.5 -38 40.5 -58l10 -12c15.333 9.33301 29.833 18.833 43.5 28.5s25.667 19.834 36 30.501s18.5 21.834 24.5 33.501s9 24.5 9 38.5c0 24 -7 43.5 -21 58.5s-36 22.5 -66 22.5c-25.333 0 -46.666 -6.83301 -63.999 -20.5
s-26 -34.5 -26 -62.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="188" 
d="M118 452h-48l-10 236h68z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="297" 
d="M72 283c0 53.333 5.33301 101.833 16 145.5s25 82.667 43 117s38.833 64 62.5 89s48.5 45.833 74.5 62.5l19 -34c-23.333 -16 -44.166 -37 -62.499 -63s-33.666 -55.5 -45.999 -88.5s-21.833 -68.833 -28.5 -107.5s-10 -79 -10 -121s3.33301 -82.333 10 -121
s16.167 -74.5 28.5 -107.5s27.666 -62.667 45.999 -89s39.166 -47.5 62.499 -63.5l-19 -33c-26 16.667 -50.833 37.5 -74.5 62.5s-44.5 54.5 -62.5 88.5s-32.333 72.833 -43 116.5s-16 92.5 -16 146.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="297" 
d="M225 283c0 -54 -5.33301 -102.833 -16 -146.5s-25 -82.5 -43 -116.5s-38.833 -63.5 -62.5 -88.5s-48.5 -45.833 -74.5 -62.5l-19 33c23.333 16 44 37.167 62 63.5s33.333 56 46 89s22.167 68.833 28.5 107.5s9.5 79 9.5 121s-3.16699 82.333 -9.5 121
s-15.833 74.5 -28.5 107.5s-28 62.5 -46 88.5s-38.667 47 -62 63l19 34c26 -16.667 50.833 -37.5 74.5 -62.5s44.5 -54.667 62.5 -89s32.333 -73.333 43 -117s16 -92.167 16 -145.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="496" 
d="M390 610l46 -49l-142 -88v-6l142 -89l-46 -48l-117 106l-5 -4l16 -164h-72l17 165l-7 2l-116 -105l-46 48l141 88v7l-141 88l46 49l115 -104l8 2l-17 166h72l-16 -166l7 -2z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="566" 
d="M317 496v-182h178v-61h-178v-181h-67v181h-179v61h179v182h67z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="236" 
d="M174 97l-71 -222h-46l29 222h88z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="316" 
d="M286 302v-60h-256v60h256z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="236" 
d="M62 46c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="366" 
d="M71 -40h-61l285 714h61z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="566" 
d="M125 325c0 -91.333 13.5 -159.5 40.5 -204.5s66.167 -67.5 117.5 -67.5c50.667 0 89.667 22.5 117 67.5s41 113.167 41 204.5s-13.667 159.5 -41 204.5s-66.333 67.5 -117 67.5c-51.333 0 -90.5 -22.5 -117.5 -67.5s-40.5 -113.167 -40.5 -204.5zM49 325
c0 105.333 20.5 187.333 61.5 246s98.5 88 172.5 88c36.667 0 69.5 -7.5 98.5 -22.5s53.5 -36.833 73.5 -65.5s35.333 -63.667 46 -105s16 -88.333 16 -141s-5.33301 -99.667 -16 -141s-26 -76.333 -46 -105s-44.5 -50.5 -73.5 -65.5s-61.833 -22.5 -98.5 -22.5
c-74 0 -131.5 29.333 -172.5 88s-61.5 140.667 -61.5 246z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="566" 
d="M257 579l-140 -45v61l154 55h62v-582h156v-68h-392v68h160v511z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="566" 
d="M267 592c-28.667 0 -56.6689 -3.66797 -84.002 -11.001s-51.666 -16.333 -72.999 -27l-19 63c20.667 10.667 46.334 20.334 77.001 29.001s63.667 13 99 13c70.667 0 122.667 -15.5 156 -46.5s50 -73.5 50 -127.5c0 -27.333 -5 -53.833 -15 -79.5s-23.667 -51 -41 -76
s-37.666 -49.667 -60.999 -74l-75 -74.5l-116 -111h329v-70h-432v58l170 172l68.5 71.5c20.333 21.667 37.666 42.5 51.999 62.5s25.333 39.833 33 59.5s11.5 40.5 11.5 62.5c0 34.667 -11.833 61 -35.5 79s-55.167 27 -94.5 27z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="566" 
d="M249 57c53.333 0 92.667 11.334 118 34.001s38 53 38 91c0 40 -12.833 70 -38.5 90s-61.167 30 -106.5 30h-93v64h93c38 0 68.667 10.5 92 31.5s35 50.833 35 89.5c0 32.667 -11.833 58.5 -35.5 77.5s-61.5 28.5 -113.5 28.5c-31.333 0 -59.666 -3.16699 -84.999 -9.5
s-48 -14.5 -68 -24.5l-19 61c21.333 11.333 47.166 20.666 77.499 27.999s62.833 11 97.5 11c38 0 70.833 -4.33301 98.5 -13s50.5 -20.834 68.5 -36.501s31.333 -34 40 -55s13 -43.5 13 -67.5c0 -20.667 -2.83301 -39.167 -8.5 -55.5s-13.167 -30.5 -22.5 -42.5
s-20.333 -22.333 -33 -31s-26.334 -15.667 -41.001 -21c37.333 -8 67.333 -25.667 90 -53s34 -61.666 34 -102.999c0 -58.667 -20 -105 -60 -139s-96.667 -51 -170 -51c-40 0 -75.667 3.83301 -107 11.5s-57.666 17.167 -78.999 28.5l19 61
c20 -9.33301 44.167 -17.5 72.5 -24.5s59.5 -10.5 93.5 -10.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="566" 
d="M370 191v335l-239 -335h239zM380 649h62v-458h77v-65h-77v-126h-74v126h-323v52z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="566" 
d="M250 57c54 0 95.334 13.334 124.001 40.001s43 61 43 103c0 43.333 -13 77.333 -39 102s-69 37 -129 37h-159v54l25 257h338v-70h-274l-16 -173h87c79.333 0 139.666 -18.333 180.999 -55s62 -87.334 62 -152.001c0 -32.667 -5.66699 -61.834 -17 -87.501
s-27.5 -47.5 -48.5 -65.5s-46.5 -31.833 -76.5 -41.5s-63.333 -14.5 -100 -14.5c-42.667 0 -79.834 4 -111.501 12s-58.167 17.667 -79.5 29l20 61c19.333 -9.33301 43.333 -17.666 72 -24.999s61.334 -11 98.001 -11z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="566" 
d="M310 364c-37.333 0 -70.834 -7.83203 -100.501 -23.499s-53.834 -33.834 -72.501 -54.501c0 -36 4.16699 -68.333 12.5 -97s19.833 -53 34.5 -73s31.834 -35.5 51.501 -46.5s40.834 -16.5 63.501 -16.5c42 0 75.5 14.5 100.5 43.5s37.5 69.5 37.5 121.5
c0 20 -2.16699 38.833 -6.5 56.5s-11.333 33.167 -21 46.5s-22.667 23.833 -39 31.5s-36.5 11.5 -60.5 11.5zM318.999 597.001c-56.667 0 -100.834 -21.5078 -132.501 -64.5078s-48.5 -104.833 -50.5 -185.5c8 8.66699 17.833 17.834 29.5 27.501s25.167 18.334 40.5 26.001
s32.333 14 51 19s39.334 7.5 62.001 7.5c28.667 0 55 -4.83301 79 -14.5s44.5 -23.5 61.5 -41.5s30.167 -40 39.5 -66s14 -55.667 14 -89c0 -31.333 -5 -60.666 -15 -87.999s-24.167 -51.166 -42.5 -71.499s-40.833 -36.333 -67.5 -48s-56.334 -17.5 -89.001 -17.5
c-75.333 0 -133.833 27.333 -175.5 82s-62.5 136.334 -62.5 245.001c0 54.667 6 103.334 18 146.001s29.167 78.334 51.5 107.001s49.333 50.5 81 65.5s66.834 22.5 105.501 22.5c30.667 0 57.5 -3.16699 80.5 -9.5s44.167 -15.166 63.5 -26.499l-18 -57
c-36.667 20.667 -78 31 -124 31z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="566" 
d="M72 650h427v-51l-280 -599h-79l273 585h-341v65z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="566" 
d="M282 51c48 0 85 11 111 33s39 52.333 39 91c0 40.667 -12 72 -36 94s-62 33 -114 33s-90 -11 -114 -33s-36 -53.333 -36 -94c0 -38.667 13 -69 39 -91s63 -33 111 -33zM282 659c64.667 0 114.999 -15.3301 150.999 -45.9971s54 -72.334 54 -125.001
c0 -20.667 -2.83301 -39.334 -8.5 -56.001s-13.5 -31.334 -23.5 -44.001s-21.5 -23.334 -34.5 -32.001s-26.833 -15.334 -41.5 -20.001c17.333 -3.33301 33.666 -9.66602 48.999 -18.999s28.833 -21 40.5 -35s20.834 -30.333 27.501 -49s10 -38.667 10 -60
c0 -56.667 -19.667 -101.167 -59 -133.5s-94.333 -48.5 -165 -48.5s-125.667 16.167 -165 48.5s-59 76.833 -59 133.5c0 21.333 3.33301 41.333 10 60s15.834 35 27.501 49s25.167 25.667 40.5 35s31.666 15.666 48.999 18.999c-29.333 8.66699 -54.5 25.834 -75.5 51.501
s-31.5 59.167 -31.5 100.5c0 52.667 17.833 94.334 53.5 125.001s85.834 46 150.501 46zM281.999 598.003c-40.667 0 -72.667 -9.5 -96 -28.5s-35 -47.5 -35 -85.5c0 -39.333 11.667 -69.333 35 -90s55.333 -31 96 -31c41.333 0 73.5 10.333 96.5 31s34.5 50.667 34.5 90
c0 38 -11.5 66.5 -34.5 85.5s-55.167 28.5 -96.5 28.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="566" 
d="M255 286c37.333 0 70.832 7.83203 100.499 23.499s53.834 33.834 72.501 54.501c0 36 -4.16699 68.333 -12.5 97s-19.833 53 -34.5 73s-31.667 35.5 -51 46.5s-40.333 16.5 -63 16.5c-42 0 -75.667 -14.5 -101 -43.5s-38 -69.5 -38 -121.5
c0 -20 2.16699 -38.833 6.5 -56.5s11.333 -33.167 21 -46.5s22.667 -23.833 39 -31.5s36.5 -11.5 60.5 -11.5zM239.999 52.999c64.667 0 111.833 21.6709 141.5 65.0039s45.5 105 47.5 185c-8 -8.66699 -17.833 -17.667 -29.5 -27s-25.167 -18 -40.5 -26
s-32.333 -14.5 -51 -19.5s-39.334 -7.5 -62.001 -7.5c-28.667 0 -55 4.83301 -79 14.5s-44.5 23.5 -61.5 41.5s-30.167 40 -39.5 66s-14 55.667 -14 89c0 31.333 5 60.666 15 87.999s24.167 51.166 42.5 71.499s40.833 36.333 67.5 48s56.667 17.5 90 17.5
c34.667 0 66.667 -6.83301 96 -20.5s54.5 -34.334 75.5 -62.001s37.333 -61.834 49 -102.501s17.5 -88 17.5 -142c0 -112 -22 -196.833 -66 -254.5s-109.667 -86.5 -197 -86.5c-31.333 0 -59.5 3.16699 -84.5 9.5s-47.167 14.5 -66.5 24.5l18 57
c18.667 -9.33301 39.334 -16.5 62.001 -21.5s45.667 -7.5 69 -7.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="256" 
d="M72 422c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39zM72 45c0 15.333 4.66699 28.333 14 39
s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="256" 
d="M72 422c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39zM178 97l-71 -222h-46l29 222h88z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="565" 
d="M64 311l421 231v-71l-345 -185l345 -184v-72l-421 231v50z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="565" 
d="M480 224v-61h-402v61h402zM480 408v-61h-402v61h402z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="565" 
d="M501 261l-421 -231v72l345 184l-345 185v71l421 -231v-50z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="488" 
d="M226 616c-28.667 0 -56.8291 -3.83301 -84.4961 -11.5s-52.167 -16.834 -73.5 -27.501l-19 63c21.333 10.667 47.333 20.5 78 29.5s63.667 13.5 99 13.5c68.667 0 119.5 -14.833 152.5 -44.5s49.5 -69.834 49.5 -120.501c0 -25.333 -4.33301 -48 -13 -68
s-19.5 -38.333 -32.5 -55s-27.167 -32.167 -42.5 -46.5s-29.5 -28.666 -42.5 -42.999s-23.833 -28.833 -32.5 -43.5s-13 -31 -13 -49c0 -21.333 3 -39 9 -53h-63c-8.66699 15.333 -13 34 -13 56c0 21.333 4.16699 40.666 12.5 57.999s18.666 33.333 30.999 48
s25.666 28.834 39.999 42.501s27.666 28 39.999 43s22.666 30.833 30.999 47.5s12.5 35.667 12.5 57c0 14 -2.33301 27.167 -7 39.5s-12.167 23.333 -22.5 33s-23.5 17.334 -39.5 23.001s-35 8.5 -57 8.5zM173.005 45c0 15.333 4.83301 28.333 14.5 39s23.5 16 41.5 16
s31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16s-31.833 5.33301 -41.5 16s-14.5 23.667 -14.5 39z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="816" 
d="M511 463c-10.667 5.33301 -23.835 9.50195 -39.502 12.502s-32.167 4.5 -49.5 4.5c-48 0 -82.5 -13.167 -103.5 -39.5s-31.5 -62.833 -31.5 -109.5c0 -25.333 2.83301 -47 8.5 -65s13.334 -32.833 23.001 -44.5s21 -20.334 34 -26.001s26.5 -8.5 40.5 -8.5
c16.667 0 32.334 3.66699 47.001 11s27.167 17.5 37.5 30.5s18.5 28.667 24.5 47s9 38.166 9 59.499v128zM101.999 327.002c0 -50 7.83105 -93.4971 23.498 -130.497s37 -67.667 64 -92s58.5 -42.666 94.5 -54.999s74.667 -18.5 116 -18.5c27.333 0 54.5 3.33301 81.5 10
s51.5 16.667 73.5 30c0 -0.666992 1 -2.83398 3 -6.50098s4 -7.66699 6 -12s4 -8.33301 6 -12s3 -5.83398 3 -6.50098c-20.667 -13.333 -45.834 -24 -75.501 -32s-62.834 -12 -99.501 -12c-48 0 -92.5 7 -133.5 21s-76.667 35 -107 63s-54.166 63 -71.499 105
s-26 91.333 -26 148c0 47.333 8 92.5 24 135.5s39.333 81 70 114s68.167 59.167 112.5 78.5s95.166 29 152.499 29c52 0 98.833 -8.16699 140.5 -24.5s77 -39 106 -68s51.333 -63.5 67 -103.5s23.5 -83.667 23.5 -131c0 -40 -3.66699 -74.167 -11 -102.5
s-17.166 -51.833 -29.499 -70.5s-26.666 -32.334 -42.999 -41.001s-33.166 -13 -50.499 -13c-27.333 0 -48.666 9.66699 -63.999 29s-23.666 41.666 -24.999 66.999c-5.33301 -11.333 -12.833 -22.666 -22.5 -33.999s-21 -21.5 -34 -30.5s-27.5 -16.167 -43.5 -21.5
s-32.333 -8 -49 -8c-49.333 0 -88 17.667 -116 53s-42 83.333 -42 144c0 27.333 3.33301 53.333 10 78s17.834 46.5 33.501 65.5s36 34.167 61 45.5s55.833 17 92.5 17c28.667 0 55.834 -3.16699 81.501 -9.5s49.167 -15.166 70.5 -26.499v-242
c0 -21.333 3.83301 -40.333 11.5 -57s21.167 -25 40.5 -25c8.66699 0 18 3 28 9s19.167 16.167 27.5 30.5s15.166 33.5 20.499 57.5s8 54.333 8 91c0 34.667 -5.66699 68.667 -17 102s-28.833 63.166 -52.5 89.499s-53.834 47.666 -90.501 63.999
s-80.334 24.5 -131.001 24.5s-95.667 -8.33301 -135 -25s-72.5 -39.334 -99.5 -68.001s-47.667 -62.167 -62 -100.5s-21.5 -79.166 -21.5 -122.499z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="614" 
d="M435 353c16.667 -2 33 -6.99902 49 -14.999s30.333 -18.667 43 -32s22.834 -29.333 30.501 -48s11.5 -40 11.5 -64c0 -28 -4.83301 -54 -14.5 -78s-24.334 -44.5 -44.001 -61.5s-44.5 -30.333 -74.5 -40s-65.333 -14.5 -106 -14.5h-248v674h247
c69.333 0 122 -16.333 158 -49s54 -75.334 54 -128.001c0 -19.333 -3.33301 -37 -10 -53s-15 -30.167 -25 -42.5s-21.333 -22.5 -34 -30.5s-25 -14 -37 -18zM329 68.001c54 0 95.167 11 123.5 33s42.5 53 42.5 93c0 44.667 -15.167 76 -45.5 94s-70.833 27 -121.5 27h-172
v-247h173zM330 382.001c38 0 70.167 9.83301 96.5 29.5s39.5 49.167 39.5 88.5c0 35.333 -12.333 61.833 -37 79.5s-58 26.5 -100 26.5h-173v-224h174z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="570" 
d="M365 615c-76.667 0 -136.499 -24.001 -179.499 -72.001s-64.5 -116.667 -64.5 -206c0 -88 21.333 -156.167 64 -204.5s102.334 -72.5 179.001 -72.5c54.667 0 104 10.667 148 32l19 -64c-24 -12 -49.5 -21.167 -76.5 -27.5s-57.5 -9.5 -91.5 -9.5
c-54.667 0 -101.834 8.33301 -141.501 25s-72.5 40 -98.5 70s-45.333 66.333 -58 109s-19 90 -19 142c0 50.667 7 97.334 21 140.001s34.5 79.167 61.5 109.5s60.167 54 99.5 71s84.666 25.5 135.999 25.5c34.667 0 64.667 -2.83301 90 -8.5s49 -13.5 71 -23.5l-19 -64
c-40.667 18.667 -87.667 28 -141 28z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="649" 
d="M603 338c0 -110.667 -28 -194.666 -84 -251.999s-139 -86 -249 -86h-188v674h188c110 0 193 -28.333 249 -85s84 -140.334 84 -251.001zM527 338.001c0 47.333 -5.66699 87.833 -17 121.5s-27.833 61.167 -49.5 82.5s-48.5 37 -80.5 47s-68.667 15 -110 15h-112v-534h112
c41.333 0 78 5 110 15s58.833 25.833 80.5 47.5s38.167 49.5 49.5 83.5s17 74.667 17 122z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="545" 
d="M82 674h407v-69h-331v-222h311v-70h-311v-244h337v-69h-413v674z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="523" 
d="M82 674h399v-70h-323v-231h303v-70h-303v-303h-76v674z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="643" 
d="M376 614c-38.667 0 -73.665 -5.66797 -104.998 -17.001s-58 -28.5 -80 -51.5s-39 -52.167 -51 -87.5s-18 -76.666 -18 -123.999c0 -41.333 5 -78.833 15 -112.5s24.167 -62.5 42.5 -86.5s40.166 -42.667 65.499 -56s53.333 -20 84 -20c24 0 46.333 3.66699 67 11
s38.667 18.166 54 32.499s27.333 32.333 36 54s13 46.834 13 75.501v73h-139v67h211v-372h-59l-13 87c-14.667 -27.333 -36.334 -50.166 -65.001 -68.499s-65.334 -27.5 -110.001 -27.5c-43.333 0 -82.166 8.16699 -116.499 24.5s-63.5 39.5 -87.5 69.5s-42.333 66 -55 108
s-19 89 -19 141c0 55.333 7.5 104.666 22.5 147.999s36.5 79.833 64.5 109.5s62.333 52.334 103 68.001s86.667 23.5 138 23.5c36 0 67.5 -3.33301 94.5 -10s51.833 -15 74.5 -25l-19 -64c-19.333 9.33301 -41.833 16.666 -67.5 21.999s-52.5 8 -80.5 8z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="657" 
d="M499 312h-341v-312h-76v674h76v-291h341v291h76v-674h-76v312z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="244" 
d="M160 0h-76v674h76v-674z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="380" 
d="M300 192c0 -68.667 -14.1641 -119.168 -42.4971 -151.501s-70.833 -48.5 -127.5 -48.5c-25.333 0 -47.833 2.33301 -67.5 7s-36.834 11.334 -51.501 20.001l20 64c11.333 -6.66699 25.166 -12 41.499 -16s35.166 -6 56.499 -6c29.333 0 52.5 9.83301 69.5 29.5
s25.5 53.5 25.5 101.5v482h76v-482z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="598" 
d="M285 359l301 -359h-93l-283 338v37l275 299h90zM158 0h-76v674h76v-674z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="510" 
d="M82 674h76v-604h322v-70h-398v674z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="785" 
d="M352 110l-147.996 325l-27 60.5c-8.66699 20.333 -17.334 43.833 -26.001 70.5h-2c4 -30.667 6.33301 -60 7 -88s1 -50.333 1 -67v-411h-75v674h89l163 -361l16 -35.5l17 -38.5c5.33301 -12.667 10.166 -24.667 14.499 -36s7.5 -20.666 9.5 -27.999h3
c2 7.33301 5.16699 16.666 9.5 27.999s9.16602 23.333 14.499 36l16.5 38.5c5.66699 13 11.167 24.833 16.5 35.5l163 361h89v-674h-75v411c0 16.667 0.333008 39 1 67s3 57.333 7 88h-2c-8.66699 -26.667 -17.334 -50.167 -26.001 -70.5l-27 -60.5l-148 -325h-81z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="663" 
d="M447 218c13.333 -21.333 25.5 -42.168 36.5 -62.501l27.5 -52.5h2c-3.33301 22.667 -5.5 45.167 -6.5 67.5s-1.5 47.166 -1.5 74.499v429h76v-674h-81l-284 456c-13.333 20.667 -25.5 41.334 -36.5 62.001l-27.5 53h-2c3.33301 -22.667 5.5 -45.167 6.5 -67.5
s1.5 -47.166 1.5 -74.499v-429h-76v674h81z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="560" 
d="M520 466c0 -30.667 -5.33398 -58.833 -16.001 -84.5s-25.834 -48 -45.501 -67s-43.5 -33.833 -71.5 -44.5s-59.333 -16 -94 -16h-135v-254h-76v674h217c34.667 0 65.834 -5.33301 93.501 -16s50.834 -25.334 69.501 -44.001s33 -40.667 43 -66s15 -52.666 15 -81.999z
M443.999 466.001c0 42.667 -12.333 76.333 -37 101s-60.667 37 -108 37h-141v-280h135c47.333 0 84.333 12.667 111 38s40 60 40 104z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="687" 
d="M211 -88c30.667 0 55.499 7.8291 74.499 23.4961s31.833 34.5 38.5 56.5c-48 2 -89.5 12 -124.5 30s-64 42.333 -87 73s-40 66.667 -51 108s-16.5 86 -16.5 134c0 49.333 5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26
s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499c0 -45.333 -4.83301 -87.666 -14.5 -126.999s-24.667 -74 -45 -104s-45.833 -54.667 -76.5 -74s-67 -31.666 -109 -36.999
c-5.33301 -17.333 -13.666 -33.666 -24.999 -48.999s-25 -26.666 -41 -33.999h203v-66h-338v66h16zM342.999 53.9961c74.667 0 130.333 25.166 167 75.499s55 119.5 55 207.5c0 44 -4.5 83.333 -13.5 118s-22.667 64.167 -41 88.5s-41.5 43 -69.5 56s-60.667 19.5 -98 19.5
c-75.333 0 -131.166 -25.167 -167.499 -75.5s-54.5 -119.166 -54.5 -206.499c0 -88 18.167 -157.167 54.5 -207.5s92.166 -75.5 167.499 -75.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="587" 
d="M542 474c0 -48.667 -14.1689 -90.667 -42.502 -126s-66.833 -59 -115.5 -71l163 -277h-83l-158 269h-1h-148v-269h-75v674h239c34.667 0 65.834 -5.16699 93.501 -15.5s50.834 -24.5 69.501 -42.5s33 -39.167 43 -63.5s15 -50.5 15 -78.5zM465.998 474
c0 40 -12.333 71.5 -37 94.5s-60.667 34.5 -108 34.5h-164v-264h158c47.333 0 84.333 12 111 36s40 57 40 99z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="528" 
d="M248 -9c-48 0 -86.834 3.66699 -116.501 11s-55.167 16.666 -76.5 27.999l20 62c19.333 -9.33301 42.666 -17.333 69.999 -24s58.666 -10 93.999 -10c24 0 46.167 2.33301 66.5 7s37.833 11.834 52.5 21.501s26.167 22.167 34.5 37.5s12.5 33.666 12.5 54.999
s-4.33301 39 -13 53s-20.5 26 -35.5 36s-32.167 18.5 -51.5 25.5s-39.666 13.833 -60.999 20.5s-43 14.667 -65 24s-42.333 21.166 -61 35.499s-33.834 32.166 -45.501 53.499s-17.5 47.666 -17.5 78.999c0 25.333 4.16699 48.833 12.5 70.5s21.5 40.334 39.5 56.001
s41 28 69 37s61.667 13.5 101 13.5c33.333 0 63.666 -3.16699 90.999 -9.5s52 -15.166 74 -26.499l-20 -62c-19.333 9.33301 -41.666 16.833 -66.999 22.5s-51.333 8.5 -78 8.5c-52.667 0 -90.667 -10.167 -114 -30.5s-35 -46.5 -35 -78.5c0 -19.333 4 -35.666 12 -48.999
s18.833 -24.833 32.5 -34.5s29.667 -18.167 48 -25.5s37.833 -14.333 58.5 -21c24 -8 47.833 -16.833 71.5 -26.5s45 -22 64 -37s34.333 -33.333 46 -55s17.5 -48.5 17.5 -80.5s-5.66699 -59.667 -17 -83s-27.166 -42.666 -47.499 -57.999s-44.666 -26.833 -72.999 -34.5
s-59.166 -11.5 -92.499 -11.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="512" 
d="M294 604v-604h-76v604l-206 -1v71h488v-71z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="664" 
d="M332 -9c-38.667 0 -73.5 5.5 -104.5 16.5s-57.5 28 -79.5 51s-38.833 51.833 -50.5 86.5s-17.5 76 -17.5 124v405h76v-406c0 -70.667 15.167 -122.834 45.5 -156.501s73.833 -50.5 130.5 -50.5s100.167 16.833 130.5 50.5s45.5 85.834 45.5 156.501v406h76v-405
c0 -48 -5.83301 -89.333 -17.5 -124s-28.5 -63.5 -50.5 -86.5s-48.5 -40 -79.5 -51s-65.833 -16.5 -104.5 -16.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="612" 
d="M514 674h78.001l-248 -674h-77l-248 674h78l155 -426c9.33301 -28 18.166 -55.833 26.499 -83.5l25.5 -83.5h2l26.5 83l25.5 83z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="879" 
d="M190 0l-159.002 674h76l89 -381c8.66699 -36.667 15.834 -72.667 21.501 -108s10.167 -69 13.5 -101h2c5.33301 32.667 11.833 65.5 19.5 98.5s17.167 68.167 28.5 105.5l118 386h79l118 -386c11.333 -36.667 21 -71.667 29 -105s14.667 -66.333 20 -99h2
c2.66699 32 7 65.667 13 101s13.333 71.333 22 108l88 381h77l-159 -674h-76l-134 438c-7.33301 23.333 -14.666 49.5 -21.999 78.5s-13 54.167 -17 75.5h-2c-4 -22 -9.5 -47.333 -16.5 -76s-14.167 -54.667 -21.5 -78l-134 -438h-75z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="612" 
d="M306 296h-1l-192 -296h-84l233 350l-215 324h85l174 -270l173 270h85l-215 -324l234 -350h-85z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="563" 
d="M319 285v-285h-76v285l-227 389h80l184 -324h2l183 324h81z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="547" 
d="M44 53l357 552h-332v69h426v-54l-356 -551h362v-69h-457v53z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="278" 
d="M82 684h174v-57h-108v-688h108v-57h-174v802z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="366" 
d="M10 674h61l285 -714h-61z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="278" 
d="M196 -118h-174v57h108v688h-108v57h174v-802z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="520" 
d="M478 276h-77l-141 322l-142 -322h-76l185 398h66z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="427" 
d="M427 -69v-59h-427v59h427z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="295" 
d="M177 576l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="557" 
d="M152 230c0 -24.667 3.83301 -47.499 11.5 -68.499s18.167 -39.333 31.5 -55s29 -27.834 47 -36.501s37.333 -13 58 -13c18 0 35.333 3.33301 52 10s31.334 17.5 44.001 32.5s22.834 34.667 30.501 59s11.5 54.166 11.5 89.499c0 60 -12 104.5 -36 133.5
s-55 43.5 -93 43.5c-32.667 0 -62.334 -8.16699 -89.001 -24.5s-49.334 -37.166 -68.001 -62.499v-108zM78 674.001l74 0.00390625v-269c6.66699 9.33301 15.5 19.166 26.5 29.499s23.667 19.666 38 27.999s30.333 15.333 48 21s36.834 8.5 57.501 8.5
c31.333 0 58.833 -6.5 82.5 -19.5s43.334 -30.667 59.001 -53s27.5 -48.333 35.5 -78s12 -60.834 12 -93.501c0 -42.667 -4.83301 -80 -14.5 -112s-23.334 -58.833 -41.001 -80.5s-38.834 -37.834 -63.501 -48.501s-51.667 -16 -81 -16c-36.667 0 -69 9.16699 -97 27.5
s-48.667 40.5 -62 66.5l-15 -85h-59v674z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="457" 
d="M277 432c-27.333 0 -51.165 -4.6709 -71.498 -14.0039s-37 -22.5 -50 -39.5s-22.667 -37.167 -29 -60.5s-9.5 -48.666 -9.5 -75.999s3.16699 -52.666 9.5 -75.999s16.166 -43.5 29.499 -60.5s30.333 -30.333 51 -40s45.334 -14.5 74.001 -14.5
c20.667 0 41.5 2.5 62.5 7.5s40.833 12.167 59.5 21.5l17 -55c-20.667 -10.667 -42.5 -19 -65.5 -25s-49.5 -9 -79.5 -9c-39.333 0 -73.333 6.33301 -102 19s-52.667 30.334 -72 53.001s-33.666 49.334 -42.999 80.001s-14 63.667 -14 99s4.66699 68.333 14 99
s23.666 57.167 42.999 79.5s43.666 39.833 72.999 52.5s63.666 19 102.999 19c27.333 0 52.333 -2.83301 75 -8.5s43.667 -13.167 63 -22.5l-17 -56c-17.333 8 -36 14.5 -56 19.5s-41.667 7.5 -65 7.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="556" 
d="M404 340c-17.333 26.667 -38.001 47.5 -62.001 62.5s-49.667 22.5 -77 22.5c-42 0 -77 -15 -105 -45s-42 -76 -42 -138c0 -34 3.83301 -62.667 11.5 -86s18 -42.333 31 -57s27.833 -25.334 44.5 -32.001s34 -10 52 -10c20.667 0 39.834 4.33301 57.501 13
s33.167 20.834 46.5 36.501s23.833 34 31.5 55s11.5 43.833 11.5 68.5v110zM477.999 0l-58.999 -0.000976562l-15 85c-13.333 -26 -33.833 -48.167 -61.5 -66.5s-59.5 -27.5 -95.5 -27.5c-29.333 0 -56.333 5.33301 -81 16s-45.834 26.5 -63.501 47.5s-31.5 47 -41.5 78
s-15 67.167 -15 108.5c0 36 4.66699 69.167 14 99.5s23 56.833 41 79.5s39.833 40.334 65.5 53.001s54.5 19 86.5 19c18.667 0 36 -2.5 52 -7.5s30.333 -11.667 43 -20s23.667 -17.5 33 -27.5s17 -20 23 -30v267h74v-674z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="501" 
d="M117 208c4 -50 19.8311 -88.666 47.498 -115.999s67.5 -41 119.5 -41c22 0 44.333 2.33301 67 7s43 11.667 61 21l18 -55c-20.667 -10.667 -43.5 -18.834 -68.5 -24.501s-52.833 -8.5 -83.5 -8.5c-39.333 0 -73.5 6 -102.5 18s-53 28.833 -72 50.5s-33.333 48 -43 79
s-14.5 65.5 -14.5 103.5c0 35.333 4.33301 68.333 13 99s21.834 57.167 39.501 79.5s40.334 39.833 68.001 52.5s60.5 19 98.5 19c62.667 0 110 -17.833 142 -53.5s48 -84.5 48 -146.5c0 -16 -1 -31 -3 -45s-4.66699 -27 -8 -39h-327zM264.998 435.001
c-25.333 0 -47 -4.33301 -65 -13s-33 -20.667 -45 -36s-21 -33.5 -27 -54.5s-9.66699 -43.833 -11 -68.5h268c0.666992 1.33301 1 6 1 14v15c0 46 -9.33301 81.333 -28 106s-49.667 37 -93 37z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="334" 
d="M251 623c-17.333 0 -31.499 -2.5 -42.499 -7.5s-19.5 -12.5 -25.5 -22.5s-10.167 -22 -12.5 -36s-3.5 -30 -3.5 -48v-26h142v-60h-142v-423h-72v423h-77v60h77v27c0 52.667 11.333 94.667 34 126s61.667 47 117 47c20 0 37.667 -2.33301 53 -7s29.666 -10.667 42.999 -18
l-17 -54c-23.333 12.667 -48 19 -74 19z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="554" 
d="M404 336c-15.333 25.333 -35.5 46.5 -60.5 63.5s-51.167 25.5 -78.5 25.5c-42 0 -77 -14.833 -105 -44.5s-42 -74.834 -42 -135.501c0 -32.667 3.83301 -60.5 11.5 -83.5s18 -41.667 31 -56s27.833 -24.833 44.5 -31.5s34 -10 52 -10c22 0 42 4.33301 60 13
s33.5 20.834 46.5 36.501s23 34 30 55s10.5 43.833 10.5 68.5v99zM404 92c-13.333 -26 -33.8311 -48.1699 -61.498 -66.5029s-59.5 -27.5 -95.5 -27.5c-29.333 0 -56.333 5.33301 -81 16s-45.834 26.334 -63.501 47.001s-31.5 46.5 -41.5 77.5s-15 66.5 -15 106.5
c0 34.667 4.66699 67 14 97s23 56.167 41 78.5s39.833 39.833 65.5 52.5s54.5 19 86.5 19c18.667 0 36 -2.83301 52 -8.5s30.167 -12.667 42.5 -21s23.333 -17.666 33 -27.999s17.5 -20.166 23.5 -29.499l13 78h61v-447c0 -42 -5.33301 -77.833 -16 -107.5
s-25.667 -54 -45 -73s-42.5 -33 -69.5 -42s-56.833 -13.5 -89.5 -13.5c-34.667 0 -67.667 4.5 -99 13.5s-56.333 19.167 -75 30.5l18 60c19.333 -11.333 41.666 -20.5 66.999 -27.5s52.333 -10.5 81 -10.5c22.667 0 43.5 2.5 62.5 7.5s35.167 14 48.5 27
s23.833 30.5 31.5 52.5s11.5 50 11.5 84v55z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="562" 
d="M152 0l-74 -0.000976562v674h74v-266c6.66699 9.33301 15.667 19 27 29s24.333 19 39 27s31 14.667 49 20s37 8 57 8c51.333 0 91.5 -16.667 120.5 -50s43.5 -79.666 43.5 -138.999v-303h-72v303c0 38 -9.33301 68 -28 90s-44 33 -76 33
c-34 0 -64.833 -8.16699 -92.5 -24.5s-50.167 -36.5 -67.5 -60.5v-341z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483zM66 633c0 14 4.33301 26 13 36s21.334 15 38.001 15c17.333 0 30.166 -5 38.499 -15s12.5 -22 12.5 -36s-4.16699 -25.833 -12.5 -35.5s-21.166 -14.5 -38.499 -14.5c-16.667 0 -29.334 4.83301 -38.001 14.5s-13 21.5 -13 35.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="235" 
d="M154 -41c0 -26 -2 -49.168 -6 -69.501s-11 -37.166 -21 -50.499s-23.667 -23.5 -41 -30.5s-39.333 -10.5 -66 -10.5c-14.667 0 -28 1.66699 -40 5s-22.667 7.33301 -32 12l19 58c16.667 -7.33301 33.667 -11 51 -11c25.333 0 42 7.5 50 22.5s12 40.167 12 75.5v523h74
v-524zM66 632.999c0 14 4.33301 26 13 36s21.334 15 38.001 15c17.333 0 30.333 -5 39 -15s13 -22 13 -36s-4.33301 -25.833 -13 -35.5s-21.667 -14.5 -39 -14.5c-16.667 0 -29.334 4.83301 -38.001 14.5s-13 21.5 -13 35.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="501" 
d="M485 0h-87l-102 128l-37.5 47l-33 41l-23.5 29l-9 11v28l192 199h89l-206 -212zM152 0h-74v674h74v-674z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="234" 
d="M154 0h-74v674h74v-674z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="797" 
d="M150 0l-73.999 0.00195312v483h61l13 -78c6 10 13.833 20 23.5 30s20.834 19.333 33.501 28s27 15.667 43 21s33.667 8 53 8c33.333 0 60.166 -8.66699 80.499 -26s34.5 -41 42.5 -71c16 26 38 48.667 66 68s59.333 29 94 29c47.333 0 82 -16 104 -48s33 -74 33 -126
v-318h-73v318c0 32.667 -5.83301 59 -17.5 79s-32.167 30 -61.5 30c-14.667 0 -28.5 -2.66699 -41.5 -8s-25.167 -12.333 -36.5 -21s-21.833 -18.834 -31.5 -30.501s-17.834 -23.834 -24.501 -36.501v-331h-73v318c0 32.667 -6 59 -18 79s-32 30 -60 30
c-30 0 -56.833 -8.83301 -80.5 -26.5s-42.167 -38.5 -55.5 -62.5v-338z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="560" 
d="M150 0l-74.002 0.000976562v483h60l14 -78c7.33301 9.33301 16.5 19.166 27.5 29.499s23.833 19.666 38.5 27.999s30.834 15.333 48.501 21s36.834 8.5 57.501 8.5c51.333 0 91.5 -16.667 120.5 -50s43.5 -79.666 43.5 -138.999v-303h-73v303c0 38 -9.16699 68 -27.5 90
s-43.5 33 -75.5 33c-34 0 -64.833 -8.5 -92.5 -25.5s-50.167 -37.833 -67.5 -62.5v-338z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="555" 
d="M150 230c0 -24.667 3.83301 -47.499 11.5 -68.499s18.167 -39.333 31.5 -55s29 -27.834 47 -36.501s37.333 -13 58 -13c18 0 35.333 3.33301 52 10s31.334 17.5 44.001 32.5s22.834 34.667 30.501 59s11.5 54.166 11.5 89.499c0 60 -12 104.5 -36 133.5
s-55 43.5 -93 43.5c-30.667 0 -59.834 -8.16699 -87.501 -24.5s-50.834 -37.166 -69.501 -62.499v-108zM137 483.001l13.001 -77.9961c6.66699 9.33301 15.5 19.166 26.5 29.499s23.667 19.666 38 27.999s30.5 15.333 48.5 21s37 8.5 57 8.5
c31.333 0 58.833 -6.5 82.5 -19.5s43.334 -30.667 59.001 -53s27.5 -48.333 35.5 -78s12 -60.834 12 -93.501c0 -42.667 -4.83301 -80 -14.5 -112s-23.334 -58.833 -41.001 -80.5s-38.834 -37.834 -63.501 -48.501s-51.667 -16 -81 -16c-36.667 0 -69 9.16699 -97 27.5
s-48.667 40.5 -62 66.5v-276h-74v674h61z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="559" 
d="M405 336c-16.667 27.333 -36.6689 49 -60.002 65s-49.666 24 -78.999 24c-42 0 -77 -15 -105 -45s-42 -76 -42 -138c0 -34 3.83301 -62.667 11.5 -86s18 -42.333 31 -57s27.833 -25.334 44.5 -32.001s34 -10 52 -10c20.667 0 39.834 4.33301 57.501 13
s33.167 20.834 46.5 36.501s23.833 34 31.5 55s11.5 43.833 11.5 68.5v106zM417.998 483l61.001 -0.000976562v-674h-74v276c-13.333 -26 -33.833 -48.167 -61.5 -66.5s-59.5 -27.5 -95.5 -27.5c-29.333 0 -56.333 5.33301 -81 16s-45.834 26.5 -63.501 47.5
s-31.5 47 -41.5 78s-15 67.167 -15 108.5c0 36 4.66699 69.167 14 99.5s23 56.833 41 79.5s39.833 40.334 65.5 53.001s54.5 19 86.5 19c18.667 0 36 -2.66699 52 -8s30.333 -12.333 43 -21s23.667 -18 33 -28s17 -20 23 -30z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="369" 
d="M150 397c6.66699 11.333 14.5 22.667 23.5 34s19.167 21.5 30.5 30.5s23.833 16.333 37.5 22s29.167 8.5 46.5 8.5c20 0 35.333 -2.33301 46 -7s17 -7.66699 19 -9l-21 -73c-14 12 -32.667 18 -56 18c-27.333 0 -51.833 -7.66699 -73.5 -23s-39.167 -37 -52.5 -65v-333
h-74v483h59z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="433" 
d="M198 49c40.667 0 69.6709 7.49902 87.0039 22.499s26 33.167 26 54.5c0 26.667 -11.333 46.834 -34 60.501s-50.334 25.167 -83.001 34.5c-21.333 6 -40.833 13 -58.5 21s-33 17.667 -46 29s-23 24.666 -30 39.999s-10.5 33.666 -10.5 54.999
c0 40.667 15.5 71.834 46.5 93.501s72.5 32.5 124.5 32.5c28 0 54.667 -2.66699 80 -8s47.666 -12.666 66.999 -21.999l-17 -57c-18 8.66699 -37.333 15.5 -58 20.5s-42.667 7.5 -66 7.5c-36 0 -62.667 -6 -80 -18s-26 -28 -26 -48c0 -23.333 9 -41 27 -53
s42 -22.667 72 -32c54 -16.667 94.833 -36.5 122.5 -59.5s41.5 -55.5 41.5 -97.5c0 -40.667 -14.667 -73.167 -44 -97.5s-75.333 -36.5 -138 -36.5c-30.667 0 -59.334 2.66699 -86.001 8s-49 13 -67 23l17 55c17.333 -8 37.666 -14.667 60.999 -20s47.333 -8 72 -8z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="349" 
d="M167 132c0 -26.667 4 -46.832 12 -60.499s24.667 -20.5 50 -20.5c15.333 0 29.5 2 42.5 6s24.5 8.66699 34.5 14l17 -53c-13.333 -8.66699 -28 -15.334 -44 -20.001s-36.333 -7 -61 -7c-42 0 -73 11 -93 33s-30 57.667 -30 107v292h-77v60h77l19 138h53v-138h149v-60
h-149v-291z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="553" 
d="M403 483h73.998v-483h-59l-15 85c-6 -10 -14.167 -20.667 -24.5 -32s-22.666 -21.5 -36.999 -30.5s-30.666 -16.5 -48.999 -22.5s-38.833 -9 -61.5 -9c-46 0 -83.667 15 -113 45s-44 78 -44 144v303h72v-303c0 -39.333 8.66699 -69.833 26 -91.5s41 -32.5 71 -32.5
c18 0 35 2.66699 51 8s30.667 12.5 44 21.5s25.5 19.5 36.5 31.5s20.5 24.333 28.5 37v329z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="462" 
d="M194 0l-175.998 483h76l88 -246l24.5 -80.5c8.33301 -26.333 16.166 -53.166 23.499 -80.499h2c7.33301 27.333 15.166 54.166 23.499 80.499l24.5 80.5l88 246h76l-176 -483h-74z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="677" 
d="M467 192c5.33301 -18 10.3311 -37.333 14.998 -58s8.66699 -42.334 12 -65.001h2c1.33301 23.333 3.33301 45.333 6 66s6.33398 40.667 11.001 60l66 288h72l-117 -483h-71l-99 315c-5.33301 17.333 -9.83301 34.333 -13.5 51s-7.16699 34.334 -10.5 53.001h-3
c-3.33301 -18.667 -6.83301 -36.334 -10.5 -53.001s-8.16699 -33.667 -13.5 -51l-99 -315h-71l-117 483h72l66 -288c4.66699 -19.333 8.33398 -39.333 11.001 -60s4.66699 -42.667 6 -66h2c3.33301 22.667 7.33301 44.334 12 65.001s9.66699 40 15 58l91 291h75z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="482" 
d="M240 202l-138 -202h-78l177 252l-162 231h78l71 -105.5l36.5 -54.5l13.5 -20l2 -3l124 183h79l-163 -231l178 -252h-78z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="550" 
d="M400 95c-6 -10.667 -13.999 -21.501 -23.999 -32.501s-22 -21.167 -36 -30.5s-29.833 -17 -47.5 -23s-37.167 -9 -58.5 -9c-49.333 0 -88.333 15 -117 45s-43 78 -43 144v294h72v-294c0 -39.333 8.5 -69.833 25.5 -91.5s40.833 -32.5 71.5 -32.5
c18 0 34.833 2.83301 50.5 8.5s30 13 43 22s24.833 19.333 35.5 31s20 23.834 28 36.501v320h74v-447c0 -80 -18.833 -139.5 -56.5 -178.5s-90.834 -58.5 -159.501 -58.5c-32 0 -63 4.5 -93 13.5s-54 19.5 -72 31.5l19 59c18.667 -11.333 40 -20.5 64 -27.5
s50 -10.5 78 -10.5c23.333 0 44 2.83301 62 8.5s33.333 15.167 46 28.5s22.167 31 28.5 53s9.5 49.333 9.5 82v58z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="449" 
d="M42 43l264 380h-244v60h334v-43l-264 -380h273v-60h-363v43z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="310" 
d="M52 308c31.333 0 52.333 7.00098 63 21.001s16 33.667 16 59c0 12 -0.833008 25 -2.5 39l-5 42.5l-5 42.5c-1.66699 14 -2.5 27.333 -2.5 40c0 44.667 12.333 77.834 37 99.501s60 32.5 106 32.5h21v-47h-20c-29.333 0 -49.666 -8.16699 -60.999 -24.5
s-17 -39.833 -17 -70.5c0 -24.667 2.5 -49.834 7.5 -75.501s7.5 -51.167 7.5 -76.5c0 -28 -5.5 -51 -16.5 -69s-30.167 -30.667 -57.5 -38c27.333 -8 46.5 -21 57.5 -39s16.5 -41 16.5 -69c0 -25.333 -2.5 -50.833 -7.5 -76.5s-7.5 -50.834 -7.5 -75.501
c0 -30 5.66699 -53.167 17 -69.5s31.666 -24.5 60.999 -24.5h20v-47h-21c-46 0 -81.333 10.833 -106 32.5s-37 54.5 -37 98.5c0 12.667 0.833008 26 2.5 40l5 42.5l5 42.5c1.66699 14 2.5 27 2.5 39c0 25.333 -5.33301 45 -16 59s-31.667 21 -63 21v51z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="247" 
d="M157 -200h-67v974h67v-974z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="310" 
d="M258 257c-31.333 0 -52.5 -7.00098 -63.5 -21.001s-16.5 -33.667 -16.5 -59c0 -12 0.833008 -25 2.5 -39l5 -42.5l5 -42.5c1.66699 -14 2.5 -27.333 2.5 -40c0 -44 -12.333 -76.833 -37 -98.5s-60 -32.5 -106 -32.5h-20v47h19c29.333 0 49.833 8.16699 61.5 24.5
s17.5 39.5 17.5 69.5c0 24.667 -2.5 49.834 -7.5 75.501s-7.5 51.167 -7.5 76.5c0 28 5.5 51 16.5 69s29.833 31 56.5 39c-26.667 7.33301 -45.5 20 -56.5 38s-16.5 41 -16.5 69c0 25.333 2.5 50.833 7.5 76.5s7.5 50.834 7.5 75.501c0 30.667 -5.83301 54.167 -17.5 70.5
s-32.167 24.5 -61.5 24.5h-19v47h20c46 0 81.333 -10.833 106 -32.5s37 -54.834 37 -99.501c0 -12.667 -0.833008 -26 -2.5 -40l-5 -42.5l-5 -42.5c-1.66699 -14 -2.5 -27 -2.5 -39c0 -25.333 5.5 -45 16.5 -59s32.167 -21 63.5 -21v-51z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="544" 
d="M466 387c6 -6 11.835 -12.165 17.502 -18.498s11.834 -12.5 18.501 -18.5c-13.333 -25.333 -31.5 -47.5 -54.5 -66.5s-49.167 -28.5 -78.5 -28.5c-24 0 -44.333 4 -61 12s-32 16.667 -46 26l-40.5 26c-13 8 -27.5 12 -43.5 12c-26 0 -46.5 -6.66699 -61.5 -20
s-27.833 -28.666 -38.5 -45.999c-6.66699 6 -12.834 12 -18.501 18l-17.5 19c5.33301 10.667 12.333 21.667 21 33s18.667 21.5 30 30.5s24 16.333 38 22s29 8.5 45 8.5c23.333 0 43.166 -3.83301 59.499 -11.5s31.333 -16.334 45 -26.001s27.167 -18.334 40.5 -26.001
s28.666 -11.5 45.999 -11.5c23.333 0 42.833 6.66699 58.5 20s29.167 28.666 40.5 45.999z" />
    <glyph glyph-name="uni007F" horiz-adv-x="500" 
 />
    <glyph glyph-name="nonbreakingspace" unicode="&#xa0;" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="248" 
d="M86 -38l11 333h55l11 -333v-153h-77v153zM180 437c0 -15.333 -4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16s-31.833 5.33301 -41.5 16s-14.5 23.667 -14.5 39s4.83301 28.333 14.5 39s23.5 16 41.5 16s31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="566" 
d="M305 -7c-34.667 2.66699 -65.1689 10.834 -91.502 24.501s-48.166 31.5 -65.499 53.5s-30.5 47.667 -39.5 77s-13.5 60.666 -13.5 93.999s4.33301 64.5 13 93.5s21.834 54.5 39.501 76.5s39.5 39.833 65.5 53.5s56.667 22.167 92 25.5v92h50v-91
c45.333 -2 85.333 -11.667 120 -29l-17 -56c-14.667 7.33301 -30.667 13 -48 17s-35.666 6.66699 -54.999 8v-381c18 1.33301 36.5 4.33301 55.5 9s36.5 10.667 52.5 18l17 -55c-36.667 -18.667 -78.334 -29 -125.001 -31v-92h-50v93zM167.998 242
c0 -49.333 11.167 -91.333 33.5 -126s56.833 -55.667 103.5 -63v377c-46 -6 -80.333 -26.333 -103 -61s-34 -77 -34 -127z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="566" 
d="M333 592c-24 0 -44.166 -3.66602 -60.499 -10.999s-29.666 -16.833 -39.999 -28.5s-17.666 -24.5 -21.999 -38.5s-6.5 -28 -6.5 -42c0 -22 3.83301 -42.333 11.5 -61s16.167 -37 25.5 -55h160v-55h-132c5.33301 -12.667 9.83301 -25.5 13.5 -38.5s5.5 -27.167 5.5 -42.5
c0 -32 -8.33301 -60.5 -25 -85.5s-39.667 -45.167 -69 -60.5h325v-74h-449v74h35c16.667 6 31.5 14.333 44.5 25s24 22.167 33 34.5s16 25.333 21 39s7.5 27.167 7.5 40.5c0 16 -2.33301 31.167 -7 45.5s-10 28.5 -16 42.5h-112v55h82c-10 19.333 -19 39.166 -27 59.499
s-12 42.833 -12 67.5c0 26.667 5.5 50.834 16.5 72.501s25.833 40.167 44.5 55.5s40.5 27.166 65.5 35.499s51.5 12.5 79.5 12.5c34.667 0 63.167 -3.33301 85.5 -10s44.166 -15.667 65.499 -27l-19 -61c-18.667 10.667 -37.334 18.5 -56.001 23.5s-41.334 7.5 -68.001 7.5z
" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="571" 
d="M487 164c-6 -6.66699 -12.168 -13.167 -18.501 -19.5s-12.5 -13.166 -18.5 -20.499l-55 58c-18 -11.333 -35.667 -20 -53 -26s-36 -9 -56 -9c-21.333 0 -40.666 3 -57.999 9s-34.333 14.667 -51 26l-56 -58l-18.5 20.5c-6.33301 7 -12.5 13.5 -18.5 19.5l55 56
c-12 16 -21 32.667 -27 50s-9 36.666 -9 57.999c0 20 3.16699 38.833 9.5 56.5s15.166 35.5 26.499 53.5l-40 39c-2 2.66699 -4.5 5.33398 -7.5 8.00098s-5.83301 5.66699 -8.5 9l18.5 18c6.33301 6 12.5 12.333 18.5 19l57 -57c16.667 13.333 34 22.833 52 28.5
s36.667 8.5 56 8.5c20 0 38.833 -3.16699 56.5 -9.5s35.167 -15.5 52.5 -27.5l56 56c6 -6 12 -12.333 18 -19s12 -12.667 18 -18l-54 -55c12 -17.333 21 -34.833 27 -52.5s9 -36.834 9 -57.501c0 -19.333 -3.16699 -38.333 -9.5 -57s-15.166 -35.667 -26.499 -51z
M416.999 328c0 18 -3.66699 35 -11 51s-17 29.833 -29 41.5s-26 20.834 -42 27.501s-32.667 10 -50 10c-18 0 -34.833 -3.33301 -50.5 -10s-29.5 -15.834 -41.5 -27.501s-21.333 -25.5 -28 -41.5s-10 -33 -10 -51s3.33301 -34.833 10 -50.5s16 -29.334 28 -41.001
s25.833 -20.834 41.5 -27.501s32.5 -10 50.5 -10c17.333 0 34 3.33301 50 10s30 15.834 42 27.501s21.667 25.334 29 41.001s11 32.5 11 50.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="566" 
d="M88 347l123 -0.000976562l-166 303h80l156 -300h2l157 300h81l-167 -303h123v-46h-147c-1.33301 -2.66699 -3 -5.16699 -5 -7.5s-3.66699 -5.16602 -5 -8.49902v-62h157v-45h-157v-178h-76v178h-156v45h156v62l-9 16h-147v46z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="246" 
d="M91 674h64v-351h-64v351zM91 176h64v-351h-64v351z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="566" 
d="M273 380c-18.667 5.33301 -33.665 10 -44.998 14s-22.333 9 -33 15c-6.66699 -4 -12.334 -9.33301 -17.001 -16c-4.66699 -5.33301 -8.66699 -12.166 -12 -20.499s-5 -17.833 -5 -28.5c0 -16 4.5 -29.5 13.5 -40.5s20.167 -20.333 33.5 -28s27.666 -14 42.999 -19
l43 -13.5c20 -6 35 -10.667 45 -14s20.333 -7.66602 31 -12.999c6.66699 4 13 9 19 15c4.66699 5.33301 9 11.5 13 18.5s6 15.833 6 26.5c0 25.333 -12.5 46.166 -37.5 62.499s-57.5 30.166 -97.5 41.499zM248.002 -4c19.333 0 37.6689 1.83105 55.002 5.49805
s32.666 9.16699 45.999 16.5s24 16 32 26s12 21.667 12 35c0 14.667 -3.5 27.167 -10.5 37.5s-16.167 19.5 -27.5 27.5s-24.333 14.833 -39 20.5s-29.334 10.834 -44.001 15.501c-16.667 4.66699 -35.667 10.667 -57 18s-41.5 17 -60.5 29s-35.167 27 -48.5 45
s-20 39.667 -20 65s6 46.333 18 63s27.667 28.667 47 36c-15.333 14.667 -26.333 30.667 -33 48s-10 35.666 -10 54.999c0 42.667 17 77.334 51 104.001s80.667 40 140 40c26 0 51.5 -3 76.5 -9s47.5 -13.667 67.5 -23l-17 -56c-17.333 9.33301 -35.666 16.833 -54.999 22.5
s-40.666 8.5 -63.999 8.5c-44 0 -76.167 -8.33301 -96.5 -25s-30.5 -37.334 -30.5 -62.001c0 -13.333 3.33301 -25 10 -35s15.5 -18.667 26.5 -26s23.5 -13.833 37.5 -19.5s28.667 -11.167 44 -16.5l61.5 -22c21.667 -8 41.667 -18 60 -30s33.5 -26.5 45.5 -43.5
s18 -37.5 18 -61.5c0 -24.667 -6.16699 -45.167 -18.5 -61.5s-28.5 -28.166 -48.5 -35.499c17.333 -15.333 29.666 -32.666 36.999 -51.999s11 -38.333 11 -57c0 -22 -5.16699 -41.667 -15.5 -59s-24.833 -32 -43.5 -44s-40.834 -21.167 -66.501 -27.5
s-53.834 -9.5 -84.501 -9.5c-32.667 0 -63 3.16699 -91 9.5s-52 14.5 -72 24.5l17 55c19.333 -9.33301 40.333 -17 63 -23s48.334 -9 77.001 -9z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="295" 
d="M4 614c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM196 614c0 12.667 4.16699 23.5 12.5 32.5
s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="688" 
d="M40 337c0 43.333 8.16699 84 24.5 122s38.333 71.167 66 99.5s59.834 50.666 96.501 66.999s75.667 24.5 117 24.5s80.333 -8.16699 117 -24.5s68.834 -38.666 96.501 -66.999s49.667 -61.5 66 -99.5s24.5 -78.667 24.5 -122s-8.16699 -84 -24.5 -122
s-38.333 -71.167 -66 -99.5s-59.834 -50.666 -96.501 -66.999s-75.667 -24.5 -117 -24.5s-80.333 8.16699 -117 24.5s-68.834 38.666 -96.501 66.999s-49.667 61.5 -66 99.5s-24.5 78.667 -24.5 122zM602 337c0 37.333 -6.5 72.5 -19.5 105.5s-31 61.833 -54 86.5
s-50.333 44.167 -82 58.5s-65.834 21.5 -102.501 21.5s-70.834 -7.16699 -102.501 -21.5s-59 -33.833 -82 -58.5s-41 -53.5 -54 -86.5s-19.5 -68.167 -19.5 -105.5s6.5 -72.5 19.5 -105.5s31 -61.833 54 -86.5s50.333 -44.167 82 -58.5s65.834 -21.5 102.501 -21.5
s70.834 7.16699 102.501 21.5s59 33.833 82 58.5s41 53.5 54 86.5s19.5 68.167 19.5 105.5zM358 471c-39.333 0 -68.001 -12.833 -86.001 -38.5s-27 -57.834 -27 -96.501s9 -70.834 27 -96.501s47.333 -38.5 88 -38.5c29.333 0 58 6.66699 86 20l12 -40
c-15.333 -7.33301 -31 -13 -47 -17s-34.667 -6 -56 -6c-28 0 -52.333 4.66699 -73 14s-37.834 22 -51.501 38s-23.667 34.833 -30 56.5s-9.5 44.834 -9.5 69.501s3.33301 47.834 10 69.501s16.834 40.5 30.501 56.5s31 28.667 52 38s45.833 14 74.5 14
c36.667 0 69.334 -7.33301 98.001 -22l-13 -40c-26.667 12.667 -55 19 -85 19z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="345" 
d="M241 473l-70 -0.000976562c-30 0 -52.833 -6.33301 -68.5 -19s-23.5 -30.667 -23.5 -54c0 -17.333 5.33301 -31.166 16 -41.499s24.667 -15.5 42 -15.5c15.333 0 29.5 3 42.5 9s24 13.833 33 23.5s16 20.667 21 33s7.5 24.833 7.5 37.5v27zM241 529.999
c0 26.667 -7.33398 46.333 -22.001 59s-33.667 19 -57 19c-19.333 0 -38.166 -2.33301 -56.499 -7s-35.5 -11.334 -51.5 -20.001l-16 47c14 7.33301 32 14.5 54 21.5s46.667 10.5 74 10.5c46 0 80.167 -11.833 102.5 -35.5s33.5 -55.5 33.5 -95.5v-229h-50l-9 50
c-11.333 -15.333 -25.5 -28.666 -42.5 -39.999s-41.5 -17 -73.5 -17c-31.333 0 -57.833 9 -79.5 27s-32.5 44.667 -32.5 80c0 40.667 14 70.667 42 90s66 29 114 29h70v11z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="451" 
d="M277 242l129 -180h-61l-130 180l130 179h61zM102 242l129 -180h-61l-130 180l130 179h61z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="496" 
d="M379 124v179h-339v63h406v-242h-67z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="295" 
d="M10 279h275v-60h-275v60z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="529" 
d="M40 460c0 31.333 5.5 60.666 16.5 87.999s26.333 51 46 71s43.167 35.833 70.5 47.5s57.666 17.5 90.999 17.5s63.833 -5.83301 91.5 -17.5s51.334 -27.5 71.001 -47.5s35 -43.667 46 -71s16.5 -56.666 16.5 -87.999s-5.5 -60.666 -16.5 -87.999
s-26.333 -51.166 -46 -71.499s-43.334 -36.333 -71.001 -48s-58.167 -17.5 -91.5 -17.5s-63.666 5.83301 -90.999 17.5s-50.833 27.667 -70.5 48s-35 44.166 -46 71.499s-16.5 56.666 -16.5 87.999zM450 460c0 26 -4.5 50.5 -13.5 73.5s-21.667 43.333 -38 61
s-35.833 31.5 -58.5 41.5s-48 15 -76 15c-27.333 0 -52.333 -5 -75 -15s-42.167 -23.833 -58.5 -41.5s-29 -38 -38 -61s-13.5 -47.5 -13.5 -73.5s4.5 -50.667 13.5 -74s21.667 -43.666 38 -60.999s35.833 -31 58.5 -41s47.667 -15 75 -15c28 0 53.333 5 76 15
s42.167 23.667 58.5 41s29 37.666 38 60.999s13.5 48 13.5 74zM367 508c0 -18 -5.33301 -33.668 -16 -47.001s-25 -22.666 -43 -27.999l60 -101h-41l-58 97h-45v-97h-38v249h93c32 0 54.667 -7 68 -21s20 -31.333 20 -52zM329 507.999c0 12.667 -4 22.333 -12 29
s-21 10 -39 10h-54v-83h53c34.667 0 52 14.667 52 44z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="295" 
d="M11 574v52h273v-52h-273z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="351" 
d="M82 553c0 -12.667 2.33594 -24.667 7.00293 -36s11.167 -21.333 19.5 -30s18.166 -15.5 29.499 -20.5s23.666 -7.5 36.999 -7.5c14 0 26.833 2.5 38.5 7.5s21.667 11.833 30 20.5s15 18.667 20 30s7.5 23.333 7.5 36c0 25.333 -9 46.833 -27 64.5s-41 26.5 -69 26.5
c-26.667 0 -48.834 -8.83301 -66.501 -26.5s-26.5 -39.167 -26.5 -64.5zM40.0029 553c0 18 3.5 35 10.5 51s16.5 29.833 28.5 41.5s26.167 21 42.5 28s34.166 10.5 53.499 10.5s37.333 -3.5 54 -10.5s31 -16.333 43 -28s21.5 -25.5 28.5 -41.5s10.5 -33 10.5 -51
s-3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.333 -21.5 -43 -28.5s-34.667 -10.5 -54 -10.5s-37.166 3.5 -53.499 10.5s-30.5 16.5 -42.5 28.5s-21.5 26 -28.5 42s-10.5 33 -10.5 51z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="566" 
d="M317 496v-174h178v-60h-178v-174h-67v174h-179v60h179v174h67zM495 50v-50h-424v50h424z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="307" 
d="M142 609c-12.667 0 -26.8301 -2.33105 -42.4971 -6.99805s-30.167 -10.334 -43.5 -17.001l-16 47c13.333 6.66699 29.166 12.834 47.499 18.501s37.166 8.5 56.499 8.5c40 0 69.667 -8.5 89 -25.5s29 -40.5 29 -70.5c0 -15.333 -3.5 -30.333 -10.5 -45
s-15.833 -28.834 -26.5 -42.501s-22.334 -26.834 -35.001 -39.501l-35 -35l-48 -48h165v-53h-245v40l91 93c24 24 43.833 46.333 59.5 67s23.5 41.667 23.5 63c0 15.333 -5.16699 26.833 -15.5 34.5s-24.833 11.5 -43.5 11.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="300" 
d="M132 344c24.667 0 42.8301 5.83398 54.4971 17.501s17.5 25.834 17.5 42.501c0 18 -6 31.833 -18 41.5s-28 14.5 -48 14.5h-54v48h54c16 0 29.333 4.5 40 13.5s16 23.167 16 42.5c0 13.333 -5 24.333 -15 33s-27.667 13 -53 13c-15.333 0 -29.833 -1.83301 -43.5 -5.5
s-26.834 -8.83398 -39.501 -15.501l-16 47c14 7.33301 29.833 13 47.5 17s35.5 6 53.5 6c44.667 0 76.834 -9.33301 96.501 -28s29.5 -41 29.5 -67c0 -20 -5.16699 -36.333 -15.5 -49s-23.166 -22 -38.499 -28c21.333 -4.66699 37.333 -14.834 48 -30.501
s16 -33.167 16 -52.5c0 -33.333 -11.167 -59.833 -33.5 -79.5s-54.833 -29.5 -97.5 -29.5c-22.667 0 -42.667 2.5 -60 7.5s-32.666 11.167 -45.999 18.5l15 46c12.667 -6.66699 26.834 -12.167 42.501 -16.5s31.5 -6.5 47.5 -6.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="295" 
d="M263 700l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="556" 
d="M76 483l72.999 -0.000976562v-306c0 -40 8.83301 -70.333 26.5 -91s42.5 -31 74.5 -31c17.333 0 33.666 2.83301 48.999 8.5s29.5 13 42.5 22s24.833 19.333 35.5 31s20 23.834 28 36.501v330h75v-483h-60l-15 85c-6 -10.667 -14.167 -21.5 -24.5 -32.5s-22 -21 -35 -30
s-27.667 -16.5 -44 -22.5s-33.5 -9 -51.5 -9c-25.333 0 -46.166 3.5 -62.499 10.5s-29.166 17.167 -38.499 30.5v-232h-73v683z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="442" 
d="M42 500c0 27.333 4.66602 51.835 13.999 73.502s22 40 38 55s34.833 26.333 56.5 34s44.834 11.5 69.501 11.5h192v-48h-36c-27.333 0 -45.666 -5.5 -54.999 -16.5s-14 -28.167 -14 -51.5v-606h-68v377h-29c-21.333 0 -42 3.66699 -62 11s-37.833 18.166 -53.5 32.499
s-28.334 32.166 -38.001 53.499s-14.5 46 -14.5 74z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="236" 
d="M62 279c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="295" 
d="M134 -164h-80l80 125h47z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="285" 
d="M118 599l-72 -23v49l87 30h45v-303h80v-52h-223v52h83v247z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="394" 
d="M94 477c0 -18.667 2 -36 6 -52s10.167 -30 18.5 -42s19 -21.333 32 -28s28.5 -10 46.5 -10s33.5 3.33301 46.5 10s23.833 16 32.5 28s15 26 19 42s6 33.333 6 52c0 38 -8.33301 69.167 -25 93.5s-43 36.5 -79 36.5s-62.167 -12.167 -78.5 -36.5s-24.5 -55.5 -24.5 -93.5z
M30 477c0 25.333 3.33301 49.166 10 71.499s16.834 41.666 30.501 57.999s31 29.166 52 38.499s45.833 14 74.5 14s53.5 -4.66699 74.5 -14s38.333 -22.166 52 -38.499s23.834 -35.666 30.501 -57.999s10 -46.166 10 -71.499c0 -26 -3.33301 -50 -10 -72
s-16.834 -41.333 -30.501 -58s-31 -29.667 -52 -39s-45.833 -14 -74.5 -14s-53.5 4.66699 -74.5 14s-38.333 22.333 -52 39s-23.834 36 -30.501 58s-10 46 -10 72z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="451" 
d="M45 421h61l130 -179l-130 -180h-61l129 180zM220 421h61l130 -179l-130 -180h-61l129 180z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="760" 
d="M532 660h63l-366 -660h-63zM136 599l-72 -23v49l86 30h46v-303h80v-52h-223v52h83v247zM583 120v141l-104 -141h104zM590 355h49v-235h40v-53h-40v-67h-57v67h-167v43z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="760" 
d="M532 660h63l-366 -660h-63zM136 599l-72 -23v49l86 30h46v-303h80v-52h-223v52h83v247zM581 309c-12.667 0 -26.8301 -2.33105 -42.4971 -6.99805s-30.167 -10.334 -43.5 -17.001l-16 47c13.333 6.66699 29.166 12.834 47.499 18.501s37.166 8.5 56.499 8.5
c40 0 69.667 -8.5 89 -25.5s29 -40.5 29 -70.5c0 -15.333 -3.5 -30.333 -10.5 -45s-15.833 -28.834 -26.5 -42.501s-22.334 -26.834 -35.001 -39.501l-35 -35l-48 -48h165v-53h-245v40l91 93c24 24 43.833 46.333 59.5 67s23.5 41.667 23.5 63
c0 15.333 -5.16699 26.833 -15.5 34.5s-24.833 11.5 -43.5 11.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="760" 
d="M532 660h63l-366 -660h-63zM583 120v141l-104 -141h104zM590 355h49v-235h40v-53h-40v-67h-57v67h-167v43zM148 344c25.333 0 43.834 5.83398 55.501 17.501s17.5 25.834 17.5 42.501c0 18 -6 31.833 -18 41.5s-28.333 14.5 -49 14.5h-53v48h53
c16.667 0 30.334 4.5 41.001 13.5s16 23.167 16 42.5c0 13.333 -5 24.333 -15 33s-27.667 13 -53 13c-15.333 0 -29.833 -1.83301 -43.5 -5.5s-26.834 -8.83398 -39.501 -15.501l-16 47c14 7.33301 29.667 13 47 17s35.333 6 54 6c44.667 0 76.667 -9.33301 96 -28
s29 -41 29 -67c0 -20 -5 -36.333 -15 -49s-23 -22 -39 -28c22 -4.66699 38.167 -14.834 48.5 -30.501s15.5 -33.167 15.5 -52.5c0 -33.333 -11.167 -59.833 -33.5 -79.5s-54.5 -29.5 -96.5 -29.5c-22.667 0 -42.834 2.5 -60.501 7.5s-33.167 11.167 -46.5 18.5l16 46
c12 -6.66699 26 -12.167 42 -16.5s31.667 -6.5 47 -6.5z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="488" 
d="M263 -133c28 0 55.999 3.83301 83.999 11.5s52.333 16.834 73 27.501l19 -63c-20.667 -10.667 -46.334 -20.667 -77.001 -30s-64 -14 -100 -14c-68 0 -118.667 15 -152 45s-50 70.333 -50 121c0 25.333 4.33301 48 13 68s19.667 38.333 33 55s27.5 32.167 42.5 46.5
l42.5 42.5c13.333 14 24.333 28.5 33 43.5s13 31.5 13 49.5c0 20 -3 37.667 -9 53h62c4.66699 -7.33301 8.16699 -16 10.5 -26s3.5 -20 3.5 -30c0 -21.333 -4.16699 -40.666 -12.5 -57.999s-18.666 -33.333 -30.999 -48s-25.833 -28.834 -40.5 -42.501
s-28.167 -28 -40.5 -43s-22.666 -30.833 -30.999 -47.5s-12.5 -35.667 -12.5 -57c0 -14 2.33301 -27.167 7 -39.5s12.167 -23.333 22.5 -33s23.5 -17.334 39.5 -23.001s35.333 -8.5 58 -8.5zM314.999 437c0 -15.333 -4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16
s-31.833 5.33301 -41.5 16s-14.5 23.667 -14.5 39s4.83301 28.333 14.5 39s23.5 16 41.5 16s31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM404 772l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM363 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM452 742h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM460 853c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5
s-18.5 11.5 -27.5 16.5s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5
c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM167 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5
s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM359 810c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5
s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="622" 
d="M454 185h-287l-67 -185h-78l251 674h76l251 -674h-78zM193 253h236l-74 200l-21.5 67.5l-21.5 67.5h-2l-21.5 -67.5l-21.5 -67.5zM263 829c0 -15.333 4.66699 -27.666 14 -36.999s20.666 -14 33.999 -14s24.833 4.66699 34.5 14s14.5 21.666 14.5 36.999
s-4.66699 27.833 -14 37.5s-21 14.5 -35 14.5c-13.333 0 -24.666 -4.83301 -33.999 -14.5s-14 -22.167 -14 -37.5zM219 828c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18.166 15 29.499 20s23.333 7.5 36 7.5s24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20
s14.833 -18.166 19.5 -29.499s7 -23.333 7 -36s-2.33301 -24.5 -7 -35.5s-11.167 -20.667 -19.5 -29s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7s-24.667 2.33301 -36 7s-21.166 11.167 -29.499 19.5s-14.833 18 -19.5 29s-7 22.833 -7 35.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="816" 
d="M464 185h-287l-40 -92.5l-40 -92.5h-77l298 674h442v-70h-345l66 -222h259v-69h-239l72 -244h193v-69h-247zM208 253h237l-61 200l-33 135h-2l-26 -67.5l-27 -67.5z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="570" 
d="M365 615c-76.667 0 -136.499 -24.001 -179.499 -72.001s-64.5 -116.667 -64.5 -206c0 -88 21.333 -156.167 64 -204.5s102.334 -72.5 179.001 -72.5c54.667 0 104 10.667 148 32l19 -64c-24 -12 -49.5 -21.167 -76.5 -27.5s-57.5 -9.5 -91.5 -9.5
c-54.667 0 -101.834 8.33301 -141.501 25s-72.5 40 -98.5 70s-45.333 66.333 -58 109s-19 90 -19 142c0 50.667 7 97.334 21 140.001s34.5 79.167 61.5 109.5s60.167 54 99.5 71s84.666 25.5 135.999 25.5c34.667 0 64.667 -2.83301 90 -8.5s49 -13.5 71 -23.5l-19 -64
c-40.667 18.667 -87.667 28 -141 28zM331.001 -164.001h-80l80 125h47z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="545" 
d="M82 674h407v-69h-331v-222h311v-70h-311v-244h337v-69h-413v674zM385 772l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="545" 
d="M82 674h407v-69h-331v-222h311v-70h-311v-244h337v-69h-413v674zM344 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="545" 
d="M82 674h407v-69h-331v-222h311v-70h-311v-244h337v-69h-413v674zM434 742h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="545" 
d="M82 674h407v-69h-331v-222h311v-70h-311v-244h337v-69h-413v674zM149 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5
s-12 19.833 -12 32.5zM341 810c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="244" 
d="M160 0h-76v674h76v-674zM215 772l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="244" 
d="M160 0h-76v674h76v-674zM175 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="244" 
d="M160 0h-76v674h76v-674zM265 742h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="244" 
d="M160 0h-76v674h76v-674zM-20 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM172 810
c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="650" 
d="M82 360v314.001h188c110 0 193 -28.333 249 -85s84 -140.334 84 -251.001s-28 -194.667 -84 -252s-139 -86 -249 -86h-188v301h-69v59h69zM527 338.001c0 47.333 -5.66699 87.833 -17 121.5s-27.833 61.167 -49.5 82.5s-48.5 37 -80.5 47s-68.667 15 -110 15h-112v-244
h178v-59h-178v-231h112c41.333 0 78 5 110 15s58.833 25.833 80.5 47.5s38.167 49.5 49.5 83.5s17 74.667 17 122z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="663" 
d="M447 218c13.333 -21.333 25.5 -42.168 36.5 -62.501l27.5 -52.5h2c-3.33301 22.667 -5.5 45.167 -6.5 67.5s-1.5 47.166 -1.5 74.499v429h76v-674h-81l-284 456c-13.333 20.667 -25.5 41.334 -36.5 62.001l-27.5 53h-2c3.33301 -22.667 5.5 -45.167 6.5 -67.5
s1.5 -47.166 1.5 -74.499v-429h-76v674h81zM481 852.998c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5
s-8 -19.5 -8 -31.5h-52c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26zM437 772l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26zM396 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26zM485 742h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26zM493 853c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5
s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5
c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="688" 
d="M344 54c37.333 0 69.833 6.5 97.5 19.5s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118s-4.5 83.333 -13.5 118s-22.833 64.334 -41.5 89.001s-41.834 43.5 -69.501 56.5s-60.167 19.5 -97.5 19.5s-70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5
s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118s4.5 -83.333 13.5 -118s22.833 -64.334 41.5 -89.001s42 -43.5 70 -56.5s60.667 -19.5 98 -19.5zM344 -9c-51.333 0 -95.666 8.66699 -132.999 26s-68.333 41.5 -93 72.5s-42.834 67.667 -54.501 110
s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26s95.666 -8.66699 132.999 -26s68.333 -41.5 93 -72.5s42.834 -67.667 54.501 -110s17.5 -88.166 17.5 -137.499
s-5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26zM200 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5
s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM392 810c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5
s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="566" 
d="M428 88l-149 148l-148 -148l-47 47l148 148l-149 149l48 47l148 -148l149 148l47 -47l-149 -149l149 -148z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="688" 
d="M543 614c34 -32 59.001 -71.501 75.001 -118.501s24 -99.833 24 -158.5c0 -49.333 -5.83301 -95.166 -17.5 -137.499s-29.834 -79 -54.501 -110s-55.667 -55.167 -93 -72.5s-81.666 -26 -132.999 -26c-32 0 -61 3.33301 -87 10s-49.667 16.334 -71 29.001l-43 -70h-59
l61 99c-34 32 -59 71.667 -75 119s-24 100.333 -24 159c0 49.333 5.83301 95.166 17.5 137.499s29.834 79 54.501 110s55.667 55.167 93 72.5s81.666 26 132.999 26c61.333 0 113.666 -13.333 156.999 -40l44 71h59zM121.001 336.999c0 -46.667 5 -88.166 15 -124.499
s25.667 -66.833 47 -91.5l283 465c-16 10.667 -34.167 19 -54.5 25s-42.833 9 -67.5 9c-37.333 0 -70 -6.5 -98 -19.5s-51.333 -31.833 -70 -56.5s-32.5 -54.334 -41.5 -89.001s-13.5 -74 -13.5 -118zM344.001 54c37.333 0 69.832 6.5 97.499 19.5
s50.834 31.833 69.501 56.5s32.5 54.334 41.5 89.001s13.5 74 13.5 118c0 93.333 -20.667 164.666 -62 213.999l-283 -463c32.667 -22.667 73.667 -34 123 -34z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="664" 
d="M332 -9c-38.667 0 -73.5 5.5 -104.5 16.5s-57.5 28 -79.5 51s-38.833 51.833 -50.5 86.5s-17.5 76 -17.5 124v405h76v-406c0 -70.667 15.167 -122.834 45.5 -156.501s73.833 -50.5 130.5 -50.5s100.167 16.833 130.5 50.5s45.5 85.834 45.5 156.501v406h76v-405
c0 -48 -5.83301 -89.333 -17.5 -124s-28.5 -63.5 -50.5 -86.5s-48.5 -40 -79.5 -51s-65.833 -16.5 -104.5 -16.5zM425 772l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="664" 
d="M332 -9c-38.667 0 -73.5 5.5 -104.5 16.5s-57.5 28 -79.5 51s-38.833 51.833 -50.5 86.5s-17.5 76 -17.5 124v405h76v-406c0 -70.667 15.167 -122.834 45.5 -156.501s73.833 -50.5 130.5 -50.5s100.167 16.833 130.5 50.5s45.5 85.834 45.5 156.501v406h76v-405
c0 -48 -5.83301 -89.333 -17.5 -124s-28.5 -63.5 -50.5 -86.5s-48.5 -40 -79.5 -51s-65.833 -16.5 -104.5 -16.5zM384 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="664" 
d="M332 -9c-38.667 0 -73.5 5.5 -104.5 16.5s-57.5 28 -79.5 51s-38.833 51.833 -50.5 86.5s-17.5 76 -17.5 124v405h76v-406c0 -70.667 15.167 -122.834 45.5 -156.501s73.833 -50.5 130.5 -50.5s100.167 16.833 130.5 50.5s45.5 85.834 45.5 156.501v406h76v-405
c0 -48 -5.83301 -89.333 -17.5 -124s-28.5 -63.5 -50.5 -86.5s-48.5 -40 -79.5 -51s-65.833 -16.5 -104.5 -16.5zM473 742h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="664" 
d="M332 -9c-38.667 0 -73.5 5.5 -104.5 16.5s-57.5 28 -79.5 51s-38.833 51.833 -50.5 86.5s-17.5 76 -17.5 124v405h76v-406c0 -70.667 15.167 -122.834 45.5 -156.501s73.833 -50.5 130.5 -50.5s100.167 16.833 130.5 50.5s45.5 85.834 45.5 156.501v406h76v-405
c0 -48 -5.83301 -89.333 -17.5 -124s-28.5 -63.5 -50.5 -86.5s-48.5 -40 -79.5 -51s-65.833 -16.5 -104.5 -16.5zM188 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5
s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM380 810c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5
s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="563" 
d="M319 285v-285h-76v285l-227 389h80l184 -324h2l183 324h81zM333 896l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="564" 
d="M520 344c0 -30.667 -5.33398 -58.668 -16.001 -84.001s-25.834 -47.166 -45.501 -65.499s-43.5 -32.5 -71.5 -42.5s-59.333 -15 -94 -15h-135v-137h-76v674h76v-127h141c34.667 0 65.834 -5 93.501 -15s50.834 -24 69.501 -42s33 -39.5 43 -64.5s15 -52.167 15 -81.5z
M443.999 343.999c0 42.667 -12.333 75.5 -37 98.5s-60.667 34.5 -108 34.5h-141v-270h135c47.333 0 84.333 11.833 111 35.5s40 57.5 40 101.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="597" 
d="M345 49c38.667 0 70.001 12.502 94.001 37.502s36 60.5 36 106.5c0 55.333 -14.667 96 -44 122s-69 39 -119 39h-52v61h68c33.333 0 61 7.33301 83 22s33 37.667 33 69c0 20 -3.66699 37 -11 51s-17 25.5 -29 34.5s-25.5 15.667 -40.5 20s-30.167 6.5 -45.5 6.5
c-58.667 0 -101.667 -16.833 -129 -50.5s-41 -79.834 -41 -138.501v-429h-73v429c0 36.667 5 70.5 15 101.5s25 57.833 45 80.5s45.167 40.334 75.5 53.001s65.833 19 106.5 19c28.667 0 55.334 -4 80.001 -12s45.667 -19.333 63 -34s31 -32.334 41 -53.001
s15 -43.667 15 -69c0 -18 -3.33301 -34.5 -10 -49.5s-15.334 -28 -26.001 -39s-22.834 -20 -36.501 -27s-27.5 -12.167 -41.5 -15.5c18.667 -3.33301 37 -9.16602 55 -17.499s33.667 -20 47 -35s24.166 -33.833 32.499 -56.5s12.5 -50.334 12.5 -83.001
c0 -33.333 -5.16699 -62.5 -15.5 -87.5s-24.5 -46 -42.5 -63s-39.167 -29.667 -63.5 -38s-50.5 -12.5 -78.5 -12.5c-58 0 -105.667 9.33301 -143 28l16 57c18 -8.66699 37.333 -15.334 58 -20.001s42.334 -7 65.001 -7z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM344.001 575.999l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM303.001 699.999l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM393.001 545.999h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM401.001 656.999c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52
c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM108.001 613.999c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM300.001 613.999
c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="503" 
d="M358 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.33301 63.5 13s35.333 20.167 49 34.5s24.167 30.333 31.5 48s11 35.5 11 53.5v43zM358 318
c0 40.667 -10.332 69.833 -30.999 87.5s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.5 -82.5 -10.5s-47.833 -15.5 -68.5 -25.5l-17 53c18 10.667 41.833 20.5 71.5 29.5s62.5 13.5 98.5 13.5c62.667 0 109 -15.667 139 -47s45 -74 45 -128v-317h-56l-15 85
c-7.33301 -12.667 -15.833 -24.667 -25.5 -36s-21 -21.333 -34 -30s-28.167 -15.5 -45.5 -20.5s-38 -7.5 -62 -7.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5
h104v21zM203.001 632.999c0 -15.333 4.66699 -27.666 14 -36.999s20.666 -14 33.999 -14s24.833 4.66699 34.5 14s14.5 21.666 14.5 36.999s-4.66699 27.833 -14 37.5s-21 14.5 -35 14.5c-13.333 0 -24.666 -4.83301 -33.999 -14.5s-14 -22.167 -14 -37.5zM159.001 631.999
c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18.166 15 29.499 20s23.333 7.5 36 7.5s24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s14.833 -18.166 19.5 -29.499s7 -23.333 7 -36s-2.33301 -24.5 -7 -35.5s-11.167 -20.667 -19.5 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7s-24.667 2.33301 -36 7s-21.166 11.167 -29.499 19.5s-14.833 18 -19.5 29s-7 22.833 -7 35.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="796" 
d="M426 208c4.66699 -50 19.5049 -88.667 44.5049 -116s63.5 -41 115.5 -41c42 0 82.333 9.33301 121 28l17 -55c-20.667 -10.667 -42.334 -18.834 -65.001 -24.501s-49.334 -8.5 -80.001 -8.5c-52.667 0 -93.167 10.5 -121.5 31.5s-49.833 50.5 -64.5 88.5
c-8 -16.667 -18.333 -32.167 -31 -46.5s-27.334 -27 -44.001 -38s-35.667 -19.667 -57 -26s-45 -9.5 -71 -9.5c-20.667 0 -40 3.16699 -58 9.5s-33.833 15.5 -47.5 27.5s-24.334 27 -32.001 45s-11.5 38.667 -11.5 62c0 54 18.667 94.5 56 121.5s89.333 40.5 156 40.5h104
v21c0 40.667 -10.333 69.834 -31 87.501s-48.667 26.5 -84 26.5c-30 0 -57.5 -3.16699 -82.5 -9.5s-47.833 -14.5 -68.5 -24.5l-17 53c18 10.667 41.833 20.167 71.5 28.5s62.5 12.5 98.5 12.5c41.333 0 74.333 -7.16699 99 -21.5s43.667 -34.5 57 -60.5
c15.333 25.333 37 45.333 65 60s61.667 22 101 22c61.333 0 107.166 -17.833 137.499 -53.5s45.5 -84.5 45.5 -146.5c0 -16 -0.833008 -31 -2.5 -45s-4.16699 -27 -7.5 -39h-313zM357.005 241h-105c-46 0 -80.667 -9.16699 -104 -27.5s-35 -44.5 -35 -78.5
c0 -28 8.5 -49.333 25.5 -64s38.167 -22 63.5 -22c23.333 0 44.5 4.16699 63.5 12.5s35.333 19.333 49 33s24.167 29.5 31.5 47.5s11 36.667 11 56v43zM566.005 435c-25.333 0 -46.501 -4.33301 -63.501 -13s-30.833 -20.667 -41.5 -36s-18.667 -33.5 -24 -54.5
s-8.66602 -43.833 -9.99902 -68.5h254c0 1.33301 0.166992 6 0.5 14s0.5 13 0.5 15c0 46 -8.66699 81.333 -26 106s-47.333 37 -90 37z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="457" 
d="M277 432c-27.333 0 -51.165 -4.6709 -71.498 -14.0039s-37 -22.5 -50 -39.5s-22.667 -37.167 -29 -60.5s-9.5 -48.666 -9.5 -75.999s3.16699 -52.666 9.5 -75.999s16.166 -43.5 29.499 -60.5s30.333 -30.333 51 -40s45.334 -14.5 74.001 -14.5
c20.667 0 41.5 2.5 62.5 7.5s40.833 12.167 59.5 21.5l17 -55c-20.667 -10.667 -42.5 -19 -65.5 -25s-49.5 -9 -79.5 -9c-39.333 0 -73.333 6.33301 -102 19s-52.667 30.334 -72 53.001s-33.666 49.334 -42.999 80.001s-14 63.667 -14 99s4.66699 68.333 14 99
s23.666 57.167 42.999 79.5s43.666 39.833 72.999 52.5s63.666 19 102.999 19c27.333 0 52.333 -2.83301 75 -8.5s43.667 -13.167 63 -22.5l-17 -56c-17.333 8 -36 14.5 -56 19.5s-41.667 7.5 -65 7.5zM245.001 -164.004h-80l80 125h47z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="501" 
d="M117 208c4 -50 19.8311 -88.666 47.498 -115.999s67.5 -41 119.5 -41c22 0 44.333 2.33301 67 7s43 11.667 61 21l18 -55c-20.667 -10.667 -43.5 -18.834 -68.5 -24.501s-52.833 -8.5 -83.5 -8.5c-39.333 0 -73.5 6 -102.5 18s-53 28.833 -72 50.5s-33.333 48 -43 79
s-14.5 65.5 -14.5 103.5c0 35.333 4.33301 68.333 13 99s21.834 57.167 39.501 79.5s40.334 39.833 68.001 52.5s60.5 19 98.5 19c62.667 0 110 -17.833 142 -53.5s48 -84.5 48 -146.5c0 -16 -1 -31 -3 -45s-4.66699 -27 -8 -39h-327zM264.998 435.001
c-25.333 0 -47 -4.33301 -65 -13s-33 -20.667 -45 -36s-21 -33.5 -27 -54.5s-9.66699 -43.833 -11 -68.5h268c0.666992 1.33301 1 6 1 14v15c0 46 -9.33301 81.333 -28 106s-49.667 37 -93 37zM342.998 576.001l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="501" 
d="M117 208c4 -50 19.8311 -88.666 47.498 -115.999s67.5 -41 119.5 -41c22 0 44.333 2.33301 67 7s43 11.667 61 21l18 -55c-20.667 -10.667 -43.5 -18.834 -68.5 -24.501s-52.833 -8.5 -83.5 -8.5c-39.333 0 -73.5 6 -102.5 18s-53 28.833 -72 50.5s-33.333 48 -43 79
s-14.5 65.5 -14.5 103.5c0 35.333 4.33301 68.333 13 99s21.834 57.167 39.501 79.5s40.334 39.833 68.001 52.5s60.5 19 98.5 19c62.667 0 110 -17.833 142 -53.5s48 -84.5 48 -146.5c0 -16 -1 -31 -3 -45s-4.66699 -27 -8 -39h-327zM264.998 435.001
c-25.333 0 -47 -4.33301 -65 -13s-33 -20.667 -45 -36s-21 -33.5 -27 -54.5s-9.66699 -43.833 -11 -68.5h268c0.666992 1.33301 1 6 1 14v15c0 46 -9.33301 81.333 -28 106s-49.667 37 -93 37zM301.998 700.001l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="501" 
d="M117 208c4 -50 19.8311 -88.666 47.498 -115.999s67.5 -41 119.5 -41c22 0 44.333 2.33301 67 7s43 11.667 61 21l18 -55c-20.667 -10.667 -43.5 -18.834 -68.5 -24.501s-52.833 -8.5 -83.5 -8.5c-39.333 0 -73.5 6 -102.5 18s-53 28.833 -72 50.5s-33.333 48 -43 79
s-14.5 65.5 -14.5 103.5c0 35.333 4.33301 68.333 13 99s21.834 57.167 39.501 79.5s40.334 39.833 68.001 52.5s60.5 19 98.5 19c62.667 0 110 -17.833 142 -53.5s48 -84.5 48 -146.5c0 -16 -1 -31 -3 -45s-4.66699 -27 -8 -39h-327zM264.998 435.001
c-25.333 0 -47 -4.33301 -65 -13s-33 -20.667 -45 -36s-21 -33.5 -27 -54.5s-9.66699 -43.833 -11 -68.5h268c0.666992 1.33301 1 6 1 14v15c0 46 -9.33301 81.333 -28 106s-49.667 37 -93 37zM391.998 546.001h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="501" 
d="M117 208c4 -50 19.8311 -88.666 47.498 -115.999s67.5 -41 119.5 -41c22 0 44.333 2.33301 67 7s43 11.667 61 21l18 -55c-20.667 -10.667 -43.5 -18.834 -68.5 -24.501s-52.833 -8.5 -83.5 -8.5c-39.333 0 -73.5 6 -102.5 18s-53 28.833 -72 50.5s-33.333 48 -43 79
s-14.5 65.5 -14.5 103.5c0 35.333 4.33301 68.333 13 99s21.834 57.167 39.501 79.5s40.334 39.833 68.001 52.5s60.5 19 98.5 19c62.667 0 110 -17.833 142 -53.5s48 -84.5 48 -146.5c0 -16 -1 -31 -3 -45s-4.66699 -27 -8 -39h-327zM264.998 435.001
c-25.333 0 -47 -4.33301 -65 -13s-33 -20.667 -45 -36s-21 -33.5 -27 -54.5s-9.66699 -43.833 -11 -68.5h268c0.666992 1.33301 1 6 1 14v15c0 46 -9.33301 81.333 -28 106s-49.667 37 -93 37zM106.998 614.001c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5
s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM298.998 614.001c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5
s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483zM210 576l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483zM170 700l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483zM260 546h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483zM-25 614c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM167 614
c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="550" 
d="M187 522l-17.002 39l94 44c-16.667 12 -34.167 22.667 -52.5 32s-37.166 17.333 -56.499 24l24 47c50.667 -16.667 98 -42 142 -76l111 53l20 -38l-95 -45c44 -42 79.5 -92.333 106.5 -151s40.5 -125 40.5 -199c0 -36 -5.16699 -70 -15.5 -102s-25.5 -59.667 -45.5 -83
s-44.5 -41.833 -73.5 -55.5s-61.833 -20.5 -98.5 -20.5c-35.333 0 -67.166 6.5 -95.499 19.5s-52.333 30.667 -72 53s-34.834 48.333 -45.501 78s-16 61.167 -16 94.5c0 34.667 5.33301 67.5 16 98.5s25.834 58.167 45.501 81.5s43.334 41.833 71.001 55.5
s58.167 20.5 91.5 20.5c31.333 0 58.333 -5 81 -15s42.667 -24 60 -42h3c-8.66699 24.667 -22.334 49 -41.001 73s-40.667 46.667 -66 68zM431.998 242c0 25.333 -2.99805 49.498 -8.99805 72.498s-15.5 43.333 -28.5 61s-29.5 31.834 -49.5 42.501s-44 16 -72 16
c-26 0 -49 -5.16699 -69 -15.5s-36.5 -24.5 -49.5 -42.5s-22.833 -38.833 -29.5 -62.5s-10 -48.834 -10 -75.501c0 -26 3.5 -50.333 10.5 -73s17.167 -42.5 30.5 -59.5s29.666 -30.333 48.999 -40s41.333 -14.5 66 -14.5c26 0 49.167 5.83301 69.5 17.5
s37.166 26.834 50.499 45.501s23.5 39.334 30.5 62.001s10.5 44.667 10.5 66z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="560" 
d="M150 0l-74.002 0.000976562v483h60l14 -78c7.33301 9.33301 16.5 19.166 27.5 29.499s23.833 19.666 38.5 27.999s30.834 15.333 48.501 21s36.834 8.5 57.501 8.5c51.333 0 91.5 -16.667 120.5 -50s43.5 -79.666 43.5 -138.999v-303h-73v303c0 38 -9.16699 68 -27.5 90
s-43.5 33 -75.5 33c-34 0 -64.833 -8.5 -92.5 -25.5s-50.167 -37.833 -67.5 -62.5v-338zM428.998 657.001c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5
s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5
c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z
M362 576l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z
M321 700l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z
M410 546h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z
M418 657c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52
c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="538" 
d="M117 242c0 -27.333 3 -52.833 9 -76.5s15.167 -44 27.5 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5s49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5s-3 52.5 -9 75.5s-15.167 43 -27.5 60s-28 30.333 -47 40s-41.833 14.5 -68.5 14.5
s-49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5zM44 242c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19s72 -6.33301 100 -19s51.333 -30.334 70 -53.001
s32.5 -49.167 41.5 -79.5s13.5 -63.166 13.5 -98.499s-4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19s-72.167 6.33301 -100.5 19s-51.666 30.334 -69.999 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99z
M125 614c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM317 614c0 12.667 4.16699 23.5 12.5 32.5
s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="565" 
d="M487 314v-61h-416v61h416zM218 132c0 15.333 5 28.666 15 39.999s24.333 17 43 17c19.333 0 34 -5.66699 44 -17s15 -24.666 15 -39.999s-5 -28.666 -15 -39.999s-24.667 -17 -44 -17c-18.667 0 -33 5.66699 -43 17s-15 24.666 -15 39.999zM218 435c0 16 5 29.667 15 41
s24.333 17 43 17c19.333 0 34 -5.66699 44 -17s15 -25 15 -41c0 -15.333 -5 -28.666 -15 -39.999s-24.667 -17 -44 -17c-18.667 0 -33 5.66699 -43 17s-15 24.666 -15 39.999z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="538" 
d="M405 453c30 -22.667 52.333 -52.3311 67 -88.998s22 -77.334 22 -122.001c0 -35.333 -4.5 -68.333 -13.5 -99s-22.833 -57.334 -41.5 -80.001s-42 -40.334 -70 -53.001s-61.333 -19 -100 -19c-20.667 0 -39.667 1.83301 -57 5.5s-33.666 8.83398 -48.999 15.501l-28 -52
h-52l42 76c-28 23.333 -48.5 52.5 -61.5 87.5s-19.5 74.5 -19.5 118.5c0 35.333 4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.666 40.334 69.999 53.001s61.833 19 100.5 19c36 0 68 -6 96 -18l38 70h52zM421 242.003c0 32 -4 60.667 -12 86
s-20.333 46.666 -37 63.999l-178 -324c10.667 -5.33301 22.167 -9.66602 34.5 -12.999s25.833 -5 40.5 -5c26.667 0 49.5 4.83301 68.5 14.5s34.667 23 47 40s21.5 37.333 27.5 61s9 49.167 9 76.5zM117 242.003c0 -30 3.33301 -57.168 10 -81.501
s17.334 -45.166 32.001 -62.499l175 320c-18.667 9.33301 -40.334 14 -65.001 14c-26.667 0 -49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.5 -37 -27.5 -60s-9 -48.167 -9 -75.5z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="553" 
d="M403 483h73.998v-483h-59l-15 85c-6 -10 -14.167 -20.667 -24.5 -32s-22.666 -21.5 -36.999 -30.5s-30.666 -16.5 -48.999 -22.5s-38.833 -9 -61.5 -9c-46 0 -83.667 15 -113 45s-44 78 -44 144v303h72v-303c0 -39.333 8.66699 -69.833 26 -91.5s41 -32.5 71 -32.5
c18 0 35 2.66699 51 8s30.667 12.5 44 21.5s25.5 19.5 36.5 31.5s20.5 24.333 28.5 37v329zM368.998 576l-23 -32l-163 100l41 56z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="553" 
d="M403 483h73.998v-483h-59l-15 85c-6 -10 -14.167 -20.667 -24.5 -32s-22.666 -21.5 -36.999 -30.5s-30.666 -16.5 -48.999 -22.5s-38.833 -9 -61.5 -9c-46 0 -83.667 15 -113 45s-44 78 -44 144v303h72v-303c0 -39.333 8.66699 -69.833 26 -91.5s41 -32.5 71 -32.5
c18 0 35 2.66699 51 8s30.667 12.5 44 21.5s25.5 19.5 36.5 31.5s20.5 24.333 28.5 37v329zM327.998 700l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="553" 
d="M403 483h73.998v-483h-59l-15 85c-6 -10 -14.167 -20.667 -24.5 -32s-22.666 -21.5 -36.999 -30.5s-30.666 -16.5 -48.999 -22.5s-38.833 -9 -61.5 -9c-46 0 -83.667 15 -113 45s-44 78 -44 144v303h72v-303c0 -39.333 8.66699 -69.833 26 -91.5s41 -32.5 71 -32.5
c18 0 35 2.66699 51 8s30.667 12.5 44 21.5s25.5 19.5 36.5 31.5s20.5 24.333 28.5 37v329zM417.998 546h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="553" 
d="M403 483h73.998v-483h-59l-15 85c-6 -10 -14.167 -20.667 -24.5 -32s-22.666 -21.5 -36.999 -30.5s-30.666 -16.5 -48.999 -22.5s-38.833 -9 -61.5 -9c-46 0 -83.667 15 -113 45s-44 78 -44 144v303h72v-303c0 -39.333 8.66699 -69.833 26 -91.5s41 -32.5 71 -32.5
c18 0 35 2.66699 51 8s30.667 12.5 44 21.5s25.5 19.5 36.5 31.5s20.5 24.333 28.5 37v329zM132.998 614c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5
s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM324.998 614c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="550" 
d="M400 95c-6 -10.667 -13.999 -21.501 -23.999 -32.501s-22 -21.167 -36 -30.5s-29.833 -17 -47.5 -23s-37.167 -9 -58.5 -9c-49.333 0 -88.333 15 -117 45s-43 78 -43 144v294h72v-294c0 -39.333 8.5 -69.833 25.5 -91.5s40.833 -32.5 71.5 -32.5
c18 0 34.833 2.83301 50.5 8.5s30 13 43 22s24.833 19.333 35.5 31s20 23.834 28 36.501v320h74v-447c0 -80 -18.833 -139.5 -56.5 -178.5s-90.834 -58.5 -159.501 -58.5c-32 0 -63 4.5 -93 13.5s-54 19.5 -72 31.5l19 59c18.667 -11.333 40 -20.5 64 -27.5
s50 -10.5 78 -10.5c23.333 0 44 2.83301 62 8.5s33.333 15.167 46 28.5s22.167 31 28.5 53s9.5 49.333 9.5 82v58zM327.001 699.999l41 -56l-163 -100l-23 32z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="587" 
d="M152 230c0 -24.667 3.83008 -47.499 11.4971 -68.499s18.167 -39.333 31.5 -55s29 -27.834 47 -36.501s37.333 -13 58 -13c18 0 35.333 3.33301 52 10s31.334 17.5 44.001 32.5s22.834 34.667 30.501 59s11.5 54.166 11.5 89.499c0 60 -12 104.5 -36 133.5
s-55 43.5 -93 43.5c-31.333 0 -60.666 -8.16699 -87.999 -24.5s-50.333 -37.166 -69 -62.499v-108zM151.997 674.001v-268.996c6.66699 9.33301 15.5 19.166 26.5 29.499s23.667 19.666 38 27.999s30.333 15.333 48 21s36.834 8.5 57.501 8.5
c31.333 0 58.833 -6.5 82.5 -19.5s43.334 -30.667 59.001 -53s27.5 -48.333 35.5 -78s12 -60.834 12 -93.501c0 -42.667 -4.83301 -80 -14.5 -112s-23.334 -58.833 -41.001 -80.5s-38.834 -37.834 -63.501 -48.501s-51.667 -16 -81 -16c-36.667 0 -69 9.16699 -97 27.5
s-48.667 40.5 -62 66.5v-276h-74v865h74z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="550" 
d="M400 95c-6 -10.667 -13.999 -21.501 -23.999 -32.501s-22 -21.167 -36 -30.5s-29.833 -17 -47.5 -23s-37.167 -9 -58.5 -9c-49.333 0 -88.333 15 -117 45s-43 78 -43 144v294h72v-294c0 -39.333 8.5 -69.833 25.5 -91.5s40.833 -32.5 71.5 -32.5
c18 0 34.833 2.83301 50.5 8.5s30 13 43 22s24.833 19.333 35.5 31s20 23.834 28 36.501v320h74v-447c0 -80 -18.833 -139.5 -56.5 -178.5s-90.834 -58.5 -159.501 -58.5c-32 0 -63 4.5 -93 13.5s-54 19.5 -72 31.5l19 59c18.667 -11.333 40 -20.5 64 -27.5
s50 -10.5 78 -10.5c23.333 0 44 2.83301 62 8.5s33.333 15.167 46 28.5s22.167 31 28.5 53s9.5 49.333 9.5 82v58zM131.001 613.999c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5
s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5zM323.001 613.999c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5
s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="234" 
d="M154 0h-74v483h74v-483z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="510" 
d="M22 330l60 26v318h76v-285l149 65v-69l-149 -65v-250h322v-70h-398v287l-60 -26v69z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="234" 
d="M153 386l56 24v-69l-56 -24v-317h-74v284l-54 -23v69l54 23v321h74v-288z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="991" 
d="M526 87c-17.333 -27.333 -40.8359 -50.165 -70.5029 -68.498s-68.5 -27.5 -116.5 -27.5c-50 0 -93.5 8.66699 -130.5 26s-67.667 41.5 -92 72.5s-42.333 67.667 -54 110s-17.5 88.166 -17.5 137.499s5.83301 95.166 17.5 137.499s29.5 79 53.5 110s54.333 55.167 91 72.5
s79.667 26 129 26c24.667 0 46.834 -2.5 66.501 -7.5s37.334 -11.833 53.001 -20.5s29.334 -19 41.001 -31s21.5 -24.667 29.5 -38v88h409v-70h-333v-222h313v-69h-313v-244h339v-69h-415v87zM342.997 54.001c21.333 0 41.333 3.16602 60 9.49902s35.834 14.833 51.501 25.5
s29.5 23 41.5 37s22 29 30 45v135v205c-18 34 -43.333 60.5 -76 79.5s-68.334 28.5 -107.001 28.5c-75.333 0 -131.166 -25.167 -167.499 -75.5s-54.5 -119.166 -54.5 -206.499c0 -88 18.167 -157.167 54.5 -207.5s92.166 -75.5 167.499 -75.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="840" 
d="M456 208c4.66699 -50 20.5 -88.8369 47.5 -116.504s66.5 -41.5 118.5 -41.5c22 0 44.333 2.5 67 7.5s43 12.167 61 21.5c3.33301 -9.33301 6.33301 -18.5 9 -27.5l8 -27.5c-40 -22 -90.333 -33 -151 -33c-44 0 -81.5 7.16699 -112.5 21.5s-56.167 35.166 -75.5 62.499
c-16 -25.333 -36.333 -45.666 -61 -60.999s-58 -23 -100 -23c-38.667 0 -72 6.33301 -100 19s-51.167 30.334 -69.5 53.001s-32 49.334 -41 80.001s-13.5 63.667 -13.5 99s4.5 68.166 13.5 98.499s22.667 56.833 41 79.5s41.5 40.334 69.5 53.001s61.333 19 100 19
c42 0 75.833 -8 101.5 -24s45.167 -35.667 58.5 -59c17.333 25.333 40.333 45.5 69 60.5s64 22.5 106 22.5c64 0 111.833 -18 143.5 -54s47.5 -85 47.5 -147c0 -15.333 -1 -30.166 -3 -44.499s-4.66699 -27.166 -8 -38.499h-326zM116 241.996
c0 -27.333 2.83301 -52.832 8.5 -76.499s14.667 -44 27 -61s28 -30.333 47 -40s41.833 -14.5 68.5 -14.5c34 0 61.833 7.16699 83.5 21.5s38.167 34.166 49.5 59.499c-5.33301 15.333 -9.5 32.5 -12.5 51.5s-4.5 38.833 -4.5 59.5c0 39.333 5 75 15 107
c-11.333 25.333 -27.5 45.5 -48.5 60.5s-48.5 22.5 -82.5 22.5c-26.667 0 -49.5 -4.83301 -68.5 -14.5s-34.667 -23 -47 -40s-21.333 -37 -27 -60s-8.5 -48.167 -8.5 -75.5zM602 434.997c-25.333 0 -47 -4.33496 -65 -13.002s-32.833 -20.667 -44.5 -36
s-20.5 -33.5 -26.5 -54.5s-9.66699 -43.833 -11 -68.5h268c0 4.66699 0.166992 9.33398 0.5 14.001s0.5 9.33398 0.5 14.001c0 46 -9.33301 81.5 -28 106.5s-50 37.5 -94 37.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="528" 
d="M248 -9c-48 0 -86.834 3.66699 -116.501 11s-55.167 16.666 -76.5 27.999l20 62c19.333 -9.33301 42.666 -17.333 69.999 -24s58.666 -10 93.999 -10c24 0 46.167 2.33301 66.5 7s37.833 11.834 52.5 21.501s26.167 22.167 34.5 37.5s12.5 33.666 12.5 54.999
s-4.33301 39 -13 53s-20.5 26 -35.5 36s-32.167 18.5 -51.5 25.5s-39.666 13.833 -60.999 20.5s-43 14.667 -65 24s-42.333 21.166 -61 35.499s-33.834 32.166 -45.501 53.499s-17.5 47.666 -17.5 78.999c0 25.333 4.16699 48.833 12.5 70.5s21.5 40.334 39.5 56.001
s41 28 69 37s61.667 13.5 101 13.5c33.333 0 63.666 -3.16699 90.999 -9.5s52 -15.166 74 -26.499l-20 -62c-19.333 9.33301 -41.666 16.833 -66.999 22.5s-51.333 8.5 -78 8.5c-52.667 0 -90.667 -10.167 -114 -30.5s-35 -46.5 -35 -78.5c0 -19.333 4 -35.666 12 -48.999
s18.833 -24.833 32.5 -34.5s29.667 -18.167 48 -25.5s37.833 -14.333 58.5 -21c24 -8 47.833 -16.833 71.5 -26.5s45 -22 64 -37s34.333 -33.333 46 -55s17.5 -48.5 17.5 -80.5s-5.66699 -59.667 -17 -83s-27.166 -42.666 -47.499 -57.999s-44.666 -26.833 -72.999 -34.5
s-59.166 -11.5 -92.499 -11.5zM288.999 740h-51l-116 140h70l71 -89l72 89h70z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="433" 
d="M198 49c40.667 0 69.6709 7.49902 87.0039 22.499s26 33.167 26 54.5c0 26.667 -11.333 46.834 -34 60.501s-50.334 25.167 -83.001 34.5c-21.333 6 -40.833 13 -58.5 21s-33 17.667 -46 29s-23 24.666 -30 39.999s-10.5 33.666 -10.5 54.999
c0 40.667 15.5 71.834 46.5 93.501s72.5 32.5 124.5 32.5c28 0 54.667 -2.66699 80 -8s47.666 -12.666 66.999 -21.999l-17 -57c-18 8.66699 -37.333 15.5 -58 20.5s-42.667 7.5 -66 7.5c-36 0 -62.667 -6 -80 -18s-26 -28 -26 -48c0 -23.333 9 -41 27 -53
s42 -22.667 72 -32c54 -16.667 94.833 -36.5 122.5 -59.5s41.5 -55.5 41.5 -97.5c0 -40.667 -14.667 -73.167 -44 -97.5s-75.333 -36.5 -138 -36.5c-30.667 0 -59.334 2.66699 -86.001 8s-49 13 -67 23l17 55c17.333 -8 37.666 -14.667 60.999 -20s47.333 -8 72 -8z
M242.004 543.999h-51l-116 140h70l71 -89l72 89h70z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="563" 
d="M319 285v-285h-76v285l-227 389h80l184 -324h2l183 324h81zM138 810c0 12.667 4 23.5 12 32.5s19.667 13.5 35 13.5s27.166 -4.5 35.499 -13.5s12.5 -19.833 12.5 -32.5s-4.16699 -23.5 -12.5 -32.5s-20.166 -13.5 -35.499 -13.5s-27 4.5 -35 13.5s-12 19.833 -12 32.5z
M330 810c0 12.667 4.16699 23.5 12.5 32.5s20.166 13.5 35.499 13.5s27 -4.5 35 -13.5s12 -19.833 12 -32.5s-4 -23.5 -12 -32.5s-19.667 -13.5 -35 -13.5s-27.166 4.5 -35.499 13.5s-12.5 19.833 -12.5 32.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="547" 
d="M44 53l357 552h-332v69h426v-54l-356 -551h362v-69h-457v53zM299 740h-51l-116 140h70l71 -89l72 89h70z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="449" 
d="M42 43l264 380h-244v60h334v-43l-264 -380h273v-60h-363v43zM250 544h-51l-116 140h70l71 -89l72 89h70z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="566" 
d="M244 397c4 38 8.99805 73 14.998 105s15.333 59.667 28 83s29.667 41.5 51 54.5s49.333 19.5 84 19.5c16 0 30.333 -1.83301 43 -5.5s23.667 -8.16699 33 -13.5c-4.66699 -9.33301 -9.16699 -18.833 -13.5 -28.5l-12.5 -28.5c-8.66699 4 -17.667 6.83301 -27 8.5
s-18.666 2.5 -27.999 2.5c-30 0 -50.833 -10.167 -62.5 -30.5s-20.167 -49.5 -25.5 -87.5l-12 -79h126v-47h-132l-16.5 -122l-16.5 -122c-4 -27.333 -9.83301 -52.166 -17.5 -74.499s-18 -41.5 -31 -57.5s-29.167 -28.5 -48.5 -37.5s-42.666 -13.5 -69.999 -13.5
c-16 0 -30.167 1.83301 -42.5 5.5s-23.166 8.16699 -32.499 13.5c4.66699 9.33301 9 18.833 13 28.5l12 28.5c8.66699 -4 17.667 -6.83301 27 -8.5s18.666 -2.5 27.999 -2.5c29.333 0 50 10.167 62 30.5s20.667 49.166 26 86.499l16.5 123l16.5 122h-92v47h99z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="295" 
d="M289 546h-70l-72 89l-71 -89h-70l116 140h51z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="295" 
d="M173 544h-51l-116 140h70l71 -89l72 89h70z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="295" 
d="M288 693c-2 -18 -6.33105 -34.667 -12.998 -50s-16 -28.833 -28 -40.5s-26.333 -20.834 -43 -27.501s-35.334 -10 -56.001 -10c-21.333 0 -40.333 3.33301 -57 10s-31 15.834 -43 27.501s-21.5 25.167 -28.5 40.5s-11.5 32 -13.5 50h53
c3.33301 -22 12.666 -39.833 27.999 -53.5s35.666 -20.5 60.999 -20.5c24.667 0 44.834 6.83301 60.501 20.5s25.167 31.5 28.5 53.5h51z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="295" 
d="M95 618c0 13.333 4.5 25 13.5 35s21.833 15 38.5 15s29.5 -5 38.5 -15s13.5 -21.667 13.5 -35c0 -14 -4.5 -26 -13.5 -36s-21.833 -15 -38.5 -15s-29.5 5 -38.5 15s-13.5 22 -13.5 36z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="295" 
d="M99 633c0 -15.333 4.66699 -27.666 14 -36.999s20.666 -14 33.999 -14s24.833 4.66699 34.5 14s14.5 21.666 14.5 36.999s-4.66699 27.833 -14 37.5s-21 14.5 -35 14.5c-13.333 0 -24.666 -4.83301 -33.999 -14.5s-14 -22.167 -14 -37.5zM55 632
c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18.166 15 29.499 20s23.333 7.5 36 7.5s24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s14.833 -18.166 19.5 -29.499s7 -23.333 7 -36s-2.33301 -24.5 -7 -35.5s-11.167 -20.667 -19.5 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7s-24.667 2.33301 -36 7s-21.166 11.167 -29.499 19.5s-14.833 18 -19.5 29s-7 22.833 -7 35.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="295" 
d="M121 2h47l78 -120h-80z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="295" 
d="M297 657c-0.666992 -32 -8.5 -56.665 -23.5 -73.998s-35.5 -26 -61.5 -26c-15.333 0 -28.5 2.5 -39.5 7.5s-21.167 10.5 -30.5 16.5s-18.5 11.5 -27.5 16.5s-18.5 7.5 -28.5 7.5c-13.333 0 -22.666 -4.5 -27.999 -13.5s-8 -19.5 -8 -31.5h-52
c0.666992 31.333 8.16699 55.666 22.5 72.999s35.5 26 63.5 26c14 0 26.5 -2.5 37.5 -7.5s21.167 -10.333 30.5 -16s18.666 -11 27.999 -16s19 -7.5 29 -7.5c13.333 0 22.333 4.16699 27 12.5s7 19.166 7 32.499h54z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="295" 
d="M123 688h80l-109 -136h-54zM279 688h79l-108 -136h-54z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="745" 
d="M374 613c-71.333 0 -127.333 -21.167 -168 -63.5s-61 -102.833 -61 -181.5c0 -37.333 6 -72.333 18 -105s26.833 -62.667 44.5 -90s36.5 -51.5 56.5 -72.5s38.333 -37.833 55 -50.5v-50h-212v66h125c-15.333 14 -32.333 31.167 -51 51.5s-36.334 43.666 -53.001 69.999
s-30.5 55.833 -41.5 88.5s-16.5 68.334 -16.5 107.001c0 45.333 7 86.5 21 123.5s34 68.5 60 94.5s57.667 46.167 95 60.5s79.333 21.5 126 21.5s88.667 -7.16699 126 -21.5s69.166 -34.5 95.499 -60.5s46.5 -57.5 60.5 -94.5s21 -78.167 21 -123.5
c0 -38.667 -5.66699 -74.334 -17 -107.001s-25.166 -62.167 -41.499 -88.5s-34 -49.666 -53 -69.999s-36.167 -37.5 -51.5 -51.5h125v-66h-212v50c16.667 12.667 35 29.5 55 50.5s38.833 45.167 56.5 72.5s32.5 57.333 44.5 90s18 67.667 18 105
c0 78.667 -20.167 139.167 -60.5 181.5s-95.166 63.5 -164.499 63.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="594" 
d="M554 10c-8.66699 -5.33301 -19.666 -9.5 -32.999 -12.5s-27.666 -4.5 -42.999 -4.5c-23.333 0 -42.5 3.66699 -57.5 11s-27 17.333 -36 30s-15.333 27.5 -19 44.5s-5.5 34.833 -5.5 53.5v291h-134v-228c0 -16 -0.833008 -33.333 -2.5 -52s-3.83398 -37 -6.50098 -55
s-6.16699 -34.833 -10.5 -50.5s-8.83301 -28.167 -13.5 -37.5h-73c6 10.667 11.167 24.334 15.5 41.001s7.66602 34 9.99902 52s4.16602 36 5.49902 54s2 34 2 48v228c-24 -0.666992 -45.167 -3.16699 -63.5 -7.5s-34.5 -9.83301 -48.5 -16.5l-11 59
c12 7.33301 29.667 13.5 53 18.5s47 7.5 71 7.5h398v-61h-120v-298c0 -22.667 4.33301 -40.834 13 -54.501s22.667 -20.5 42 -20.5c12.667 0 22.834 0.833008 30.501 2.5s14.5 4.16699 20.5 7.5z" />
    <glyph glyph-name="uni0E00" unicode="&#xe00;" horiz-adv-x="195" 
 />
    <glyph glyph-name="uni0E01" unicode="&#xe01;" horiz-adv-x="601" 
d="M448 0l0.000976562 352.999c0 49.333 -13 86.833 -39 112.5s-63.667 38.5 -113 38.5c-39.333 0 -73.833 -8.83301 -103.5 -26.5s-50.5 -40.5 -62.5 -68.5l81 -74c-38 -17.333 -57 -47.333 -57 -90v-245h-76v234c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-84 71
c6 20.667 16.167 41 30.5 61s32.333 37.667 54 53s46.167 27.666 73.5 36.999s56.666 14 87.999 14c37.333 0 70.333 -5.33301 99 -16s52.5 -25.667 71.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-353h-76z" />
    <glyph glyph-name="uni0E02" unicode="&#xe02;" horiz-adv-x="552" 
d="M399 560h76.002v-372c0 -64.667 -16.333 -113.667 -49 -147s-84.334 -50 -155.001 -50s-122.334 17.167 -155.001 51.5s-49 79.833 -49 136.5v78c0 19.333 5.16699 37.333 15.5 54s21.833 32.5 34.5 47.5s24.167 29.5 34.5 43.5s15.5 27.667 15.5 41v26
c0 17.333 -8.66699 26 -26 26h-68v65h75c34 0 57.667 -7.66699 71 -23s20 -36.333 20 -63v-30c0 -18.667 -5.16699 -36 -15.5 -52s-21.5 -31.5 -33.5 -46.5s-23.167 -29.833 -33.5 -44.5s-15.5 -29.334 -15.5 -44.001v-78c0 -40.667 10.167 -71.5 30.5 -92.5
s53.5 -31.5 99.5 -31.5c44 0 76.333 9.33301 97 28s31 53.334 31 104.001v373z" />
    <glyph glyph-name="uni0E03" unicode="&#xe03;" horiz-adv-x="556" 
d="M71 256c0 19.333 5.50195 37.3301 16.502 53.9971s23.333 32.667 37 48s26 30.5 37 45.5s16.5 30.833 16.5 47.5v22c0 7.33301 -1.83301 13.666 -5.5 18.999s-10.167 8 -19.5 8s-17.333 -3.16699 -24 -9.5s-12 -14.833 -16 -25.5c-12.667 6.66699 -25.5 13 -38.5 19
l-38.5 18v67c11.333 -6.66699 22.833 -13 34.5 -19l34.5 -18c6.66699 10.667 16 18.834 28 24.501s24.667 8.5 38 8.5c27.333 0 47.5 -8.16699 60.5 -24.5s19.5 -38.833 19.5 -67.5v-22c0 -21.333 -5.5 -40.666 -16.5 -57.999s-23.167 -33.5 -36.5 -48.5
s-25.5 -29.667 -36.5 -44s-16.5 -29.166 -16.5 -44.499v-77c0 -40.667 10.167 -71.5 30.5 -92.5s53.5 -31.5 99.5 -31.5c44 0 76.333 9.33301 97 28s31 53.334 31 104.001v373h76v-372c0 -64.667 -16.333 -113.667 -49 -147s-84.334 -50 -155.001 -50
s-122.334 17.167 -155.001 51.5s-49 79.833 -49 136.5v77z" />
    <glyph glyph-name="uni0E04" unicode="&#xe04;" horiz-adv-x="617" 
d="M308 569c37.333 0 70.498 -5.50098 99.498 -16.501s53.333 -26.167 73 -45.5s34.5 -42 44.5 -68s15 -54 15 -84v-355h-76v356c0 44 -13 79.667 -39 107s-65 41 -117 41s-90.833 -13.667 -116.5 -41s-38.5 -63 -38.5 -107v-127c9.33301 20.667 24 38.167 44 52.5
s44.667 21.5 74 21.5h69v-67h-69c-28.667 0 -53.167 -8.16699 -73.5 -24.5s-35.166 -40.166 -44.499 -71.499v-140h-76v355c0 30 5 58 15 84s24.833 48.667 44.5 68s43.834 34.5 72.501 45.5s61.667 16.5 99 16.5z" />
    <glyph glyph-name="uni0E05" unicode="&#xe05;" horiz-adv-x="626" 
d="M154 229c9.33301 20.667 23.998 38.165 43.998 52.498s45 21.5 75 21.5h69v-67h-69c-29.333 0 -54.166 -8.16699 -74.499 -24.5s-35.166 -40.166 -44.499 -71.499v-140h-76v388c0 50 10.833 90.167 32.5 120.5s55.5 50.5 101.5 60.5l101 -68l101 68
c89.333 -19.333 134 -79.666 134 -180.999v-388h-76v396c0 54.667 -20.333 88.667 -61 102l-98 -59l-98 59c-40.667 -13.333 -61 -47.333 -61 -102v-167z" />
    <glyph glyph-name="uni0E06" unicode="&#xe06;" horiz-adv-x="602" 
d="M155 176c7.33301 -16 16.833 -31.335 28.5 -46.002s25 -27.5 40 -38.5s31.333 -19.667 49 -26s35.5 -9.5 53.5 -9.5c44 0 75.5 13.167 94.5 39.5s28.5 63.833 28.5 112.5v352h76v-353c0 -68 -15.5 -121 -46.5 -159s-78.833 -57 -143.5 -57c-40 0 -75 8.5 -105 25.5
s-55 40.833 -75 71.5l-5 -88h-71v250c0 19.333 5.16699 37.333 15.5 54s21.5 33 33.5 49s23.167 32 33.5 48s15.5 33 15.5 51v21c0 8 -2 14.5 -6 19.5s-10.667 7.5 -20 7.5c-18.667 0 -31.667 -11.667 -39 -35l-78 38v66l69 -37c6.66699 10.667 16 18.834 28 24.501
s24.667 8.5 38 8.5c27.333 0 47.5 -8.16699 60.5 -24.5s19.5 -38.5 19.5 -66.5v-22c0 -22.667 -4.83301 -43 -14.5 -61s-20.5 -35 -32.5 -51s-22.833 -31.167 -32.5 -45.5s-14.5 -29.166 -14.5 -44.499v-74z" />
    <glyph glyph-name="uni0E07" unicode="&#xe07;" horiz-adv-x="555" 
d="M185 0l-170 560.003h75l151 -495h15c22.667 0 44.5 5.16699 65.5 15.5s39.5 25.5 55.5 45.5s28.833 44.333 38.5 73s14.5 61.334 14.5 98.001c0 37.333 -3.33301 69.166 -10 95.499s-15.834 47.666 -27.501 63.999s-25.334 28.333 -41.001 36s-32.5 11.5 -50.5 11.5
c-22.667 0 -43.334 -3.33301 -62.001 -10l-11 60c10 5.33301 22.333 9.16602 37 11.499s28 3.5 40 3.5c29.333 0 56.333 -5.83301 81 -17.5s45.834 -28.834 63.501 -51.501s31.334 -51 41.001 -85s14.5 -73.333 14.5 -118c0 -46 -6.83301 -87.5 -20.5 -124.5
s-32 -68.167 -55 -93.5s-49.667 -44.833 -80 -58.5s-62.166 -20.5 -95.499 -20.5h-69z" />
    <glyph glyph-name="uni0E08" unicode="&#xe08;" horiz-adv-x="538" 
d="M180 0l-56.0029 230.998c-3.33301 15.333 -12.333 23 -27 23h-38v65h44c26.667 0 47.167 -5.16699 61.5 -15.5s24.5 -29.5 30.5 -57.5l42 -181h14c20.667 0 40.667 5.16699 60 15.5s36.5 25.333 51.5 45s27 44.167 36 73.5s13.5 63 13.5 101
c0 70.667 -15.167 122.334 -45.5 155.001s-74.166 49 -131.499 49c-39.333 0 -75.166 -6.66699 -107.499 -20s-60.833 -30 -85.5 -50l-24 59c26.667 21.333 58.834 39.333 96.501 54s77.5 22 119.5 22s78.833 -6.16699 110.5 -18.5s58.167 -30.166 79.5 -53.499
s37.333 -51.666 48 -84.999s16 -71 16 -113c0 -44 -6 -84.167 -18 -120.5s-28.833 -67.833 -50.5 -94.5s-47.5 -47.334 -77.5 -62.001s-63 -22 -99 -22h-63z" />
    <glyph glyph-name="uni0E09" unicode="&#xe09;" horiz-adv-x="587" 
d="M439 0l-5 87.0029c-8.66699 -13.333 -18.5 -25.833 -29.5 -37.5s-23.5 -21.834 -37.5 -30.501s-30 -15.5 -48 -20.5s-38.667 -7.5 -62 -7.5c-54 0 -94.667 13.667 -122 41s-41 70 -41 128v71c0 15.333 -6.66699 23 -20 23h-32v64h51c23.333 0 41.666 -6 54.999 -18
s20 -31.667 20 -59v-78c0 -37.333 8 -64.5 24 -81.5s43 -25.5 81 -25.5c18 0 35.167 3.16699 51.5 9.5s31.333 15 45 26s26 23.833 37 38.5s20.167 30 27.5 46v160c0 53.333 -13.333 94.666 -40 123.999s-66.667 44 -120 44c-42 0 -80.833 -6.5 -116.5 -19.5
s-66.5 -28.833 -92.5 -47.5l-28 57c30 21.333 64.5 39.166 103.5 53.499s83.167 21.5 132.5 21.5c39.333 0 73.833 -5.5 103.5 -16.5s54.334 -26.667 74.001 -47s34.5 -44.833 44.5 -73.5s15 -60.667 15 -96v-336h-71z" />
    <glyph glyph-name="uni0E0A" unicode="&#xe0a;" horiz-adv-x="553" 
d="M476 189c0 -65.333 -16.333 -114.668 -49 -148.001s-84.334 -50 -155.001 -50s-122.5 17.333 -155.5 52s-49.5 80.334 -49.5 137.001v77c0 19.333 5.33301 37.5 16 54.5s22.167 33 34.5 48l34.5 43c10.667 13.667 16 27.5 16 41.5v25c0 17.333 -9 26 -27 26h-68v65h76
c33.333 0 56.833 -7.66699 70.5 -23s20.5 -36.333 20.5 -63v-29c0 -18.667 -5.16699 -36.167 -15.5 -52.5s-21.666 -32 -33.999 -47s-23.666 -29.667 -33.999 -44s-15.5 -29.166 -15.5 -44.499v-77c0 -41.333 10.333 -72.5 31 -93.5s54 -31.5 100 -31.5
c21.333 0 40 2.16699 56 6.5s29.167 11.666 39.5 21.999s18.166 24 23.499 41s8 38.167 8 63.5v104c0 28.667 -6.33301 49.667 -19 63s-29 20 -49 20h-20l-11 32l109 153h83l-112 -146c28 -4.66699 51 -18 69 -40s27 -49 27 -81v-104z" />
    <glyph glyph-name="uni0E0B" unicode="&#xe0b;" horiz-adv-x="556" 
d="M145 180c0 -41.333 10.1709 -72.501 30.5039 -93.501s53.5 -31.5 99.5 -31.5c21.333 0 40 2.16699 56 6.5s29.333 11.666 40 21.999s18.667 24 24 41s8 38.167 8 63.5v104c0 28.667 -6.33301 49.667 -19 63s-29 20 -49 20h-20l-11 32l108 153h83l-112 -146
c28 -4.66699 51 -18 69 -40s27 -49 27 -81v-104c0 -65.333 -16.333 -114.666 -49 -147.999s-84.334 -50 -155.001 -50c-35.333 0 -65.833 4.5 -91.5 13.5s-46.834 21.833 -63.501 38.5s-29 36.667 -37 60s-12 49 -12 77v77c0 19.333 5.66699 37.166 17 53.499
s23.666 32.166 36.999 47.499s25.666 30.5 36.999 45.5s17 31.167 17 48.5v21c0 8 -2 14.5 -6 19.5s-10.667 7.5 -20 7.5c-18.667 0 -31.667 -11.667 -39 -35l-78 38v66l69 -37c6.66699 10.667 16 18.834 28 24.501s24.667 8.5 38 8.5c27.333 0 47.5 -8.16699 60.5 -24.5
s19.5 -38.5 19.5 -66.5v-22c0 -21.333 -5.5 -40.666 -16.5 -57.999s-23.167 -33.666 -36.5 -48.999s-25.5 -30 -36.5 -44s-16.5 -28.667 -16.5 -44v-77z" />
    <glyph glyph-name="uni0E0C" unicode="&#xe0c;" horiz-adv-x="919" 
d="M425 353c0 49.333 -11.666 86.834 -34.999 112.501s-58 38.5 -104 38.5c-35.333 0 -67.166 -8.83301 -95.499 -26.5s-48.5 -40.5 -60.5 -68.5l40 -37l41 -37c-38 -17.333 -57 -47.333 -57 -90v-154c0 -18.667 8.66699 -28 26 -28h54v-63h-73
c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-42 35l-42 36c6 20.667 16 41 30 61s31.333 37.667 52 53s44 27.666 70 36.999s54 14 84 14c36 0 67.5 -5.33301 94.5 -16s49.333 -25.667 67 -45
s31 -42.166 40 -68.499s13.5 -55.166 13.5 -86.499v-177c7.33301 -16.667 16.666 -32.167 27.999 -46.5s24 -27 38 -38s28.833 -19.667 44.5 -26s31.5 -9.5 47.5 -9.5c38.667 0 66.167 13 82.5 39s24.5 62 24.5 108v357h76v-344c0 -34.667 -3.16699 -65.834 -9.5 -93.501
s-16.5 -51.334 -30.5 -71.001s-32 -34.667 -54 -45s-48.333 -15.5 -79 -15.5c-36.667 0 -68.834 8.5 -96.501 25.5s-51.167 40.5 -70.5 70.5l-6 -87h-71v353z" />
    <glyph glyph-name="uni0E0D" unicode="&#xe0d;" horiz-adv-x="919" 
d="M840 63c0 -38.667 -5.16797 -71.998 -15.501 -99.998s-25 -51 -44 -69s-41.833 -31.167 -68.5 -39.5s-56 -12.5 -88 -12.5c-38 0 -72.667 4.33301 -104 13s-56.333 19 -75 31l19 59c19.333 -11.333 41.833 -20.333 67.5 -27s52.167 -10 79.5 -10
c24 0 45.333 2.16699 64 6.5s34.667 12.166 48 23.499s23.5 27.166 30.5 47.499s10.5 46.5 10.5 78.5v33c-6 -12 -14.167 -23.833 -24.5 -35.5s-22.5 -22 -36.5 -31s-30 -16.333 -48 -22s-37.667 -8.5 -59 -8.5c-24.667 0 -47.5 3.66699 -68.5 11
s-39.167 18.666 -54.5 33.999s-27.333 34.833 -36 58.5s-13 51.5 -13 83.5v166c0 49.333 -11.5 86.833 -34.5 112.5s-57.5 38.5 -103.5 38.5c-35.333 0 -67.166 -8.83301 -95.499 -26.5s-48.5 -40.5 -60.5 -68.5l81 -74c-38 -17.333 -57 -47.333 -57 -90v-154
c0 -18.667 8.66699 -28 26 -28h54v-63h-73c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 19.333 4.83301 38.333 14.5 57s23.5 32.667 41.5 42l-84 71c6 20.667 16 41 30 61s31.333 37.667 52 53s44 27.666 70 36.999s54 14 84 14
c36 0 67.333 -5.33301 94 -16s49 -25.667 67 -45s31.333 -42.166 40 -68.499s13 -55.166 13 -86.499v-166c0 -37.333 8.5 -67 25.5 -89s44.5 -33 82.5 -33c37.333 0 69 10.5 95 31.5s46.333 45.5 61 73.5v390h76v-497z" />
    <glyph glyph-name="uni0E0E" unicode="&#xe0e;" horiz-adv-x="609" 
d="M456 353c0 49.333 -12.833 86.832 -38.5 112.499s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l93 -79c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5s-35.167 -22.5 -60.5 -22.5h-59v63h41
c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-96 75c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16
s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-553h-93c-17.333 15.333 -37.333 28.666 -60 39.999s-49 17 -79 17c-32.667 0 -60.334 -5 -83.001 -15s-44.667 -23.667 -66 -41l-10 29l-10 30c18.667 17.333 41.167 32 67.5 44
s57.5 18 93.5 18c32 0 63 -6.83301 93 -20.5s53.667 -29.834 71 -48.501v500z" />
    <glyph glyph-name="uni0E0F" unicode="&#xe0f;" horiz-adv-x="609" 
d="M532 -200l-72.999 -0.00292969l-123 74c-10.667 -24.667 -27.5 -44.167 -50.5 -58.5s-47.167 -21.5 -72.5 -21.5c-24.667 0 -48 3 -70 9s-39.667 13.667 -53 23l10 32l10 32c13.333 -9.33301 29.166 -17.5 47.499 -24.5s35.166 -10.5 50.499 -10.5c28 0 52 9.5 72 28.5
s33 42.833 39 71.5l137 -82v480c0 49.333 -12.833 86.833 -38.5 112.5s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l71 -57l22 -22c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5
s-35.167 -22.5 -60.5 -22.5h-59v63h41c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-47.5 37.5l-48.5 37.5c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5
s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-553z" />
    <glyph glyph-name="uni0E10" unicode="&#xe10;" horiz-adv-x="538" 
d="M180 0l-56.001 231c-3.33301 15.333 -12.333 23 -27 23h-38v65h44c26.667 0 47.167 -5.16699 61.5 -15.5s24.5 -29.5 30.5 -57.5l42 -181h14c20.667 0 40.667 5.16699 60 15.5s36.5 25.333 51.5 45s27 44.167 36 73.5s13.5 63 13.5 101
c0 68.667 -15.167 119.834 -45.5 153.501s-74.166 50.5 -131.499 50.5c-39.333 0 -75.333 -6.66699 -108 -20s-61 -30 -85 -50l-24 59c26.667 21.333 58.834 39.333 96.501 54s77.5 22 119.5 22c49.333 0 92.666 -10.333 129.999 -31l53 61h86l-85 -101
c23.333 -24 40.833 -52.833 52.5 -86.5s17.5 -71.167 17.5 -112.5c0 -44 -6 -84.167 -18 -120.5s-28.833 -67.833 -50.5 -94.5s-47.5 -47.334 -77.5 -62.001s-63 -22 -99 -22h-63zM335.999 -200c-22.667 0 -42.1689 4.66699 -58.502 14s-29.833 21 -40.5 35
c-9.33301 -14.667 -23.166 -26.5 -41.499 -35.5s-39.166 -13.5 -62.499 -13.5h-65v66h71c21.333 0 40 5 56 15s30 22.667 42 38c12.667 -15.333 26 -28 40 -38s32.333 -15 55 -15h53v59h71v-125h-120z" />
    <glyph glyph-name="uni0E11" unicode="&#xe11;" horiz-adv-x="606" 
d="M154 0l-75 -0.00292969v219c0 24.667 3.16699 47.834 9.5 69.501s14.333 41.667 24 60s20.167 35 31.5 50s22.333 28.5 33 40.5v34c0 7.33301 -2 13.666 -6 18.999s-10.667 8 -20 8c-8.66699 0 -16.5 -3.16699 -23.5 -9.5s-12.167 -14.833 -15.5 -25.5l-70 37v67
c10 -6.66699 20.167 -13 30.5 -19l30.5 -18c6.66699 10.667 16 18.834 28 24.501s25 8.5 39 8.5c21.333 0 38.166 -7.16699 50.499 -21.5s18.833 -31.166 19.5 -50.499c14.667 20 34 37.667 58 53s52.667 23 86 23c20.667 0 39.834 -3.33301 57.501 -10
s32.834 -17.167 45.501 -31.5s22.667 -32.666 30 -54.999s11 -48.833 11 -79.5v-393h-76v392c0 43.333 -6.16699 72.833 -18.5 88.5s-29.833 23.5 -52.5 23.5c-26.667 0 -53.5 -9.16699 -80.5 -27.5s-51.333 -41.333 -73 -69s-39.334 -58.167 -53.001 -91.5
s-20.5 -65 -20.5 -95v-221z" />
    <glyph glyph-name="uni0E12" unicode="&#xe12;" horiz-adv-x="983" 
d="M546 176c7.33301 -16 16.833 -31.335 28.5 -46.002s24.667 -27.5 39 -38.5s30 -19.667 47 -26s34.167 -9.5 51.5 -9.5c42 0 72.167 13.167 90.5 39.5s27.5 63.833 27.5 112.5v352h76v-353c0 -68 -15 -121 -45 -159s-76.667 -57 -140 -57c-39.333 0 -73.333 8.5 -102 25.5
s-53 40.833 -73 71.5l-5 -88h-71v399c0 28 -5.16699 50 -15.5 66s-24.833 27.333 -43.5 34l-80 -53l-76 52c-18 -4 -34.833 -11.667 -50.5 -23s-29.167 -26.166 -40.5 -44.499s-20.333 -40.166 -27 -65.499s-10 -53.666 -10 -84.999c0 -40 4.16699 -74 12.5 -102
s19.5 -50.833 33.5 -68.5s30 -30.5 48 -38.5s36.667 -12 56 -12c14 0 26.333 1 37 3s21.334 5.33301 32.001 10l14 -60c-11.333 -5.33301 -24.5 -9.66602 -39.5 -12.999s-30.833 -5 -47.5 -5c-31.333 0 -60.5 5.5 -87.5 16.5s-50.5 28.167 -70.5 51.5
s-35.667 52.833 -47 88.5s-17 78.5 -17 128.5c0 40 5 76.5 15 109.5s23.833 62 41.5 87s38.667 45.333 63 61s51.166 26.5 80.499 32.5l80 -62l82 62c42.667 -8.66699 75.5 -27.834 98.5 -57.501s34.5 -68.834 34.5 -117.501v-218z" />
    <glyph glyph-name="uni0E13" unicode="&#xe13;" horiz-adv-x="919" 
d="M769 0l-6.00098 87c-19.333 -30 -42.833 -53.5 -70.5 -70.5s-60.834 -25.5 -99.501 -25.5c-57.333 0 -99.833 16.5 -127.5 49.5s-41.5 77.833 -41.5 134.5v178c0 49.333 -11.5 86.833 -34.5 112.5s-57.5 38.5 -103.5 38.5c-35.333 0 -67.166 -8.83301 -95.499 -26.5
s-48.5 -40.5 -60.5 -68.5l81 -74c-38 -17.333 -57 -47.333 -57 -90v-154c0 -18.667 8.66699 -28 26 -28h54v-63h-73c-26 -0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-84 71c6 20.667 16 41 30 61
s31.333 37.667 52 53s44 27.666 70 36.999s54 14 84 14c36 0 67.333 -5.33301 94 -16s49 -25.667 67 -45s31.333 -42.166 40 -68.499s13 -55.166 13 -86.499v-177c0 -36.667 7.83301 -65.834 23.5 -87.501s40.167 -32.5 73.5 -32.5c18.667 0 36.334 3.16699 53.001 9.5
s32.334 15 47.001 26s27.667 23.667 39 38s20.666 29.833 27.999 46.5v384h76v-560h-71z" />
    <glyph glyph-name="uni0E14" unicode="&#xe14;" horiz-adv-x="620" 
d="M467 0l-0.000976562 335.997c0 24 -3 46.333 -9 67s-15.333 38.5 -28 53.5s-28.5 26.667 -47.5 35s-41.5 12.5 -67.5 12.5c-31.333 0 -58.833 -5.5 -82.5 -16.5s-43.334 -26.667 -59.001 -47s-27.5 -44.666 -35.5 -72.999s-12 -59.833 -12 -94.5
c0 -39.333 4.66699 -72.5 14 -99.5s21.333 -49 36 -66s31.167 -29.333 49.5 -37s36.5 -11.5 54.5 -11.5c14 0 26.167 1 36.5 3s20.5 5 30.5 9l14 -60c-11.333 -5.33301 -24.166 -9.5 -38.499 -12.5s-29.833 -4.5 -46.5 -4.5c-29.333 0 -57.5 5.5 -84.5 16.5s-51 28 -72 51
s-37.833 52 -50.5 87s-19 76.5 -19 124.5c0 41.333 5.83301 80 17.5 116s28.834 67.333 51.501 94s50.167 47.667 82.5 63s69.5 23 111.5 23c39.333 0 73.5 -6.16699 102.5 -18.5s53 -29.166 72 -50.499s33 -46.166 42 -74.499s13.5 -58.5 13.5 -90.5v-335h-76z" />
    <glyph glyph-name="uni0E15" unicode="&#xe15;" horiz-adv-x="633" 
d="M336 445c-13.333 9.33301 -26.667 18.1641 -40 26.4971l-40 25.5c-18 -4 -35 -11.667 -51 -23s-29.833 -26.166 -41.5 -44.499s-21 -40.333 -28 -66s-10.5 -54.834 -10.5 -87.501c0 -39.333 4.5 -72.666 13.5 -99.999s20.667 -49.666 35 -66.999s30.666 -30 48.999 -38
s37.166 -12 56.499 -12c14 0 26.167 1 36.5 3s20.833 5.33301 31.5 10c2.66699 -10 5.16699 -20 7.5 -30s4.5 -20 6.5 -30c-11.333 -5.33301 -24.333 -9.66602 -39 -12.999s-30.667 -5 -48 -5c-30.667 0 -59.667 5.5 -87 16.5s-51.166 28 -71.499 51
s-36.333 52.167 -48 87.5s-17.5 77.666 -17.5 126.999c0 40.667 5 77.667 15 111s24 62.5 42 87.5s39.333 45.5 64 61.5s51.667 27 81 33l42 -31.5l42 -30.5l43 30.5l43 31.5c42 -9.33301 74.667 -28.666 98 -57.999s35 -68.333 35 -117v-394h-76v399
c0 27.333 -5.16699 49 -15.5 65s-25.166 27.333 -44.499 34l-83 -53v0z" />
    <glyph glyph-name="uni0E16" unicode="&#xe16;" horiz-adv-x="603" 
d="M154 91c0 -18.667 8.66895 -28.001 26.002 -28.001h54v-63h-73c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-42 35l-42 36c6 20.667 16.333 41 31 61s32.834 37.667 54.501 53s46.167 27.666 73.5 36.999
s56.666 14 87.999 14c37.333 0 70.166 -5.33301 98.499 -16s52.166 -25.667 71.499 -45s33.833 -42.166 43.5 -68.499s14.5 -55.166 14.5 -86.499v-353h-76v353c0 49.333 -13 86.833 -39 112.5s-63.667 38.5 -113 38.5c-39.333 0 -74 -8.83301 -104 -26.5
s-51 -40.5 -63 -68.5l40 -37l41 -37c-38 -17.333 -57 -47.333 -57 -90v-154z" />
    <glyph glyph-name="uni0E17" unicode="&#xe17;" horiz-adv-x="612" 
d="M459 363c0 48.667 -9.16602 84.332 -27.499 106.999s-44.5 34 -78.5 34c-21.333 0 -41.833 -3.16699 -61.5 -9.5s-38 -15 -55 -26s-32.5 -23.667 -46.5 -38s-25.667 -29.5 -35 -45.5v-385h-76v560h71l5 -88c11.333 12.667 24.166 24.834 38.499 36.501s30 22 47 31
s35.167 16.167 54.5 21.5s40 8 62 8c58.667 0 103 -18.167 133 -54.5s45 -86.833 45 -151.5v-363h-76v363z" />
    <glyph glyph-name="uni0E18" unicode="&#xe18;" horiz-adv-x="552" 
d="M500 153c0 -32 -4.16699 -58.498 -12.5 -79.498s-21.5 -37.5 -39.5 -49.5s-41.167 -20.5 -69.5 -25.5s-62.5 -7.5 -102.5 -7.5s-74.167 2.83301 -102.5 8.5s-51.5 15.167 -69.5 28.5s-31.167 31 -39.5 53s-12.5 49 -12.5 81v58h73v-58c0 -21.333 2.66699 -39 8 -53
s14 -25 26 -33s27.5 -13.667 46.5 -17s42.5 -5 70.5 -5c28.667 0 52.667 1.33301 72 4s34.833 7.5 46.5 14.5s20 17 25 30s7.5 29.833 7.5 50.5v50c0 20 -5.16699 34.833 -15.5 44.5s-25.166 16.167 -44.499 19.5l-189 26c-40 5.33301 -71.667 18.666 -95 39.999
s-35 52.333 -35 93c0 42.667 18.833 77.5 56.5 104.5s92.167 40.5 163.5 40.5c40.667 0 78.167 -4 112.5 -12s65.166 -18.667 92.499 -32l-15 -61c-25.333 13.333 -54 23.666 -86 30.999s-66 11 -102 11c-52 0 -89.833 -7.66699 -113.5 -23s-35.5 -35 -35.5 -59
c0 -21.333 5.83301 -36.833 17.5 -46.5s27.167 -16.167 46.5 -19.5l190 -28c42 -6 73.333 -19.5 94 -40.5s31 -50.833 31 -89.5v-49z" />
    <glyph glyph-name="uni0E19" unicode="&#xe19;" horiz-adv-x="602" 
d="M153 198c0 -48 9.33398 -83.667 28.001 -107s50 -35 94 -35c18 0 35.667 3.16699 53 9.5s33.666 15 48.999 26s29 23.833 41 38.5s21.667 30 29 46v384h76v-560h-71l-5 88c-20 -30 -45.5 -53.667 -76.5 -71s-68.5 -26 -112.5 -26c-60.667 0 -106 17.333 -136 52
s-45 86 -45 154v363h76v-362z" />
    <glyph glyph-name="uni0E1A" unicode="&#xe1a;" horiz-adv-x="600" 
d="M300 56c50 0 87 13.167 111 39.5s36 62.5 36 108.5v356h76v-356c0 -68.667 -18.833 -121.334 -56.5 -158.001s-93.167 -55 -166.5 -55s-128.833 18.333 -166.5 55s-56.5 89.334 -56.5 158.001v356h76v-356c0 -46 12 -82.167 36 -108.5s61 -39.5 111 -39.5z" />
    <glyph glyph-name="uni0E1B" unicode="&#xe1b;" horiz-adv-x="600" 
d="M300 56c50 0 87 13.167 111 39.5s36 62.5 36 108.5v546h76v-546c0 -68.667 -18.833 -121.334 -56.5 -158.001s-93.167 -55 -166.5 -55s-128.833 18.333 -166.5 55s-56.5 89.334 -56.5 158.001v356h76v-356c0 -46 12 -82.167 36 -108.5s61 -39.5 111 -39.5z" />
    <glyph glyph-name="uni0E1C" unicode="&#xe1c;" horiz-adv-x="655" 
d="M468 195c8.66699 -16.667 16.1641 -34 22.4971 -52s12.5 -37.333 18.5 -58h1c-3.33301 24.667 -5.5 49 -6.5 73s-1.5 49.667 -1.5 77v325h74v-560h-82l-134 265c-7.33301 15.333 -13.666 30.5 -18.999 45.5s-9 26.167 -11 33.5h-2
c-1.33301 -7.33301 -4.66602 -18.5 -9.99902 -33.5s-12 -30.167 -20 -45.5l-137 -265h-82v475c0 25.333 7.5 46 22.5 62s35.5 23.667 61.5 23h62v-65h-44c-18.667 0 -28 -9.33301 -28 -28v-232c0 -27.333 -0.666992 -53 -2 -77s-3.66602 -48.333 -6.99902 -73h2l18 56
c6.66699 20 14.667 38.333 24 55l111 207h61z" />
    <glyph glyph-name="uni0E1D" unicode="&#xe1d;" horiz-adv-x="655" 
d="M468 195c8.66699 -16.667 16.1641 -34 22.4971 -52s12.5 -37.333 18.5 -58h1c-3.33301 24.667 -5.5 49 -6.5 73s-1.5 49.667 -1.5 77v515h74v-750h-82l-134 265c-7.33301 15.333 -13.666 30.5 -18.999 45.5s-9 26.167 -11 33.5h-2
c-1.33301 -7.33301 -4.66602 -18.5 -9.99902 -33.5s-12 -30.167 -20 -45.5l-137 -265h-82v475c0 25.333 7.5 46 22.5 62s35.5 23.667 61.5 23h62v-65h-44c-18.667 0 -28 -9.33301 -28 -28v-232c0 -27.333 -0.666992 -53 -2 -77s-3.66602 -48.333 -6.99902 -73h2l18 56
c6.66699 20 14.667 38.333 24 55l111 207h61z" />
    <glyph glyph-name="uni0E1E" unicode="&#xe1e;" horiz-adv-x="700" 
d="M382 532l101 -308c6 -18.667 11.667 -39.334 17 -62.001s10.333 -50.334 15 -83.001h2c1.33301 33.333 3.33301 61.166 6 83.499s5.66699 43.166 9 62.499l53 335h77l-92 -560h-84l-110 328c-6 17.333 -11.167 38 -15.5 62s-7.5 45.667 -9.5 65h-2
c-2 -19.333 -5.16699 -41 -9.5 -65s-9.5 -44.667 -15.5 -62l-110 -328h-84l-46 280l-46 280h77l53 -335c3.33301 -19.333 6.33301 -40.166 9 -62.499s4.66699 -50.166 6 -83.499h2c4.66699 32.667 9.66699 60.334 15 83.001s11 43.334 17 62.001l101 308h64z" />
    <glyph glyph-name="uni0E1F" unicode="&#xe1f;" horiz-adv-x="700" 
d="M384 532l103 -308c6 -18.667 11.667 -39.334 17 -62.001s10.333 -50.334 15 -83.001h2c1.33301 33.333 3.33301 61.166 6 83.499s5.66699 43.166 9 62.499l54 525h77l-93 -750h-84l-112 328c-6 17.333 -11.167 38 -15.5 62s-7.5 45.667 -9.5 65h-2
c-2 -19.333 -5.16699 -41 -9.5 -65s-9.5 -44.667 -15.5 -62l-112 -328h-84l-46 280l-46 280h77l53 -335c3.33301 -19.333 6.33301 -40.166 9 -62.499s4.66699 -50.166 6 -83.499h2c4.66699 32.667 10 60.334 16 83.001s12 43.334 18 62.001l101 308h64z" />
    <glyph glyph-name="uni0E20" unicode="&#xe20;" horiz-adv-x="609" 
d="M456 353c0 49.333 -12.834 86.8301 -38.501 112.497s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l93 -79c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5s-35.167 -22.5 -60.5 -22.5h-59v63h41
c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-96 75c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16
s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-353h-76v353z" />
    <glyph glyph-name="uni0E21" unicode="&#xe21;" horiz-adv-x="602" 
d="M449 560h76v-353c0 -68 -15.5 -121 -46.5 -159s-78.833 -57 -143.5 -57c-40 0 -75 8.5 -105 25.5s-55 40.833 -75 71.5l-5 -88h-71v560h76v-384c7.33301 -16 16.833 -31.333 28.5 -46s25 -27.5 40 -38.5s31.333 -19.667 49 -26s35.5 -9.5 53.5 -9.5
c44 0 75.5 13.167 94.5 39.5s28.5 63.833 28.5 112.5v352z" />
    <glyph glyph-name="uni0E22" unicode="&#xe22;" horiz-adv-x="577" 
d="M424 560l76.002 0.000976562v-355c0 -32.667 -4.33301 -62.167 -13 -88.5s-22.334 -48.833 -41.001 -67.5s-42.5 -33 -71.5 -43s-63.833 -15 -104.5 -15c-68.667 0 -122.834 15.167 -162.501 45.5s-59.5 74.5 -59.5 132.5c0 34 8.33301 63.167 25 87.5s40 41.5 70 51.5
c-28 7.33301 -50.833 21.666 -68.5 42.999s-26.5 50 -26.5 86c0 36.667 12.167 67.334 36.5 92.001s58.5 37 102.5 37c18 0 34.333 -1.66699 49 -5s27 -7.33301 37 -12c-1.33301 -10.667 -2.83301 -21.167 -4.5 -31.5s-3.16699 -20.833 -4.5 -31.5
c-6.66699 3.33301 -16 6.66602 -28 9.99902s-24.333 5 -37 5c-26 0 -45.5 -6.83301 -58.5 -20.5s-19.5 -31.167 -19.5 -52.5c0 -13.333 3 -25.333 9 -36s13.667 -19.5 23 -26.5s19.833 -12.333 31.5 -16s23.167 -5.5 34.5 -5.5h81c1.33301 -11.333 2.5 -22.333 3.5 -33
s1.83301 -21.667 2.5 -33h-74c-31.333 0 -57.5 -9.66699 -78.5 -29s-31.5 -44.666 -31.5 -75.999c0 -20 4.16699 -37.333 12.5 -52s19.333 -26.834 33 -36.501s29.5 -16.834 47.5 -21.501s36.667 -7 56 -7c52 0 90.5 12.833 115.5 38.5s37.5 62.834 37.5 111.501v354z" />
    <glyph glyph-name="uni0E23" unicode="&#xe23;" horiz-adv-x="490" 
d="M227 55c46.667 0 81.999 9.00195 105.999 27.002s36 40 36 66c0 16 -4.33301 29.667 -13 41s-20 21.333 -34 30s-29.833 16.167 -47.5 22.5s-35.5 12.166 -53.5 17.499c-24 6.66699 -46.5 14.334 -67.5 23.001s-39.5 19.334 -55.5 32.001s-28.5 27.667 -37.5 45
s-13.5 38.666 -13.5 63.999c0 46 18.167 81.833 54.5 107.5s85.833 38.5 148.5 38.5c32 0 63 -3.33301 93 -10s56 -15.667 78 -27l-19 -59c-20 10 -42 17.833 -66 23.5s-50 8.5 -78 8.5c-44.667 0 -78.667 -7.66699 -102 -23s-35 -35 -35 -59c0 -14 3.5 -26 10.5 -36
s16.333 -18.833 28 -26.5s25.167 -14.334 40.5 -20.001s31.666 -11.167 48.999 -16.5c28 -8.66699 54 -18 78 -28s44.5 -21.833 61.5 -35.5s30.333 -29.834 40 -48.501s14.5 -40.667 14.5 -66c0 -47.333 -17.167 -85 -51.5 -113s-87.833 -42 -160.5 -42
c-37.333 0 -71.666 3.5 -102.999 10.5s-57.666 16.833 -78.999 29.5l19 59c19.333 -10.667 43 -19.167 71 -25.5s57.333 -9.5 88 -9.5z" />
    <glyph glyph-name="uni0E24" unicode="&#xe24;" horiz-adv-x="603" 
d="M154 91c0 -18.667 8.66895 -28.001 26.002 -28.001h54v-63h-73c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-42 35l-42 36c6 20.667 16.333 41 31 61s32.834 37.667 54.501 53s46.167 27.666 73.5 36.999
s56.666 14 87.999 14c37.333 0 70.166 -5.33301 98.499 -16s52.166 -25.667 71.499 -45s33.833 -42.166 43.5 -68.499s14.5 -55.166 14.5 -86.499v-553h-76v553c0 49.333 -13 86.833 -39 112.5s-63.667 38.5 -113 38.5c-39.333 0 -74 -8.83301 -104 -26.5
s-51 -40.5 -63 -68.5l40 -37l41 -37c-38 -17.333 -57 -47.333 -57 -90v-154z" />
    <glyph glyph-name="uni0E25" unicode="&#xe25;" horiz-adv-x="572" 
d="M419 0l0.000976562 275.996h-142c-50.667 0 -90.667 -9.33301 -120 -28s-44 -49 -44 -91c0 -35.333 10.833 -61.333 32.5 -78s47.5 -25 77.5 -25c15.333 0 30.5 1.5 45.5 4.5s28.167 7.16699 39.5 12.5l13 -62c-13.333 -5.33301 -29.333 -9.66602 -48 -12.999
s-37.667 -5 -57 -5c-25.333 0 -48.833 3.33301 -70.5 10s-40.5 16.834 -56.5 30.501s-28.5 30.834 -37.5 51.501s-13.5 45 -13.5 73c0 58.667 20.333 104.167 61 136.5s102.667 48.5 186 48.5h134v18c0 46.667 -10.833 82.834 -32.5 108.501s-58.834 38.5 -111.501 38.5
c-38.667 0 -74.167 -5.66699 -106.5 -17s-60.5 -25.333 -84.5 -42l-25 54c10.667 8 23.667 16 39 24s32.5 15.333 51.5 22s39.5 12 61.5 16s45 6 69 6c70 0 123.333 -18.667 160 -56s55 -88.333 55 -153v-360h-76z" />
    <glyph glyph-name="uni0E26" unicode="&#xe26;" horiz-adv-x="609" 
d="M456 353c0 49.333 -12.834 86.8301 -38.501 112.497s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l93 -79c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5s-35.167 -22.5 -60.5 -22.5h-59v63h41
c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-96 75c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16
s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-553h-76v553z" />
    <glyph glyph-name="uni0E27" unicode="&#xe27;" horiz-adv-x="518" 
d="M56 104c22.667 -14 47.667 -25.501 75 -34.501s57 -13.5 89 -13.5c52.667 0 94.5 18 125.5 54s46.5 92 46.5 168c0 76.667 -14.667 133.5 -44 170.5s-72 55.5 -128 55.5c-34.667 0 -65.167 -4.66699 -91.5 -14s-51.166 -21.666 -74.499 -36.999l-18 61
c24.667 15.333 53.5 28.333 86.5 39s67.167 16 102.5 16c79.333 0 139.666 -25.167 180.999 -75.5s62 -120.5 62 -210.5c0 -96 -21.5 -168.667 -64.5 -218s-104.5 -74 -184.5 -74c-38 0 -71.667 4.5 -101 13.5s-56 21.5 -80 37.5z" />
    <glyph glyph-name="uni0E28" unicode="&#xe28;" horiz-adv-x="617" 
d="M308 569c24 0 46.166 -2.50098 66.499 -7.50098s39.166 -12.167 56.499 -21.5l50 59h87l-84 -98c18 -19.333 31.833 -41.5 41.5 -66.5s14.5 -51.5 14.5 -79.5v-355h-76v356c0 44 -13 79.667 -39 107s-65 41 -117 41s-90.833 -13.667 -116.5 -41s-38.5 -63 -38.5 -107
v-127c9.33301 20.667 24 38.167 44 52.5s44.667 21.5 74 21.5h69v-67h-69c-28.667 0 -53.167 -8.16699 -73.5 -24.5s-35.166 -40.166 -44.499 -71.499v-140h-76v355c0 30 5 58 15 84s24.833 48.667 44.5 68s43.834 34.5 72.501 45.5s61.667 16.5 99 16.5z" />
    <glyph glyph-name="uni0E29" unicode="&#xe29;" horiz-adv-x="614" 
d="M300 56c50 0 87 13.167 111 39.5s36 62.5 36 108.5v94h-133v65h133v197h76v-197h83l-17 -65h-66v-94c0 -68.667 -18.833 -121.334 -56.5 -158.001s-93.167 -55 -166.5 -55s-128.833 18.333 -166.5 55s-56.5 89.334 -56.5 158.001v356h76v-356c0 -46 12 -82.167 36 -108.5
s61 -39.5 111 -39.5z" />
    <glyph glyph-name="uni0E2A" unicode="&#xe2a;" horiz-adv-x="572" 
d="M419 0v275.996h-142c-50.667 0 -90.667 -9.33301 -120 -28s-44 -49 -44 -91c0 -35.333 10.833 -61.333 32.5 -78s47.5 -25 77.5 -25c15.333 0 30.5 1.5 45.5 4.5s28.167 7.16699 39.5 12.5l13 -62c-13.333 -5.33301 -29.333 -9.66602 -48 -12.999s-37.667 -5 -57 -5
c-25.333 0 -48.833 3.33301 -70.5 10s-40.5 16.834 -56.5 30.501s-28.5 30.834 -37.5 51.501s-13.5 45 -13.5 73c0 58.667 20.333 104.167 61 136.5s102.667 48.5 186 48.5h134v18c0 44.667 -10.833 80.334 -32.5 107.001s-58.167 40 -109.5 40
c-40 0 -76.167 -5.66699 -108.5 -17s-60.5 -25.333 -84.5 -42l-25 54c10.667 8 24 16.167 40 24.5s33.5 15.666 52.5 21.999s39.167 11.5 60.5 15.5s43 6 65 6c22.667 0 43.5 -2.16699 62.5 -6.5s36.167 -10.166 51.5 -17.499l47 54h87l-80 -94
c33.333 -36.667 50 -85 50 -145v-360h-76z" />
    <glyph glyph-name="uni0E2B" unicode="&#xe2b;" horiz-adv-x="578" 
d="M468 0l-72.999 254c-19.333 64 -56 97 -110 99l-130 -107v-246h-76v560h76v-230l277 230h103l-195 -162c36 -5.33301 64.167 -20 84.5 -44s36.166 -55 47.499 -93l76 -261h-80z" />
    <glyph glyph-name="uni0E2C" unicode="&#xe2c;" horiz-adv-x="655" 
d="M576 0l-82.0029 0.000976562l-137 265c-8 15.333 -14.667 30.5 -20 45.5s-8.66602 26.167 -9.99902 33.5h-2c-2 -7.33301 -5.66699 -18.5 -11 -33.5s-11.666 -30.167 -18.999 -45.5l-134 -265h-82v560h74v-325c0 -27.333 -0.5 -53 -1.5 -77s-3.16699 -48.333 -6.5 -73h1
c6 20.667 12.167 40 18.5 58s13.833 35.333 22.5 52l108 208h61l111 -207c9.33301 -16.667 17.333 -35 24 -55l18 -56h2c-3.33301 24.667 -5.66602 49 -6.99902 73s-2 49.667 -2 77v199c0 34.667 -6 59 -18 73s-33.333 21 -64 21h-15l-11 32l130 190h79l-132 -183
c36 -4 62.5 -18.333 79.5 -43s25.5 -54.334 25.5 -89.001v-435z" />
    <glyph glyph-name="uni0E2D" unicode="&#xe2d;" horiz-adv-x="586" 
d="M159 254c-9.33301 0 -17.002 -3.16895 -23.002 -9.50195s-9 -15.166 -9 -26.499v-28c0 -89.333 51.667 -134 155 -134c56.667 0 100.334 17.833 131.001 53.5s46 92.834 46 171.501c0 72.667 -15.667 128 -47 166s-75.333 57 -132 57c-42 0 -79.667 -6.16699 -113 -18.5
s-62.333 -27.833 -87 -46.5l-25 56c11.333 9.33301 25 18.333 41 27s33.667 16.5 53 23.5s40.166 12.667 62.499 17s45.5 6.5 69.5 6.5c82.667 0 145.667 -26 189 -78s65 -123.333 65 -214c0 -93.333 -21.167 -164.333 -63.5 -213s-105.166 -73 -188.499 -73
c-78 0 -136 17.167 -174 51.5s-57 83.5 -57 147.5v31c0 32.667 8.83301 57.167 26.5 73.5s41.834 24.5 72.501 24.5h89v-65h-81z" />
    <glyph glyph-name="uni0E2E" unicode="&#xe2e;" horiz-adv-x="586" 
d="M159 254c-9.33301 0 -17.002 -3.16992 -23.002 -9.50293s-9 -15.166 -9 -26.499v-28c0 -89.333 51.667 -134 155 -134c56.667 0 100.334 17.833 131.001 53.5s46 92.834 46 171.501c0 72.667 -15.667 128 -47 166s-75.333 57 -132 57c-42 0 -79.667 -6.16699 -113 -18.5
s-62.333 -27.833 -87 -46.5l-25 56c11.333 9.33301 25 18.333 41 27s33.667 16.5 53 23.5s40.166 12.667 62.499 17s45.5 6.5 69.5 6.5c22 0 42.5 -2.16699 61.5 -6.5s36.833 -10.166 53.5 -17.499l47 54h87l-78 -92c27.333 -25.333 48 -57.5 62 -96.5s21 -83.5 21 -133.5
c0 -93.333 -21.167 -164.333 -63.5 -213s-105.166 -73 -188.499 -73c-78 0 -136 17.167 -174 51.5s-57 83.5 -57 147.5v31c0 32.667 8.83301 57.167 26.5 73.5s41.834 24.5 72.501 24.5h89v-65h-81z" />
    <glyph glyph-name="uni0E2F" unicode="&#xe2f;" horiz-adv-x="536" 
d="M381 0l-0.000976562 421.999c-6.66699 -14 -16 -27 -28 -39s-25.833 -22.333 -41.5 -31s-32.667 -15.5 -51 -20.5s-37.5 -7.5 -57.5 -7.5c-30 0 -55.5 4.83301 -76.5 14.5s-38 23.334 -51 41.001s-22.5 38.5 -28.5 62.5s-9 50.667 -9 80v38h73v-39
c0 -46 8.5 -80.333 25.5 -103s44.167 -34 81.5 -34c20.667 0 41.167 4.66699 61.5 14s38.333 21.5 54 36.5s28.334 31.833 38.001 50.5s14.5 37.667 14.5 57v18h71v-560h-76z" />
    <glyph glyph-name="uni0E30" unicode="&#xe30;" horiz-adv-x="296" 
d="M132 370c-26.667 0 -47.833 6.00098 -63.5 18.001s-23.5 32 -23.5 60v44h72v-36c0 -8.66699 1.83301 -14.834 5.5 -18.501s10.167 -5.5 19.5 -5.5h104v-62h-114zM132.001 89.001c-26.667 0 -47.833 5.83301 -63.5 17.5s-23.5 31.5 -23.5 59.5v45h72v-37
c0 -8 1.83301 -13.833 5.5 -17.5s10.167 -5.5 19.5 -5.5h104v-62h-114z" />
    <glyph glyph-name="uni0E31" unicode="&#xe31;" 
d="M-188 638c-63.333 0 -95 28.333 -95 85v39h72v-30c0 -21.333 10.667 -32 32 -32h239v-62h-248z" />
    <glyph glyph-name="uni0E32" unicode="&#xe32;" horiz-adv-x="467" 
d="M314 0v363c0 48 -10.167 83.5 -30.5 106.5s-50.833 34.5 -91.5 34.5c-32 0 -62.167 -5.83301 -90.5 -17.5s-52.166 -25.167 -71.499 -40.5l-20 57c10 8 21.833 16 35.5 24s28.667 15 45 21s33.666 11 51.999 15s36.5 6 54.5 6c64.667 0 113 -18 145 -54
s48 -86.667 48 -152v-363h-76z" />
    <glyph glyph-name="uni0E33" unicode="&#xe33;" horiz-adv-x="467" 
d="M-117 622c-13.333 0 -25.666 2.3291 -36.999 6.99609s-21.166 11.334 -29.499 20.001s-15 18.834 -20 30.501s-7.5 24.167 -7.5 37.5s2.5 25.833 7.5 37.5s11.667 21.834 20 30.501s18.166 15.5 29.499 20.5s23.666 7.5 36.999 7.5
c26.667 0 49.334 -9.16699 68.001 -27.5s28 -41.166 28 -68.499s-9.33301 -50 -28 -68s-41.334 -27 -68.001 -27zM-117 671.996c12.667 0 23.334 4.33301 32.001 13s13 19.667 13 33s-4.33301 24.166 -13 32.499s-19.334 12.5 -32.001 12.5
c-11.333 0 -21.333 -4.16699 -30 -12.5s-13 -19.166 -13 -32.499s4.33301 -24.333 13 -33s18.667 -13 30 -13zM314 -0.00390625v363c0 48 -10.167 83.5 -30.5 106.5s-50.833 34.5 -91.5 34.5c-32 0 -62.167 -5.83301 -90.5 -17.5s-52.166 -25.167 -71.499 -40.5l-20 57
c10 8 21.833 16 35.5 24s28.667 15 45 21s33.666 11 51.999 15s36.5 6 54.5 6c64.667 0 113 -18 145 -54s48 -86.667 48 -152v-363h-76z" />
    <glyph glyph-name="uni0E34" unicode="&#xe34;" 
d="M-502 638v62h425v-62h-425z" />
    <glyph glyph-name="uni0E35" unicode="&#xe35;" 
d="M-502 638v62h353v94h72v-156h-425z" />
    <glyph glyph-name="uni0E36" unicode="&#xe36;" 
d="M-501 638l0.000976562 61.998h282c-3.33301 6.66699 -5 14 -5 22c0 24 8.16699 44.333 24.5 61s36.166 25 59.499 25s43.333 -8.33301 60 -25s25 -37 25 -61c0 -12 -2.16699 -23.167 -6.5 -33.5s-10.333 -19.166 -18 -26.499s-16.667 -13.166 -27 -17.499
s-21.5 -6.5 -33.5 -6.5h-361zM-139.999 686.998c10 0 18.333 3.5 25 10.5s10 15.5 10 25.5c0 10.667 -3.33301 19.334 -10 26.001s-15 10 -25 10c-9.33301 0 -17.5 -3.33301 -24.5 -10s-10.5 -15.334 -10.5 -26.001c0 -10 3.5 -18.5 10.5 -25.5s15.167 -10.5 24.5 -10.5z
" />
    <glyph glyph-name="uni0E37" unicode="&#xe37;" 
d="M-502 638v62h246v94h63v-96h52v96h64v-156h-425z" />
    <glyph glyph-name="uni0E38" unicode="&#xe38;" 
d="M-151 -254v97c0 14.667 -7.33301 22 -22 22h-37v63h57c26 0 45.167 -6.16699 57.5 -18.5s18.5 -32.833 18.5 -61.5v-102h-74z" />
    <glyph glyph-name="uni0E39" unicode="&#xe39;" 
d="M-201 -265c-40.667 0 -71.333 8.83301 -92 26.5s-31 42.834 -31 75.501v30h-52v62h123v-73c0 -24.667 3.66699 -41.834 11 -51.501s21 -14.5 41 -14.5s33.667 4.83301 41 14.5s11 26.834 11 51.501v73h72v-92c0 -32.667 -10.5 -57.834 -31.5 -75.501
s-51.833 -26.5 -92.5 -26.5z" />
    <glyph glyph-name="uni0E3A" unicode="&#xe3a;" 
d="M-57 -123c0 -15.333 -5.83301 -28.833 -17.5 -40.5s-26.167 -17.5 -43.5 -17.5c-16 0 -30 5.83301 -42 17.5s-18 25.167 -18 40.5c0 16 6 30 18 42s26 18 42 18c17.333 0 31.833 -6 43.5 -18s17.5 -26 17.5 -42z" />
    <glyph glyph-name="uni0E3F" unicode="&#xe3f;" horiz-adv-x="566" 
d="M273 0l-196 -0.00292969v650h196v100h51v-101c58.667 -4.66699 103.167 -22 133.5 -52s45.5 -69.333 45.5 -118c0 -19.333 -3.16699 -36.666 -9.5 -51.999s-14.5 -28.666 -24.5 -39.999s-21.167 -20.833 -33.5 -28.5s-24.5 -13.5 -36.5 -17.5
c16 -2.66699 32 -7.66699 48 -15s30.167 -17.166 42.5 -29.499s22.333 -27.5 30 -45.5s11.5 -39 11.5 -63c0 -53.333 -17 -96.833 -51 -130.5s-86 -52.5 -156 -56.5v-101h-51v100zM273 65.9971v237h-123v-237h123zM273 368.997v215h-123v-215h123zM458 187.997
c0 39.333 -12.167 67.333 -36.5 84s-56.833 26.667 -97.5 30v-235c43.333 3.33301 76.5 15.333 99.5 36s34.5 49 34.5 85zM430 481.997c0 30.667 -9.5 54.168 -28.5 70.501s-44.833 26.166 -77.5 29.499v-212c30 4.66699 55.167 16 75.5 34s30.5 44 30.5 78z" />
    <glyph glyph-name="uni0E40" unicode="&#xe40;" horiz-adv-x="236" 
d="M80 0v560h76v-560h-76z" />
    <glyph glyph-name="uni0E41" unicode="&#xe41;" horiz-adv-x="430" 
d="M80 0v560h74v-560h-74zM276 0v560h74v-560h-74z" />
    <glyph glyph-name="uni0E42" unicode="&#xe42;" horiz-adv-x="267" 
d="M106 0l-0.00195312 615c0 30 -4.66699 56.333 -14 79s-20.166 41.667 -32.499 57s-24.666 27.333 -36.999 36s-21.833 14.667 -28.5 18l-2 17c21.333 37.333 52.5 65.5 93.5 84.5s86.167 28.5 135.5 28.5c20 0 37.667 -0.666992 53 -2s29.333 -3.33301 42 -6
s24.334 -5.5 35.001 -8.5s20.667 -6.5 30 -10.5l-13 -66c-18.667 10 -40.5 17.333 -65.5 22s-49.833 7 -74.5 7c-32.667 0 -62.334 -4.33301 -89.001 -13s-47.334 -21.667 -62.001 -39c34.667 -19.333 60.834 -48 78.501 -86s26.5 -80 26.5 -126v-607h-76z" />
    <glyph glyph-name="uni0E43" unicode="&#xe43;" horiz-adv-x="267" 
d="M106 0v542c0 28 6.33301 53.167 19 75.5s26.5 43.166 41.5 62.499s28.833 38.166 41.5 56.499s19 37.5 19 57.5c0 21.333 -6.66699 39.166 -20 53.499s-35 21.5 -65 21.5c-25.333 0 -51.333 -5.66699 -78 -17s-51 -28.666 -73 -51.999l-24 53
c16.667 22.667 41.5 42 74.5 58s68.167 24 105.5 24c26 0 48.833 -3.66699 68.5 -11s36.167 -17.333 49.5 -30s23.5 -27.667 30.5 -45s10.5 -35.666 10.5 -54.999c0 -25.333 -6.5 -48.333 -19.5 -69s-27.167 -41.167 -42.5 -61.5s-29.5 -41.666 -42.5 -63.999
s-19.5 -47.833 -19.5 -76.5v-523h-76z" />
    <glyph glyph-name="uni0E44" unicode="&#xe44;" horiz-adv-x="267" 
d="M106 0v673c0 40 7 75.667 21 107s34 57.333 60 78h-246v63h342v-54c-32 -15.333 -56.833 -37.833 -74.5 -67.5s-26.5 -65.5 -26.5 -107.5v-692h-76z" />
    <glyph glyph-name="uni0E45" unicode="&#xe45;" horiz-adv-x="467" 
d="M314 -200v563c0 48 -10.167 83.5 -30.5 106.5s-50.833 34.5 -91.5 34.5c-32 0 -62.167 -5.83301 -90.5 -17.5s-52.166 -25.167 -71.499 -40.5l-20 57c10 8 21.833 16 35.5 24s28.667 15 45 21s33.666 11 51.999 15s36.5 6 54.5 6c64.667 0 113 -18 145 -54
s48 -86.667 48 -152v-563h-76z" />
    <glyph glyph-name="uni0E46" unicode="&#xe46;" horiz-adv-x="556" 
d="M401 -200l0.00195312 617.001c0 25.333 -4.5 45.833 -13.5 61.5s-22.167 25.167 -39.5 28.5l-83 -59l-85 59c-19.333 -3.33301 -34.833 -11 -46.5 -23s-17.5 -27 -17.5 -45c0 -22 6.16699 -38.833 18.5 -50.5s29.166 -17.5 50.499 -17.5c5.33301 0 10.5 0.333008 15.5 1
s10.167 1.66699 15.5 3l9 -57c-7.33301 -2.66699 -15.333 -4.5 -24 -5.5s-16.667 -1.5 -24 -1.5c-16 0 -31.5 2.33301 -46.5 7s-28.5 12.167 -40.5 22.5s-21.667 23.666 -29 39.999s-11 35.833 -11 58.5c0 18.667 3.66699 35.5 11 50.5s17.333 28.167 30 39.5
s27.167 20.5 43.5 27.5s33.166 11.833 50.499 14.5l79 -61l81 61c44 -3.33301 77 -20 99 -50s33 -66.667 33 -110v-611h-76z" />
    <glyph glyph-name="uni0E47" unicode="&#xe47;" 
d="M-176 638c-16 0 -30.6699 4.16797 -44.0029 12.501s-23 19.833 -29 34.5c-4.66699 -14.667 -14 -26.834 -28 -36.501s-32 -14.5 -54 -14.5c-16.667 0 -31.5 2.83301 -44.5 8.5s-24 13.167 -33 22.5s-15.833 20 -20.5 32s-7 24.667 -7 38s2.33301 26.5 7 39.5
s11.834 24.5 21.501 34.5s21.834 18 36.501 24s32.334 9 53.001 9h206v-58h-203c-18 0 -31 -5 -39 -15s-12 -21.667 -12 -35c0 -12.667 4 -23.167 12 -31.5s19.333 -12.5 34 -12.5c12.667 0 23 4.16699 31 12.5s13.333 17.5 16 27.5h45c5.33301 -20 21.333 -30 48 -30h68
v-62h-64z" />
    <glyph glyph-name="uni0E48" unicode="&#xe48;" 
d="M-148 844v131h71v-131h-71z" />
    <glyph glyph-name="uni0E49" unicode="&#xe49;" 
d="M-159 844c-14.667 0 -25.498 4.5 -32.498 13.5s-10.5 21.167 -10.5 36.5v18c0 5.33301 -0.833008 9.5 -2.5 12.5s-5.83398 4.5 -12.501 4.5h-49v60h81c17.333 0 29.666 -4.5 36.999 -13.5s11 -22.5 11 -40.5v-17c0 -9.33301 4.66699 -14 14 -14h133v-60h-169z" />
    <glyph glyph-name="uni0E4A" unicode="&#xe4a;" 
d="M-128 844l-6.00098 14.9971c6 7.33301 9 16 9 26c0 9.33301 -2.66699 16.833 -8 22.5s-11.666 9.16699 -18.999 10.5l-46 -34l-45 32c-6.66699 -0.666992 -12 -4.16699 -16 -10.5s-6 -13.166 -6 -20.499c0 -6 0.666992 -12.333 2 -19s4 -14.667 8 -24h-66
c-3.33301 8.66699 -5.5 17.5 -6.5 26.5s-1.5 17.167 -1.5 24.5c0 19.333 7 37.333 21 54s34.667 26.667 62 30l49 -42l46 43c24.667 -1.33301 43.167 -8.66602 55.5 -21.999s18.5 -30.666 18.5 -51.999v-9.5c0 -3 -0.333008 -6.16699 -1 -9.5
c13.333 6 22.5 15.167 27.5 27.5s7.5 27.166 7.5 44.499c0 15.333 -3.33301 29.666 -10 42.999h69c1.33301 -5.33301 3 -11.166 5 -17.499s3 -15.166 3 -26.499c0 -13.333 -2.5 -26.666 -7.5 -39.999s-12.5 -25.333 -22.5 -36s-22.167 -19.334 -36.5 -26.001
s-30.833 -10 -49.5 -10h-36z" />
    <glyph glyph-name="uni0E4B" unicode="&#xe4b;" 
d="M-81 908l2 -64h-66l2 64l-72 -2v53l72 -2l-2 64h66l-2 -64l72 2v-53z" />
    <glyph glyph-name="uni0E4C" unicode="&#xe4c;" 
d="M-96 814c-26 0 -39 -12 -39 -36v-21h-74v31c0 26.667 8.16699 48 24.5 64s40.5 24 72.5 24h91v-62h-75z" />
    <glyph glyph-name="uni0E4D" unicode="&#xe4d;" 
d="M-117 622c-13.333 0 -25.666 2.3291 -36.999 6.99609s-21.166 11.334 -29.499 20.001s-15 18.834 -20 30.501s-7.5 24.167 -7.5 37.5s2.5 25.833 7.5 37.5s11.667 21.834 20 30.501s18.166 15.5 29.499 20.5s23.666 7.5 36.999 7.5
c26.667 0 49.334 -9.16699 68.001 -27.5s28 -41.166 28 -68.499s-9.33301 -50 -28 -68s-41.334 -27 -68.001 -27zM-117 671.996c12.667 0 23.334 4.33301 32.001 13s13 19.667 13 33s-4.33301 24.166 -13 32.499s-19.334 12.5 -32.001 12.5
c-11.333 0 -21.333 -4.16699 -30 -12.5s-13 -19.166 -13 -32.499s4.33301 -24.333 13 -33s18.667 -13 30 -13z" />
    <glyph glyph-name="uni0E4E" unicode="&#xe4e;" 
d="M-152 650c-8 -4.66699 -16.3311 -7.99902 -24.998 -9.99902s-19 -3 -31 -3c-12.667 0 -25 1.66699 -37 5s-22.667 8.33301 -32 15s-16.833 15.167 -22.5 25.5s-8.5 22.833 -8.5 37.5c0 12 2 23.167 6 33.5s10.333 19.166 19 26.499s19.667 12.666 33 15.999s29 4 47 2
c-9.33301 10.667 -14 22.667 -14 36c0 12.667 5.66699 24 17 34s28.666 15 51.999 15h56l13 -50h-55c-7.33301 0 -13 -2.16699 -17 -6.5s-6 -9.16602 -6 -14.499c0 -12.667 8.33301 -23 25 -31l-14 -37c-9.33301 2.66699 -18.5 4.66699 -27.5 6s-18.5 2 -28.5 2
c-28 0 -42 -10.667 -42 -32c0 -10 4 -18 12 -24s19.333 -9 34 -9c7.33301 0 14.666 0.333008 21.999 1s15.333 2.66699 24 6v-44z" />
    <glyph glyph-name="uni0E4F" unicode="&#xe4f;" horiz-adv-x="546" 
d="M273 26c-29.333 0 -57 5.66699 -83 17s-48.667 26.666 -68 45.999s-34.5 42 -45.5 68s-16.5 53.667 -16.5 83s5.5 57 16.5 83s26.167 48.667 45.5 68s42 34.5 68 45.5s53.667 16.5 83 16.5s57 -5.5 83 -16.5s48.667 -26.167 68 -45.5s34.5 -42 45.5 -68
s16.5 -53.667 16.5 -83s-5.5 -57 -16.5 -83s-26.167 -48.667 -45.5 -68s-42 -34.666 -68 -45.999s-53.667 -17 -83 -17zM273 58c25.333 0 49 4.66699 71 14s41.333 22.333 58 39s29.667 36 39 58s14 45.667 14 71c0 24.667 -4.66699 48.167 -14 70.5
s-22.333 41.666 -39 57.999s-36 29.333 -58 39s-45.667 14.5 -71 14.5s-49 -4.83301 -71 -14.5s-41.167 -22.667 -57.5 -39s-29.333 -35.666 -39 -57.999s-14.5 -45.833 -14.5 -70.5c0 -25.333 4.83301 -49 14.5 -71s22.667 -41.333 39 -58s35.5 -29.667 57.5 -39
s45.667 -14 71 -14zM273 119c-17.333 0 -33.333 3.16895 -48 9.50195s-27.5 15 -38.5 26s-19.667 23.667 -26 38s-9.5 29.833 -9.5 46.5c0 17.333 3.16699 33.333 9.5 48s15 27.5 26 38.5s23.833 19.667 38.5 26s30.334 9.5 47.001 9.5s32.5 -3.16699 47.5 -9.5
s28 -15 39 -26s19.667 -23.833 26 -38.5s9.5 -30.334 9.5 -47.001c0 -17.333 -3.16699 -33.333 -9.5 -48s-15 -27.334 -26 -38.001s-23.833 -19.167 -38.5 -25.5s-30.334 -9.5 -47.001 -9.5zM273 166.002c20 0 37 7.00098 51 21.001s21 31.667 21 53
c0 19.333 -7 36.166 -21 50.499s-31.333 21.5 -52 21.5c-19.333 0 -36.333 -7.16699 -51 -21.5s-22 -31.5 -22 -51.5c0 -19.333 7.16699 -36.333 21.5 -51s31.833 -22 52.5 -22z" />
    <glyph glyph-name="uni0E50" unicode="&#xe50;" horiz-adv-x="635" 
d="M318 492c36.667 0 71.1699 -5.83301 103.503 -17.5s60.666 -28.167 84.999 -49.5s43.5 -47.5 57.5 -78.5s21 -65.833 21 -104.5s-7 -73.5 -21 -104.5s-33.167 -57.333 -57.5 -79s-52.833 -38.334 -85.5 -50.001s-67.334 -17.5 -104.001 -17.5
s-71.167 5.83301 -103.5 17.5s-60.666 28.334 -84.999 50.001s-43.5 48 -57.5 79s-21 65.833 -21 104.5s7 73.5 21 104.5s33.167 57.167 57.5 78.5s52.666 37.833 84.999 49.5s67.166 17.5 104.499 17.5zM317.003 54c26.667 0 52 4.33301 76 13s44.667 21 62 37
s31.166 35.667 41.499 59s15.5 49.666 15.5 78.999s-5.16699 55.5 -15.5 78.5s-24.166 42.5 -41.499 58.5s-38 28.333 -62 37s-49.333 13 -76 13s-51.834 -4.33301 -75.501 -13s-44.167 -21 -61.5 -37s-31.166 -35.5 -41.499 -58.5s-15.5 -49.167 -15.5 -78.5
s5.16699 -55.666 15.5 -78.999s24.166 -43 41.499 -59s37.833 -28.333 61.5 -37s48.834 -13 75.501 -13z" />
    <glyph glyph-name="uni0E51" unicode="&#xe51;" horiz-adv-x="640" 
d="M269 -52l30.999 -0.00292969c26 0 51.833 5.16699 77.5 15.5s48.667 25.666 69 45.999s37 46 50 77s19.5 67.5 19.5 109.5c0 34.667 -4.66699 66.334 -14 95.001s-22.666 53.167 -39.999 73.5s-38.666 36.166 -63.999 47.499s-54.333 17 -87 17
c-28.667 0 -54.834 -4.5 -78.501 -13.5s-44 -21.667 -61 -38s-30.333 -35.5 -40 -57.5s-14.5 -46 -14.5 -72c0 -24 3.5 -45.5 10.5 -64.5s16.833 -34.833 29.5 -47.5s27.334 -22.334 44.001 -29.001s35 -10 55 -10c14 0 28.333 1.66699 43 5s27.667 7.33301 39 12
c0.666992 -9.33301 1.5 -19.166 2.5 -29.499s1.83301 -20.166 2.5 -29.499c-13.333 -7.33301 -27.333 -12.666 -42 -15.999s-30 -5 -46 -5c-28 0 -54.667 4.66699 -80 14s-47.5 23 -66.5 41s-34 40.333 -45 67s-16.5 57.667 -16.5 93s6.5 67.833 19.5 97.5
s31.167 55.334 54.5 77.001s51.333 38.5 84 50.5s68.667 18 108 18c43.333 0 82.166 -7.33301 116.499 -22s63.333 -35 87 -61s41.834 -57 54.501 -93s19 -75.333 19 -118c0 -47.333 -7.83301 -90.166 -23.5 -128.499s-37 -71 -64 -98s-58.333 -47.833 -94 -62.5
s-73.834 -22 -114.501 -22h-32z" />
    <glyph glyph-name="uni0E52" unicode="&#xe52;" horiz-adv-x="745" 
d="M672 191c0 -28 -3.99902 -53.666 -11.999 -76.999s-20 -43.5 -36 -60.5s-35.667 -30.167 -59 -39.5s-50.333 -14 -81 -14h-221c-30.667 0 -57.667 4.66699 -81 14s-43 22.5 -59 39.5s-28 37.167 -36 60.5s-12 49 -12 77v369h74v-369c0 -44 10.833 -76.333 32.5 -97
s50.5 -31 86.5 -31h211c36 0 65 10.333 87 31s33 53 33 97v127c0 35.333 -5.83301 61.166 -17.5 77.499s-29.5 25.833 -53.5 28.5l-84 -66l-90 67c-19.333 -3.33301 -34 -13.5 -44 -30.5s-15 -34.833 -15 -53.5c0 -25.333 8.16699 -45.833 24.5 -61.5
s36.166 -27.167 59.499 -34.5l-20 -56c-40 7.33301 -72 24 -96 50s-36 59.333 -36 100c0 44 11.833 79 35.5 105s55.167 42 94.5 48l87 -73l79 73c52.667 -5.33301 90.667 -22.833 114 -52.5s35 -71.167 35 -124.5v-124z" />
    <glyph glyph-name="uni0E53" unicode="&#xe53;" horiz-adv-x="658" 
d="M300 183l0.00390625 147c0 30 -5 53.833 -15 71.5s-28 26.5 -54 26.5c-18 0 -33.333 -4.66699 -46 -14s-23 -22.333 -31 -39s-13.833 -36.5 -17.5 -59.5s-5.5 -47.833 -5.5 -74.5c0 -59.333 9 -105.5 27 -138.5s42 -49.5 72 -49.5c17.333 0 33.666 3.33301 48.999 10
l15 -51c-18 -12 -40.333 -18 -67 -18c-25.333 0 -48.5 5.83301 -69.5 17.5s-38.833 28.334 -53.5 50.001s-26 47.5 -34 77.5s-12 63.667 -12 101c0 36.667 3.5 70.334 10.5 101.001s17.333 57.167 31 79.5s30.834 39.833 51.501 52.5s44.667 19 72 19
c31.333 0 55.666 -6.66699 72.999 -20s30 -30.666 38 -51.999c10 21.333 24 38.666 42 51.999s42.333 20 73 20c23.333 0 43.666 -4 60.999 -12s31.5 -19.333 42.5 -34s19.167 -31.834 24.5 -51.501s8 -40.834 8 -63.501v-331h-73v332c0 12 -0.666992 23.667 -2 35
s-4.33301 21.5 -9 30.5s-11.334 16.333 -20.001 22s-20.334 8.5 -35.001 8.5c-28 0 -48.167 -8.83301 -60.5 -26.5s-18.5 -41.5 -18.5 -71.5v-147h-67z" />
    <glyph glyph-name="uni0E54" unicode="&#xe54;" horiz-adv-x="605" 
d="M513 0c-31.333 0 -57.0049 3.16699 -77.0049 9.5s-38.333 18.166 -55 35.499c-12 -15.333 -27.667 -28.166 -47 -38.499s-46.666 -15.5 -81.999 -15.5c-29.333 0 -56.333 5.66699 -81 17s-46 27.666 -64 48.999s-32 47.166 -42 77.499s-15 64.5 -15 102.5
c0 40 6.83301 75.333 20.5 106s32.334 56.334 56.001 77.001s51.667 36.334 84 47.001s67.5 16 105.5 16h109c34.667 0 57.834 5.83301 69.501 17.5s17.5 27.167 17.5 46.5v13h73v-13c0 -41.333 -12.333 -72.833 -37 -94.5s-62 -32.5 -112 -32.5h-119
c-28.667 0 -54.834 -3.5 -78.501 -10.5s-44 -18 -61 -33s-30.333 -33.833 -40 -56.5s-14.5 -49.667 -14.5 -81c0 -28.667 3.83301 -54.5 11.5 -77.5s18 -42.5 31 -58.5s28.333 -28.333 46 -37s36.5 -13 56.5 -13c20.667 0 37.667 3 51 9s25 14.667 35 26
c-7.33301 11.333 -13 23.833 -17 37.5s-6 28.167 -6 43.5c0 40 11.833 71 35.5 93s52.834 33 87.501 33h66l-13 -61h-39c-19.333 0 -35.166 -6.66699 -47.499 -20s-18.5 -31.333 -18.5 -54c0 -32 10.333 -56.5 31 -73.5s50.334 -25.5 89.001 -25.5h53l-13 -61h-49z" />
    <glyph glyph-name="uni0E55" unicode="&#xe55;" horiz-adv-x="605" 
d="M513 0c-31.333 0 -57.167 3.00391 -77.5 9.00391s-38.5 17.667 -54.5 35c-12.667 -15.333 -28.667 -28 -48 -38s-46.333 -15 -81 -15c-29.333 0 -56.333 5.66699 -81 17s-46 27.666 -64 48.999s-32.167 47.166 -42.5 77.499s-15.5 64.5 -15.5 102.5
c0 60 14.333 109 43 147s66.667 65 114 81c-8 12.667 -12 27.667 -12 45c0 12 2.16699 23.5 6.5 34.5s10.5 20.667 18.5 29s17.5 15 28.5 20s22.833 7.5 35.5 7.5s24.5 -2.33301 35.5 -7s20.5 -11.167 28.5 -19.5s14.333 -18 19 -29s7 -22.833 7 -35.5
c0 -9.33301 -1.66699 -18.333 -5 -27h56c34.667 0 57.834 5.83301 69.501 17.5s17.5 27.167 17.5 46.5v13h73v-13c0 -42 -12.333 -73.833 -37 -95.5s-61.667 -32.5 -111 -32.5h-120c-28.667 0 -54.834 -3.5 -78.501 -10.5s-44 -18 -61 -33s-30.333 -33.833 -40 -56.5
s-14.5 -49.334 -14.5 -80.001c0 -28.667 3.83301 -54.5 11.5 -77.5s18.167 -42.667 31.5 -59s28.666 -28.833 45.999 -37.5s36 -13 56 -13c20.667 0 37.667 3.16699 51 9.5s25.333 14.833 36 25.5c-15.333 22.667 -23 50 -23 82c0 40 11.833 71 35.5 93s52.834 33 87.501 33
h66l-14 -61h-38c-19.333 0 -35.333 -6.83301 -48 -20.5s-19 -31.5 -19 -53.5c0 -32.667 10.333 -57.334 31 -74.001s50.667 -25 90 -25h53l-13 -61h-49zM282.999 471.004c11.333 0 20.666 3.83301 27.999 11.5s11 17.167 11 28.5s-3.66699 20.833 -11 28.5
s-16.666 11.5 -27.999 11.5c-10.667 0 -19.667 -3.83301 -27 -11.5s-11 -17.167 -11 -28.5s3.66699 -20.833 11 -28.5s16.333 -11.5 27 -11.5z" />
    <glyph glyph-name="uni0E56" unicode="&#xe56;" horiz-adv-x="613" 
d="M164 231c-7.33301 -7.33301 -13.166 -17.168 -17.499 -29.501s-6.5 -23.833 -6.5 -34.5c0 -14 3.33301 -27.833 10 -41.5s16.667 -25.667 30 -36s29.833 -18.833 49.5 -25.5s42.5 -10 68.5 -10c32.667 0 61 4.83301 85 14.5s44 23 60 40s27.833 37.333 35.5 61
s11.5 49.5 11.5 77.5c0 26.667 -4.16699 51.334 -12.5 74.001s-20 42.167 -35 58.5s-33.167 29 -54.5 38s-45 13.5 -71 13.5c-42.667 0 -76.834 -7.83301 -102.501 -23.5s-47.167 -35.834 -64.5 -60.501h-60l-77 222h73l52 -159c20.667 24.667 46.667 44.5 78 59.5
s66.666 22.5 105.999 22.5c34 0 65.833 -6 95.5 -18s55.167 -29 76.5 -51s38.166 -48.167 50.499 -78.5s18.5 -63.5 18.5 -99.5c0 -34 -5 -66.333 -15 -97s-25.667 -57.667 -47 -81s-48.666 -41.833 -81.999 -55.5s-73.333 -20.5 -120 -20.5c-36 0 -68.167 4.5 -96.5 13.5
s-52.333 21.5 -72 37.5s-34.667 35.167 -45 57.5s-15.5 46.5 -15.5 72.5c0 20 3.83301 38.833 11.5 56.5s16.834 31.834 27.501 42.501z" />
    <glyph glyph-name="uni0E57" unicode="&#xe57;" horiz-adv-x="801" 
d="M281 300v52.001c0 21.333 -3.66699 39.333 -11 54s-21.666 22 -42.999 22c-17.333 0 -32 -4.66699 -44 -14s-21.833 -22.333 -29.5 -39s-13.334 -36.5 -17.001 -59.5s-5.5 -47.833 -5.5 -74.5c0 -59.333 9.5 -105.5 28.5 -138.5s42.5 -49.5 70.5 -49.5
c18 0 34.333 3.33301 49 10l15 -51c-9.33301 -6.66699 -19.5 -11.334 -30.5 -14.001s-23.167 -4 -36.5 -4c-25.333 0 -48.5 5.83301 -69.5 17.5s-38.833 28.334 -53.5 50.001s-26 47.5 -34 77.5s-12 63.667 -12 101c0 36.667 3.66699 70.334 11 101.001s18 57.167 32 79.5
s31 39.833 51 52.5s43 19 69 19c28 0 49 -6 63 -18s24 -28.667 30 -50c9.33301 21.333 22.833 38 40.5 50s40.5 18 68.5 18c18.667 0 35.167 -4 49.5 -12s26.166 -19.333 35.499 -34s16.5 -31.834 21.5 -51.501s7.5 -40.834 7.5 -63.501v-273h25
c27.333 0 49.333 8.83301 66 26.5s25 44.167 25 79.5v396h73v-385c0 -52 -13.667 -94.167 -41 -126.5s-68.666 -48.5 -123.999 -48.5h-96v332c0 12 -0.5 23.667 -1.5 35s-3.5 21.5 -7.5 30.5s-9.83301 16.333 -17.5 22s-18.167 8.5 -31.5 8.5
c-19.333 0 -34.5 -7.16699 -45.5 -21.5s-16.5 -32.5 -16.5 -54.5v-52h-64z" />
    <glyph glyph-name="uni0E58" unicode="&#xe58;" horiz-adv-x="632" 
d="M295 419c-50 0 -87.834 -13.1621 -113.501 -39.4951s-38.5 -60.833 -38.5 -103.5v-119c0 -26 7.66699 -48.167 23 -66.5s34.666 -27.833 57.999 -28.5l111 109l119 -107c22.667 6.66699 40.167 19.167 52.5 37.5s18.5 39.833 18.5 64.5c0 27.333 -8 49.666 -24 66.999
s-35.667 28 -59 32l24 56c18.667 -2 35.834 -7.83301 51.501 -17.5s29.167 -21.834 40.5 -36.501s20.333 -31 27 -49s10 -36.333 10 -55c0 -22.667 -4.33301 -43.834 -13 -63.501s-20.167 -37 -34.5 -52s-30.666 -27.167 -48.999 -36.5s-36.833 -14.333 -55.5 -15l-108 99
l-94 -99c-50.667 1.33301 -91.834 15.5 -123.501 42.5s-47.5 65.833 -47.5 116.5v127c0 27.333 5.16699 53.166 15.5 77.499s25 45.666 44 63.999s42 32.833 69 43.5s57.167 16 90.5 16h126c34.667 0 57.834 5.83301 69.501 17.5s17.5 27.167 17.5 46.5v13h73v-13
c0 -42 -12.333 -73.833 -37 -95.5s-62 -32.5 -112 -32.5h-131z" />
    <glyph glyph-name="uni0E59" unicode="&#xe59;" horiz-adv-x="616" 
d="M441 0l-119.001 340c-9.33301 26 -20.5 47.167 -33.5 63.5s-32.5 24.5 -58.5 24.5c-17.333 0 -32.833 -4.5 -46.5 -13.5s-25.334 -21.833 -35.001 -38.5s-17.167 -36.334 -22.5 -59.001s-8 -47.334 -8 -74.001c0 -30 3 -56.833 9 -80.5s14 -43.834 24 -60.501
s21.5 -29.5 34.5 -38.5s26.5 -13.5 40.5 -13.5c8.66699 0 18.5 1.5 29.5 4.5s20.833 7.16699 29.5 12.5l15 -51c-10.667 -7.33301 -22.5 -13.166 -35.5 -17.499s-26.833 -6.5 -41.5 -6.5c-24 0 -46.833 5.5 -68.5 16.5s-40.667 27.167 -57 48.5s-29.166 47.5 -38.499 78.5
s-14 66.5 -14 106.5c0 36.667 4.5 70.334 13.5 101.001s21.5 57 37.5 79s35.167 39.167 57.5 51.5s46.5 18.5 72.5 18.5c18.667 0 34.834 -2 48.501 -6s25.667 -9.5 36 -16.5s19 -15.167 26 -24.5s13.5 -19.333 19.5 -30c0 26 7.33301 45.333 22 58s32.667 19 54 19
c22 0 40 -5.5 54 -16.5s24.667 -25.167 32 -42.5l23 127h65l-38 -200h-54c-5.33301 20 -13.333 37.333 -24 52s-24.667 22 -42 22c-19.333 0 -34 -7.66699 -44 -23s-15 -32.666 -15 -51.999l123 -359h-71z" />
    <glyph glyph-name="uni0E5B" unicode="&#xe5b;" horiz-adv-x="1135" 
d="M1113 191c-16 -5.33301 -30.8359 -9.16895 -44.5029 -11.502s-26.5 -3.5 -38.5 -3.5c-30 0 -55.667 8.66699 -77 26s-39 42 -53 74l-45 -153h-33l-69 191l-62 -209h-35l-64 189c-9.33301 -33.333 -22.833 -64.833 -40.5 -94.5s-38.834 -55.5 -63.501 -77.5
s-52.5 -39.333 -83.5 -52s-63.833 -19 -98.5 -19c-35.333 0 -67.833 6 -97.5 18s-55.334 28.667 -77.001 50s-38.667 46.5 -51 75.5s-18.5 60.167 -18.5 93.5c0 32 5 61.5 15 88.5s24 50.5 42 70.5s40 35.5 66 46.5s55 16.5 87 16.5c26.667 0 51 -5.16699 73 -15.5
s40.833 -24.166 56.5 -41.499s27.834 -37 36.501 -59s13.667 -45 15 -69h-60c-3.33301 36 -15.666 66.333 -36.999 91s-50 37 -86 37c-23.333 0 -44 -4.66699 -62 -14s-33 -21.666 -45 -36.999s-21.167 -33 -27.5 -53s-9.5 -40.333 -9.5 -61c0 -26 4.66699 -49.833 14 -71.5
s22 -40.167 38 -55.5s35 -27.333 57 -36s45.667 -13 71 -13c42.667 0 79.334 10.667 110.001 32s56.334 48 77.001 80s36.334 67 47.001 105s17 74 19 108h40l75 -222l57 201h40l65 -190l51 168h32c7.33301 -50.667 20.833 -91.167 40.5 -121.5s46.167 -45.5 79.5 -45.5
c19.333 0 38 3.33301 56 10z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="512" 
d="M480 282v-53h-448v53h448z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="989" 
d="M917 282v-53h-845v53h845z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="208" 
d="M149 679c-5.33301 -9.33301 -10.332 -19.667 -14.999 -31s-8.66699 -22.5 -12 -33.5s-5.83301 -21.5 -7.5 -31.5s-2.5 -18.333 -2.5 -25c0 -10 3.33301 -15 10 -15c25.333 0 38 -14.667 38 -44c0 -14 -4.16699 -25.167 -12.5 -33.5s-20.5 -12.5 -36.5 -12.5
s-28.5 4.66699 -37.5 14s-13.5 23.666 -13.5 42.999c0 12.667 1.66699 26.834 5 42.501s7.83301 31.5 13.5 47.5s12.167 31.333 19.5 46s14.666 27.667 21.999 39z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="207" 
d="M58 460c10 18.667 18.668 39.5 26.001 62.5s11 41.5 11 55.5c0 11.333 -3.33301 17 -10 17c-24.667 0 -37 14.667 -37 44c0 13.333 4 24.5 12 33.5s20 13.5 36 13.5s28.5 -4.83301 37.5 -14.5s13.5 -24.167 13.5 -43.5v-4
c-0.666992 -12.667 -2.83398 -26.667 -6.50098 -42s-8.16699 -30.666 -13.5 -45.999s-11.5 -30.333 -18.5 -45s-13.833 -27.334 -20.5 -38.001c-5.33301 1.33301 -10.333 2.5 -15 3.5s-9.66699 2.16699 -15 3.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="207" 
d="M58 -111c4.66699 9.33301 9.33398 19.4971 14.001 30.4971s8.66699 21.833 12 32.5s6 20.834 8 30.501s3 17.834 3 24.501c0 11.333 -3.33301 17 -10 17c-24.667 0 -37 14.667 -37 44c0 14 4 25.167 12 33.5s20 12.5 36 12.5s28.5 -4.66699 37.5 -14
s13.5 -23.666 13.5 -42.999v-5c-0.666992 -12.667 -2.83398 -26.5 -6.50098 -41.5s-8.16699 -30.333 -13.5 -46s-11.5 -30.5 -18.5 -44.5s-13.833 -26.333 -20.5 -37l-30 6v0z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="363" 
d="M305 678c-5.33301 -9.33301 -10.333 -19.668 -15 -31.001s-8.66699 -22.5 -12 -33.5s-6 -21.5 -8 -31.5s-3 -18.333 -3 -25c0 -10 3.66699 -15 11 -15c24.667 0 37 -14.667 37 -44c0 -14 -4.16699 -25.167 -12.5 -33.5s-20.5 -12.5 -36.5 -12.5c-33.333 0 -50 19 -50 57
c0 12.667 1.66699 26.834 5 42.501s7.83301 31.5 13.5 47.5s12 31.333 19 46s14.167 27.667 21.5 39zM149 677.998c-5.33301 -9.33301 -10.333 -19.667 -15 -31s-8.66699 -22.5 -12 -33.5s-5.83301 -21.5 -7.5 -31.5s-2.5 -18.333 -2.5 -25c0 -10 3.33301 -15 10 -15
c24.667 0 37 -14.667 37 -44c0 -14 -4 -25.167 -12 -33.5s-20 -12.5 -36 -12.5s-28.5 4.66699 -37.5 14s-13.5 23.666 -13.5 42.999c0 12.667 1.66699 26.834 5 42.501s7.83301 31.5 13.5 47.5s12 31.333 19 46s14.167 27.667 21.5 39z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="363" 
d="M58 460c10 18.667 18.668 39.5 26.001 62.5s11 41.5 11 55.5c0 11.333 -3.33301 17 -10 17c-24.667 0 -37 14.667 -37 44c0 13.333 4 24.5 12 33.5s20 13.5 36 13.5s28.5 -4.83301 37.5 -14.5s13.5 -24.167 13.5 -43.5v-4
c-0.666992 -12.667 -2.83398 -26.667 -6.50098 -42s-8.16699 -30.666 -13.5 -45.999s-11.5 -30.333 -18.5 -45s-13.833 -27.334 -20.5 -38.001c-5.33301 1.33301 -10.333 2.5 -15 3.5s-9.66699 2.16699 -15 3.5zM213.001 460c5.33301 9.33301 10.334 19.667 15.001 31
s8.66699 22.5 12 33.5s6 21.333 8 31s3 18.167 3 25.5c0 9.33301 -3.66699 14 -11 14c-24.667 0 -37 14.667 -37 44c0 13.333 4.33301 24.5 13 33.5s20.667 13.5 36 13.5c16 0 28.5 -4.83301 37.5 -14.5s13.5 -24.167 13.5 -43.5c0 -12 -1.83301 -25.833 -5.5 -41.5
s-8.33398 -31.5 -14.001 -47.5s-12 -31.667 -19 -47s-14.167 -28.333 -21.5 -39z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="363" 
d="M58 -111c4.66699 9.33301 9.33398 19.4971 14.001 30.4971s8.66699 21.833 12 32.5s6 20.834 8 30.501s3 17.834 3 24.501c0 11.333 -3.33301 17 -10 17c-24.667 0 -37 14.667 -37 44c0 14 4 25.167 12 33.5s20 12.5 36 12.5s28.5 -4.66699 37.5 -14
s13.5 -23.666 13.5 -42.999v-5c-0.666992 -12.667 -2.83398 -26.5 -6.50098 -41.5s-8.16699 -30.333 -13.5 -46s-11.5 -30.5 -18.5 -44.5s-13.833 -26.333 -20.5 -37zM213 -111.003c10.667 19.333 19.666 40.834 26.999 64.501s11 42.167 11 55.5c0 10 -3.66699 15 -11 15
c-24.667 0 -37 14.667 -37 44c0 14 4.16699 25.167 12.5 33.5s20.5 12.5 36.5 12.5s28.5 -4.66699 37.5 -14s13.5 -23.666 13.5 -42.999c0 -12.667 -1.66699 -26.834 -5 -42.501s-7.83301 -31.5 -13.5 -47.5s-12.167 -31.333 -19.5 -46s-14.666 -27.334 -21.999 -38.001z
" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="538" 
d="M305 674v-194h171v-58h-171v-422h-72v422h-171v58h171v194h72z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="538" 
d="M233 -50v194h-171v58h171v220h-171v58h171v194h72v-194h171v-58h-171v-220h171v-58h-171v-194h-72z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="306" 
d="M254 346c0 -27.333 -8.5 -50.999 -25.5 -70.999s-42.5 -30 -76.5 -30c-33.333 0 -58.333 9.66699 -75 29s-25 42.666 -25 69.999c0 28 8.33301 51.667 25 71s42 29 76 29s59.333 -9.66699 76 -29s25 -42.333 25 -69z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="708" 
d="M62 46c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39zM298 46c0 15.333 4.66699 28.333 14 39
s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39zM534 46c0 15.333 4.66699 28.333 14 39s23.333 16 42 16c18 0 31.833 -5.33301 41.5 -16
s14.5 -23.667 14.5 -39s-4.83301 -28.333 -14.5 -39s-23.5 -16 -41.5 -16c-18.667 0 -32.667 5.33301 -42 16s-14 23.667 -14 39z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1117" 
d="M535 660h63l-366 -660h-63zM105 484c0 -16 1.5 -31.667 4.5 -47s7.66699 -28.833 14 -40.5s14.5 -21 24.5 -28s22 -10.5 36 -10.5s26.167 3.5 36.5 10.5s18.666 16.333 24.999 28s11 25 14 40s4.5 30.5 4.5 46.5s-1.5 31.667 -4.5 47s-7.66699 29 -14 41
s-14.666 21.5 -24.999 28.5s-22.5 10.5 -36.5 10.5s-26 -3.66699 -36 -11s-18.167 -16.833 -24.5 -28.5s-11 -25 -14 -40s-4.5 -30.5 -4.5 -46.5zM44 484c0 23.333 3 45.5 9 66.5s15 39.5 27 55.5s26.667 28.833 44 38.5s37.333 14.5 60 14.5s42.667 -4.83301 60 -14.5
s32 -22.5 44 -38.5s21 -34.5 27 -55.5s9 -43.167 9 -66.5s-3 -45.5 -9 -66.5s-15 -39.5 -27 -55.5s-26.667 -28.833 -44 -38.5s-37.333 -14.5 -60 -14.5s-42.667 4.83301 -60 14.5s-32 22.5 -44 38.5s-21 34.5 -27 55.5s-9 43.167 -9 66.5zM504 166
c0 -16 1.5 -31.667 4.5 -47s7.66699 -28.833 14 -40.5s14.5 -21 24.5 -28s22 -10.5 36 -10.5s26.167 3.5 36.5 10.5s18.666 16.333 24.999 28s11 25 14 40s4.5 30.5 4.5 46.5s-1.5 31.667 -4.5 47s-7.66699 29 -14 41s-14.666 21.5 -24.999 28.5s-22.5 10.5 -36.5 10.5
s-26 -3.66699 -36 -11s-18.167 -16.833 -24.5 -28.5s-11 -25 -14 -40s-4.5 -30.5 -4.5 -46.5zM443 166c0 23.333 3 45.5 9 66.5s15 39.5 27 55.5s26.667 28.833 44 38.5s37.333 14.5 60 14.5s42.834 -4.83301 60.501 -14.5s32.5 -22.5 44.5 -38.5s21 -34.5 27 -55.5
s9 -43.167 9 -66.5s-3 -45.5 -9 -66.5s-15 -39.5 -27 -55.5s-26.833 -28.833 -44.5 -38.5s-37.834 -14.5 -60.501 -14.5s-42.667 4.83301 -60 14.5s-32 22.5 -44 38.5s-21 34.5 -27 55.5s-9 43.167 -9 66.5zM853 166c0 -16 1.5 -31.667 4.5 -47s7.66699 -28.833 14 -40.5
s14.666 -21 24.999 -28s22.5 -10.5 36.5 -10.5s26 3.5 36 10.5s18.333 16.333 25 28s11.5 25 14.5 40s4.5 30.5 4.5 46.5s-1.5 31.667 -4.5 47s-7.83301 29 -14.5 41s-15 21.5 -25 28.5s-22 10.5 -36 10.5s-26.167 -3.66699 -36.5 -11s-18.666 -16.833 -24.999 -28.5
s-11 -25 -14 -40s-4.5 -30.5 -4.5 -46.5zM793 166c0 23.333 3 45.5 9 66.5s15 39.5 27 55.5s26.667 28.833 44 38.5s37.333 14.5 60 14.5s42.667 -4.83301 60 -14.5s32 -22.5 44 -38.5s21 -34.5 27 -55.5s9 -43.167 9 -66.5s-3 -45.5 -9 -66.5s-15 -39.5 -27 -55.5
s-26.667 -28.833 -44 -38.5s-37.333 -14.5 -60 -14.5s-42.667 4.83301 -60 14.5s-32 22.5 -44 38.5s-21 34.5 -27 55.5s-9 43.167 -9 66.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="273" 
d="M102 242l129 -180h-61l-65.5 90l-64.5 90l130 179h61z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="272" 
d="M41 421h61l130 -179l-130 -180h-61l129 180z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="429" 
d="M359 660h62l-366 -660h-62z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="565" 
d="M45 243c4 15.333 8.6709 30.3359 14.0039 45.0029h65c-1.33301 8 -2 16 -2 24v25c0 6 0.166992 12.167 0.5 18.5s0.833008 13.166 1.5 20.499h-79l14 45h70c6 37.333 16 72 30 104s31.833 59.667 53.5 83s46.667 41.666 75 54.999s60.166 20 95.499 20
c32.667 0 59.5 -4.83301 80.5 -14.5s40.167 -22.167 57.5 -37.5l-10 -31.5l-10 -30.5c-14.667 14 -31.334 25 -50.001 33s-39.667 12 -63 12c-50.667 0 -91.5 -17.5 -122.5 -52.5s-51.5 -81.833 -61.5 -140.5h227c-2.66699 -7.33301 -5 -14.833 -7 -22.5
s-4.33301 -15.167 -7 -22.5h-218c-0.666992 -6.66699 -1 -13.167 -1 -19.5v-19.5v-25.5c0 -7.66699 0.666992 -15.5 2 -23.5h231c-2.66699 -7.33301 -5 -14.833 -7 -22.5s-4.33301 -15.167 -7 -22.5h-210c11.333 -57.333 33.166 -102.333 65.499 -135s72.5 -49 120.5 -49
c23.333 0 44.666 4 63.999 12s36.666 19 51.999 33l10 -30.5l10 -31.5c-17.333 -16 -36.666 -28.5 -57.999 -37.5s-49 -13.5 -83 -13.5s-65.167 6.16699 -93.5 18.5s-53.5 29.666 -75.5 51.999s-40.667 48.833 -56 79.5s-26.333 64.667 -33 102h-85v0z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="394" 
d="M190 153c0 -36 4.5 -61.168 13.5 -75.501s25.5 -21.5 49.5 -21.5c19.333 0 36.666 3.33301 51.999 10l18 -57c-8.66699 -4.66699 -19 -8.66699 -31 -12s-25.667 -5 -41 -5c-50.667 0 -85.834 13 -105.501 39s-29.5 67 -29.5 123v38
c-11.333 -8.66699 -23.833 -17.167 -37.5 -25.5l-41.5 -26.5l-25 38c18 10.667 35.667 22 53 34s34.333 25.333 51 40v222c0 74.667 11.333 130.334 34 167.001s54.334 55 95.001 55c34.667 0 62.834 -12.5 84.501 -37.5s32.5 -63.167 32.5 -114.5
c0 -62.667 -14.333 -119.5 -43 -170.5s-71.667 -97.5 -129 -139.5v-81zM190 301.999c38 32.667 64.333 69.167 79 109.5s22 84.5 22 132.5c0 36 -4 61.333 -12 76s-19 22 -33 22c-9.33301 0 -17.5 -2.66699 -24.5 -8s-12.833 -14.5 -17.5 -27.5s-8.16699 -30.333 -10.5 -52
s-3.5 -48.5 -3.5 -80.5v-172z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="774" 
d="M186 629v-334h-53v334l-113 -1v46h281v-46zM545 403c3.33301 11.333 7.33301 22.666 12 33.999l13 31l90 206h64v-379h-53v230c0 11.333 0.5 23.166 1.5 35.499s1.83301 23.5 2.5 33.5c-4 -7.33301 -7.83301 -16.333 -11.5 -27s-8.5 -22.667 -14.5 -36l-77 -173h-56
l-77 173c-6 13.333 -10.833 25.333 -14.5 36s-6.5 19.667 -8.5 27h-3c1.33301 -10 2.33301 -21.167 3 -33.5s1 -24.166 1 -35.499v-230h-53v379h64l90 -206l13 -31c4.66699 -11.333 8.66699 -22.666 12 -33.999h2z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="745" 
d="M374 613c-71.333 0 -127.333 -21.167 -168 -63.5s-61 -102.833 -61 -181.5c0 -37.333 6 -72.333 18 -105s26.833 -62.667 44.5 -90s36.5 -51.5 56.5 -72.5s38.333 -37.833 55 -50.5v-50h-212v66h125c-15.333 14 -32.333 31.167 -51 51.5s-36.334 43.666 -53.001 69.999
s-30.5 55.833 -41.5 88.5s-16.5 68.334 -16.5 107.001c0 45.333 7 86.5 21 123.5s34 68.5 60 94.5s57.667 46.167 95 60.5s79.333 21.5 126 21.5s88.667 -7.16699 126 -21.5s69.166 -34.5 95.499 -60.5s46.5 -57.5 60.5 -94.5s21 -78.167 21 -123.5
c0 -38.667 -5.66699 -74.334 -17 -107.001s-25.166 -62.167 -41.499 -88.5s-34 -49.666 -53 -69.999s-36.167 -37.5 -51.5 -51.5h125v-66h-212v50c16.667 12.667 35 29.5 55 50.5s38.833 45.167 56.5 72.5s32.5 57.333 44.5 90s18 67.667 18 105
c0 78.667 -20.167 139.167 -60.5 181.5s-95.166 63.5 -164.499 63.5z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="818" 
d="M782 309l-610.001 -0.000976562v-183c6 -7.33301 16.5 -17.333 31.5 -30s33 -25 54 -37s44.5 -22.5 70.5 -31.5s52.667 -13.5 80 -13.5c28.667 0 55.834 4 81.501 12s48.834 17.667 69.501 29s38.5 23.5 53.5 36.5s26.167 24.5 33.5 34.5h72
c-38 -46 -84.333 -80.667 -139 -104s-111.334 -35 -170.001 -35c-51.333 0 -99.666 8.16699 -144.999 24.5s-84.833 39.333 -118.5 69s-60.334 65.167 -80.001 106.5s-29.5 86.666 -29.5 135.999c0 48.667 9.83301 93.667 29.5 135s46.334 76.833 80.001 106.5
s73.167 52.834 118.5 69.501s93.666 25 144.999 25s99.666 -8.16699 144.999 -24.5s84.833 -39.166 118.5 -68.499s60.334 -64 80.001 -104s29.5 -83.667 29.5 -131v-22zM645.999 518.999c-7.33301 10 -18.5 21.499 -33.5 34.499s-33 25.333 -54 37
s-44.167 21.334 -69.5 29.001s-52.333 11.5 -81 11.5c-27.333 0 -54 -4.5 -80 -13.5s-49.5 -19.5 -70.5 -31.5s-39 -24.333 -54 -37s-25.5 -22.667 -31.5 -30v-182h474v182z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="593" 
d="M298 366c-28.667 0 -54.334 -5.99902 -77.001 -17.999s-41.667 -27.833 -57 -47.5s-27.166 -41.834 -35.499 -66.501s-12.5 -49.667 -12.5 -75c0 -34 9.16699 -60.167 27.5 -78.5s44.166 -27.5 77.499 -27.5c22.667 0 45.334 5.5 68.001 16.5s43.834 26.5 63.501 46.5
s37.167 44.333 52.5 73s27.666 61 36.999 97c-13.333 20.667 -31.833 39.167 -55.5 55.5s-53.167 24.5 -88.5 24.5zM340 597.001c-24.667 0 -47.1689 -2.66797 -67.502 -8.00098s-39.833 -13 -58.5 -23l-9 57c18 10.667 40 19.334 66 26.001s52 10 78 10
c64.667 0 113.834 -18.167 147.501 -54.5s50.5 -87.833 50.5 -154.5c0 -30 -3.16699 -62.167 -9.5 -96.5s-15.666 -68.5 -27.999 -102.5s-27.833 -66.667 -46.5 -98s-40 -59 -64 -83s-51 -43.167 -81 -57.5s-62.667 -21.5 -98 -21.5c-62 0 -107.5 15.333 -136.5 46
s-43.5 71 -43.5 121c0 33.333 6.33301 66.166 19 98.499s30.334 61.333 53.001 87s49.667 46.334 81 62.001s66 23.5 104 23.5c22.667 0 42.834 -2.66699 60.501 -8s33 -12 46 -20s24.167 -17 33.5 -27s16.666 -19.667 21.999 -29c4.66699 19.333 8 37.833 10 55.5
s3 34.5 3 50.5c0 47.333 -11.333 83.5 -34 108.5s-55.334 37.5 -98.001 37.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="622" 
d="M310 551l-185 -491h369zM589 0h-557l263 674h30z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="607" 
d="M90 674h427v-722h-76v652h-275v-652h-76v722z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="542" 
d="M40 -49v35l258 330l-239 319v39h421v-57h-330l224 -300l-240 -309h366v-57h-460z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="566" 
d="M491 314v-61h-416v61h416z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="519" 
d="M20 356v58h128l101 -345l171 605h67l-198 -686h-76l-111 368h-82z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="761" 
d="M352 309c-18.667 28.667 -40.168 51.332 -64.501 67.999s-50.166 25 -77.499 25c-30.667 0 -54 -9 -70 -27s-24 -39.667 -24 -65c0 -29.333 9 -52.666 27 -69.999s41 -26 69 -26s54 9.5 78 28.5s44.667 41.167 62 66.5zM643.999 309.998
c0 25.333 -7.66699 46.999 -23 64.999s-39.333 27 -72 27c-29.333 0 -54.833 -8.16699 -76.5 -24.5s-43.167 -39.166 -64.5 -68.499c18.667 -30 40 -53.333 64 -70s50 -25 78 -25c16 0 29.833 2.66699 41.5 8s21.5 12.5 29.5 21.5s13.833 19.167 17.5 30.5
s5.5 23.333 5.5 36zM705.999 310.997c0 -20.667 -3.50195 -40.002 -10.502 -58.002s-17.167 -33.5 -30.5 -46.5s-29.333 -23.333 -48 -31s-39.334 -11.5 -62.001 -11.5c-36.667 0 -69.834 9.66699 -99.501 29s-54.167 43.333 -73.5 72h-2
c-21.333 -27.333 -46.333 -51 -75 -71s-61.667 -30 -99 -30c-22 0 -42.167 3.5 -60.5 10.5s-34.166 16.667 -47.499 29s-23.833 27.5 -31.5 45.5s-11.5 38 -11.5 60c0 21.333 3.66699 40.833 11 58.5s17.833 32.834 31.5 45.501s29.834 22.5 48.501 29.5s39 10.5 61 10.5
c38.667 0 72.167 -10 100.5 -30s52.166 -43.333 71.499 -70h2c8 10.667 17.5 21.834 28.5 33.501s23.667 22.5 38 32.5s30.333 18.167 48 24.5s36.834 9.5 57.501 9.5c22 0 42.333 -3.5 61 -10.5s34.834 -16.833 48.501 -29.5s24.334 -27.667 32.001 -45
s11.5 -36.333 11.5 -57z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="425" 
d="M249 129c0 -30 -2.16797 -56.333 -6.50098 -79s-11.5 -41.667 -21.5 -57s-23.667 -26.833 -41 -34.5s-39 -11.5 -65 -11.5c-15.333 0 -29 1.66699 -41 5s-22.667 7.33301 -32 12l19 57c15.333 -6.66699 32.333 -10 51 -10c26 0 43 8.33301 51 25s12 43 12 79v385
c0 60 9.33301 105.5 28 136.5s54 46.5 106 46.5c15.333 0 29 -1.66699 41 -5s22.667 -7.33301 32 -12l-18 -58c-15.333 6.66699 -32.666 10 -51.999 10c-26 0 -43 -8.33301 -51 -25s-12 -43 -12 -79v-385z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="566" 
d="M513 402c-13.333 -24.667 -31.3311 -46.666 -53.998 -65.999s-49 -29 -79 -29c-24 0 -44.333 4 -61 12s-31.834 16.833 -45.501 26.5s-27 18.5 -40 26.5s-27.5 12 -43.5 12c-25.333 0 -45.5 -6.83301 -60.5 -20.5s-28.167 -29.167 -39.5 -46.5l-36 38
c5.33301 10.667 12.333 21.667 21 33s18.667 21.5 30 30.5s24 16.333 38 22s29 8.5 45 8.5c24 0 44.167 -4 60.5 -12s31.166 -16.667 44.499 -26s26.5 -18 39.5 -26s28.167 -12 45.5 -12c23.333 0 43 6.66699 59 20s29.333 28.666 40 45.999zM513.002 220.001
c-13.333 -24.667 -31.332 -46.666 -53.999 -65.999s-49 -29 -79 -29c-24 0 -44.333 4 -61 12s-31.834 16.833 -45.501 26.5s-27 18.5 -40 26.5s-27.5 12 -43.5 12c-25.333 0 -45.666 -6.83301 -60.999 -20.5s-28.333 -29.167 -39 -46.5l-36 38
c5.33301 10.667 12.333 21.667 21 33s18.667 21.5 30 30.5s24 16.333 38 22s29 8.5 45 8.5c24 0 44.167 -4 60.5 -12s31.166 -16.667 44.499 -26s26.5 -18 39.5 -26s28.167 -12 45.5 -12c23.333 0 43 6.66699 59 20s29.333 28.666 40 45.999z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="566" 
d="M359 408h121v-61h-145l-50 -123h195v-61h-219l-64 -163h-58l65 163h-126v61h150l49 123h-199v61h223l68 170h58z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="566" 
d="M65 349l421 231v-71l-345 -184l345 -185v-71l-421 231v49zM486 50v-50h-421v50h421z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="566" 
d="M501 50v-50h-421v50h421zM501 300l-421 -231v71l345 185l-345 184v71l421 -231v-49z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="583" 
d="M469 342l-178 265l-180 -265l180 -266zM542 342l-229 -342h-44l-229 341l229 343h44z" />
    <glyph glyph-name="uni0E10.alt1" unicode="&#xf700;" horiz-adv-x="538" 
d="M180 0l-56.001 231c-3.33301 15.333 -12.333 23 -27 23h-38v65h44c26.667 0 47.167 -5.16699 61.5 -15.5s24.5 -29.5 30.5 -57.5l42 -181h14c20.667 0 40.667 5.16699 60 15.5s36.5 25.333 51.5 45s27 44.167 36 73.5s13.5 63 13.5 101
c0 68.667 -15.167 119.834 -45.5 153.501s-74.166 50.5 -131.499 50.5c-39.333 0 -75.333 -6.66699 -108 -20s-61 -30 -85 -50l-24 59c26.667 21.333 58.834 39.333 96.501 54s77.5 22 119.5 22c49.333 0 92.666 -10.333 129.999 -31l53 61h86l-85 -101
c23.333 -24 40.833 -52.833 52.5 -86.5s17.5 -71.167 17.5 -112.5c0 -44 -6 -84.167 -18 -120.5s-28.833 -67.833 -50.5 -94.5s-47.5 -47.334 -77.5 -62.001s-63 -22 -99 -22h-63z" />
    <glyph glyph-name="uni0E34.alt1" unicode="&#xf701;" 
d="M-581 638v62h375v-62h-375z" />
    <glyph glyph-name="uni0E35.alt1" unicode="&#xf702;" 
d="M-581 638v62h303v94h72v-156h-375z" />
    <glyph glyph-name="uni0E36.alt1" unicode="&#xf703;" 
d="M-284 638l-296.999 -0.000976562v62h230c-3.33301 6.66699 -5 14 -5 22c0 24 8.16699 44.333 24.5 61s36.166 25 59.499 25s43.166 -8.33301 59.499 -25s24.5 -37 24.5 -61c0 -23.333 -8.16699 -43.166 -24.5 -59.499s-36.166 -24.5 -59.499 -24.5h-12zM-271.999 686.999
c10 0 18.333 3.5 25 10.5s10 15.5 10 25.5c0 10.667 -3.33301 19.334 -10 26.001s-15 10 -25 10c-9.33301 0 -17.5 -3.33301 -24.5 -10s-10.5 -15.334 -10.5 -26.001c0 -10 3.5 -18.5 10.5 -25.5s15.167 -10.5 24.5 -10.5z" />
    <glyph glyph-name="uni0E37.alt1" unicode="&#xf704;" 
d="M-581 638v62h195v94h64v-96h52v96h64v-156h-375z" />
    <glyph glyph-name="uni0E48.alt1" unicode="&#xf705;" 
d="M-346 638v163h74v-163h-74z" />
    <glyph glyph-name="uni0E49.alt1" unicode="&#xf706;" 
d="M-351 638c-17.333 0 -29.333 5.16797 -36 15.501s-10 23.5 -10 39.5v20c0 7.33301 -1.16699 12.5 -3.5 15.5s-7.5 4.5 -15.5 4.5h-52v62h78c21.333 0 36.5 -4.83301 45.5 -14.5s13.5 -24.834 13.5 -45.501v-19c0 -4.66699 1 -8.5 3 -11.5s6.66699 -4.5 14 -4.5h134v-62
h-171z" />
    <glyph glyph-name="uni0E4A.alt1" unicode="&#xf707;" 
d="M-345 638l-6.00488 14.9951c6.66699 8.66699 10 19 10 31c0 22.667 -10 35 -30 37l-22.5 -18.5l-23.5 -18.5l-43 36c-9.33301 -1.33301 -16.666 -5.5 -21.999 -12.5s-8 -14.833 -8 -23.5c0 -8 0.5 -15.667 1.5 -23s3.5 -15.666 7.5 -24.999h-68
c-3.33301 8 -5.66602 17.333 -6.99902 28s-2 20 -2 28c0 21.333 7.66699 41 23 59s38 29 68 33l50 -48l50 49c25.333 -2 45 -10.167 59 -24.5s21 -34.166 21 -59.499c0 -3.33301 -0.166992 -6.83301 -0.5 -10.5s-0.833008 -7.16699 -1.5 -10.5c15.333 8 25.5 18.667 30.5 32
s7.5 28.333 7.5 45c0 12 -1 22.167 -3 30.5s-4.66699 15.833 -8 22.5h74c2 -6 4 -12.667 6 -20s3 -17.666 3 -30.999c0 -15.333 -2.66699 -30.166 -8 -44.499s-13.166 -27.166 -23.499 -38.499s-23.166 -20.5 -38.499 -27.5s-32.666 -10.5 -51.999 -10.5h-44z" />
    <glyph glyph-name="uni0E4B.alt1" unicode="&#xf708;" 
d="M-285 715l3 -77h-71l3 77l-83 -2v56l83 -2l-3 77h71l-3 -77l86 2v-56z" />
    <glyph glyph-name="uni0E4C.alt1" unicode="&#xf709;" 
d="M-284 700c-30.667 0 -46 -13.667 -46 -41v-23h-80v34c0 29.333 8.66699 52.5 26 69.5s43.333 25.5 78 25.5h100v-65h-78z" />
    <glyph glyph-name="uni0E48.alt2" unicode="&#xf70a;" 
d="M-151 638v163h74v-163h-74z" />
    <glyph glyph-name="uni0E49.alt2" unicode="&#xf70b;" 
d="M-184 638c-17.333 0 -29.333 5.16699 -36 15.5s-10 23.5 -10 39.5v20c0 7.33301 -1.16699 12.5 -3.5 15.5s-7.5 4.5 -15.5 4.5h-52v62h78c18 0 32.333 -4 43 -12s16 -21 16 -39v-28c0 -4.66699 1 -8.5 3 -11.5s6.66699 -4.5 14 -4.5h139v-62h-176z" />
    <glyph glyph-name="uni0E4A.alt2" unicode="&#xf70c;" 
d="M-161 638l-7 14.9951c6.66699 8.66699 10 19 10 31c0 22.667 -9.66699 35 -29 37l-50 -37l-47 36c-9.33301 -1.33301 -16.666 -5.5 -21.999 -12.5s-8 -14.833 -8 -23.5c0 -8 0.5 -15.667 1.5 -23s3.5 -15.666 7.5 -24.999h-68c-3.33301 8 -5.66602 17.333 -6.99902 28
s-2 20 -2 28c0 21.333 7.66699 41 23 59s38 29 68 33l54 -48l53 49c25.333 -2 45.166 -10.167 59.499 -24.5s21.5 -34.166 21.5 -59.499c0 -3.33301 -0.166992 -6.83301 -0.5 -10.5s-0.833008 -7.16699 -1.5 -10.5c15.333 8 26.666 18.667 33.999 32s11 28.333 11 45
c0 12 -1 22.167 -3 30.5s-4.66699 15.833 -8 22.5h73c2 -6 4 -12.667 6 -20s3 -17.666 3 -30.999c0 -15.333 -3 -30.166 -9 -44.499s-14.667 -27.166 -26 -38.499s-24.833 -20.5 -40.5 -27.5s-33.5 -10.5 -53.5 -10.5h-43z" />
    <glyph glyph-name="uni0E4B.alt2" unicode="&#xf70d;" 
d="M-84 715l4 -77h-71l3 77l-83 -2v56l83 -2l-3 77h71l-4 -77l85 2v-56z" />
    <glyph glyph-name="uni0E4C.alt2" unicode="&#xf70e;" 
d="M-103 700c-30.667 0 -46 -13.667 -46 -41v-23h-80v34c0 29.333 8.66699 52.5 26 69.5s43.333 25.5 78 25.5h102v-65h-80z" />
    <glyph glyph-name="uni0E0D.alt1" unicode="&#xf70f;" horiz-adv-x="919" 
d="M842 187c0 -34 -5.00098 -63.334 -15.001 -88.001s-24 -45 -42 -61s-39.833 -27.833 -65.5 -35.5s-54.167 -11.5 -85.5 -11.5s-60 3.83301 -86 11.5s-48.167 19.5 -66.5 35.5s-32.5 36.333 -42.5 61s-15 54 -15 88v166c0 49.333 -11.5 86.833 -34.5 112.5
s-57.5 38.5 -103.5 38.5c-35.333 0 -67.166 -8.83301 -95.499 -26.5s-48.5 -40.5 -60.5 -68.5l40 -37l41 -37c-38 -17.333 -57 -47.333 -57 -90v-154c0 -18.667 8.66699 -28 26 -28h54v-63h-73c-26 0 -46.333 7.83301 -61 23.5s-22 36.167 -22 61.5v149
c0 21.333 4.83301 41 14.5 59s23.5 31.333 41.5 40l-42 35l-42 36c6 20.667 16 41 30 61s31.333 37.667 52 53s44 27.666 70 36.999s54 14 84 14c36 0 67.333 -5.33301 94 -16s49 -25.667 67 -45s31.333 -42.166 40 -68.499s13 -55.166 13 -86.499v-166
c0 -87.333 44.333 -131 133 -131c46 0 79.667 11 101 33s32 54.667 32 98v373h76v-373z" />
    <glyph glyph-name="uni0E31.alt1" unicode="&#xf710;" 
d="M-331 638c-63.333 0 -95 28.333 -95 85v39h72v-30c0 -21.333 10.667 -32 32 -32h219v-62h-228z" />
    <glyph glyph-name="uni0E4D.alt1" unicode="&#xf711;" 
d="M-317 622c-13.333 0 -25.666 2.3291 -36.999 6.99609s-21.166 11.334 -29.499 20.001s-15 18.834 -20 30.501s-7.5 24.167 -7.5 37.5s2.5 25.833 7.5 37.5s11.667 21.834 20 30.501s18.166 15.5 29.499 20.5s23.666 7.5 36.999 7.5
c26.667 0 49.334 -9.16699 68.001 -27.5s28 -41.166 28 -68.499s-9.33301 -50 -28 -68s-41.334 -27 -68.001 -27zM-317 671.996c12.667 0 23.334 4.33301 32.001 13s13 19.667 13 33s-4.33301 24.166 -13 32.499s-19.334 12.5 -32.001 12.5
c-11.333 0 -21.333 -4.16699 -30 -12.5s-13 -19.166 -13 -32.499s4.33301 -24.333 13 -33s18.667 -13 30 -13z" />
    <glyph glyph-name="uni0E47.alt1" unicode="&#xf712;" 
d="M-272 638c-16 0 -30.6699 4.16797 -44.0029 12.501s-23 19.833 -29 34.5c-4.66699 -14.667 -14 -26.834 -28 -36.501s-32 -14.5 -54 -14.5c-16.667 0 -31.5 2.83301 -44.5 8.5s-24 13.167 -33 22.5s-15.833 20 -20.5 32s-7 24.667 -7 38s2.33301 26.5 7 39.5
s11.834 24.5 21.501 34.5s21.834 18 36.501 24s32.334 9 53.001 9h206v-58h-203c-18 0 -31 -5 -39 -15s-12 -21.667 -12 -35c0 -12.667 4 -23.167 12 -31.5s19.333 -12.5 34 -12.5c12.667 0 23 4.16699 31 12.5s13.333 17.5 16 27.5h45c5.33301 -20 21.333 -30 48 -30h68
v-62h-64z" />
    <glyph glyph-name="uni0E48.alt3" unicode="&#xf713;" 
d="M-276 844v131h71v-131h-71z" />
    <glyph glyph-name="uni0E49.alt3" unicode="&#xf714;" 
d="M-297 844c-14.667 0 -25.498 4.5 -32.498 13.5s-10.5 21.167 -10.5 36.5v18c0 5.33301 -0.833008 9.5 -2.5 12.5s-5.83398 4.5 -12.501 4.5h-49v60h81c17.333 0 29.666 -4.5 36.999 -13.5s11 -22.5 11 -40.5v-17c0 -9.33301 4.66699 -14 14 -14h133v-60h-169z" />
    <glyph glyph-name="uni0E4A.alt3" unicode="&#xf715;" 
d="M-256 844l-6.00098 14.9971c6 7.33301 9 16 9 26c0 9.33301 -2.66699 16.833 -8 22.5s-11.666 9.16699 -18.999 10.5l-46 -34l-45 32c-6.66699 -0.666992 -12 -4.16699 -16 -10.5s-6 -13.166 -6 -20.499c0 -6 0.666992 -12.333 2 -19s4 -14.667 8 -24h-66
c-3.33301 8.66699 -5.5 17.5 -6.5 26.5s-1.5 17.167 -1.5 24.5c0 19.333 7 37.333 21 54s34.667 26.667 62 30l49 -42l46 43c24.667 -1.33301 43.167 -8.66602 55.5 -21.999s18.5 -30.666 18.5 -51.999v-9.5c0 -3 -0.333008 -6.16699 -1 -9.5
c13.333 6 22.5 15.167 27.5 27.5s7.5 27.166 7.5 44.499c0 15.333 -3.33301 29.666 -10 42.999h69c1.33301 -5.33301 3 -11.166 5 -17.499s3 -15.166 3 -26.499c0 -13.333 -2.5 -26.666 -7.5 -39.999s-12.5 -25.333 -22.5 -36s-22.167 -19.334 -36.5 -26.001
s-30.833 -10 -49.5 -10h-36z" />
    <glyph glyph-name="uni0E4B.alt3" unicode="&#xf716;" 
d="M-210 908l2 -64h-66l2 64l-72 -2v53l72 -2l-2 64h66l-2 -64l72 2v-53z" />
    <glyph glyph-name="uni0E4C.alt3" unicode="&#xf717;" 
d="M-224 814c-26 0 -39 -12 -39 -36v-21h-74v31c0 26.667 8.16699 48 24.5 64s40.5 24 72.5 24h91v-62h-75z" />
    <glyph glyph-name="uni0E38.alt1" unicode="&#xf718;" 
d="M-151 -407v87c0 15.333 -7.33301 23 -22 23h-37v63h57c26 0 45.167 -6.16699 57.5 -18.5s18.5 -32.833 18.5 -61.5v-93h-74z" />
    <glyph glyph-name="uni0E39.alt1" unicode="&#xf719;" 
d="M-201 -417c-40.667 0 -71.333 8.66699 -92 26s-31 42.666 -31 75.999v20h-52v61h123v-65c0 -23.333 3.83301 -39.666 11.5 -48.999s21.167 -14 40.5 -14s32.833 4.66699 40.5 14s11.5 25.666 11.5 48.999v65h72v-81c0 -33.333 -10.5 -58.666 -31.5 -75.999
s-51.833 -26 -92.5 -26z" />
    <glyph glyph-name="uni0E3A.alt1" unicode="&#xf71a;" 
d="M-59 -288c0 -16 -5.83301 -29.833 -17.5 -41.5s-26.167 -17.5 -43.5 -17.5c-16 0 -30 5.83301 -42 17.5s-18 25.5 -18 41.5s6 29.833 18 41.5s26 17.5 42 17.5c17.333 0 31.833 -5.83301 43.5 -17.5s17.5 -25.5 17.5 -41.5z" />
    <glyph glyph-name="uni0E0E.alt1" unicode="&#xf71b;" horiz-adv-x="609" 
d="M456 353c0 49.333 -12.833 86.832 -38.5 112.499s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l93 -79c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5s-35.167 -22.5 -60.5 -22.5h-59v63h41
c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-96 75c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16
s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-533h-93c-17.333 15.333 -37.333 28.666 -60 39.999s-49 17 -79 17c-32.667 0 -60.334 -5 -83.001 -15s-44.667 -23.667 -66 -41l-10 29l-10 30c18.667 17.333 41.167 32 67.5 44
s57.5 18 93.5 18c32 0 63 -6.83301 93 -20.5s53.667 -29.834 71 -48.501v480z" />
    <glyph glyph-name="uni0E0F.alt1" unicode="&#xf71c;" horiz-adv-x="609" 
d="M532 -180l-72.999 -0.00292969l-123 74c-10.667 -24.667 -27.5 -44.167 -50.5 -58.5s-47.167 -21.5 -72.5 -21.5c-24.667 0 -48 3 -70 9s-39.667 13.667 -53 23l10 32l10 32c13.333 -9.33301 29.166 -17.5 47.499 -24.5s35.166 -10.5 50.499 -10.5c28 0 52 9.5 72 28.5
s33 42.833 39 71.5l137 -82v460c0 49.333 -12.833 86.833 -38.5 112.5s-62.834 38.5 -111.501 38.5c-40 0 -75 -9.16699 -105 -27.5s-50.667 -41.166 -62 -68.499l71 -57l22 -22c-38.667 -16.667 -58 -46.667 -58 -90v-155c0 -26 -7.5 -46.5 -22.5 -61.5
s-35.167 -22.5 -60.5 -22.5h-59v63h41c9.33301 0 15.833 2.33301 19.5 7s5.5 11.334 5.5 20.001v139c0 20.667 5 40 15 58s23.667 31.333 41 40l-47.5 37.5l-48.5 37.5c6 20.667 16.333 41.167 31 61.5s32.834 38.166 54.501 53.499s46.334 27.833 74.001 37.5
s57.167 14.5 88.5 14.5c37.333 0 70 -5.33301 98 -16s51.5 -25.667 70.5 -45s33.333 -42.166 43 -68.499s14.5 -55.166 14.5 -86.499v-533z" />
    <glyph glyph-name="uni0E2C.alt1" unicode="&#xf71d;" horiz-adv-x="655" 
d="M502 346c0 28 -6.16992 48.835 -18.5029 62.502s-28.833 20.5 -49.5 20.5h-20c-2 5.33301 -3.83301 10.666 -5.5 15.999s-3.5 10.666 -5.5 15.999l54 68.5l54 69.5h83l-55.5 -66l-56.5 -65c30 -4 53.167 -17.167 69.5 -39.5s24.5 -49.5 24.5 -81.5v-347h-82l-137 265
c-8 15.333 -14.667 30.5 -20 45.5s-8.66602 26.167 -9.99902 33.5h-2c-2 -7.33301 -5.66699 -18.5 -11 -33.5s-11.666 -30.167 -18.999 -45.5l-134 -265h-82v560h74v-325c0 -27.333 -0.5 -53 -1.5 -77s-3.16699 -48.333 -6.5 -73h1c6 20.667 12.167 40 18.5 58
s13.833 35.333 22.5 52l108 208h61l111 -207c9.33301 -16.667 17.333 -35 24 -55l18 -56h2c-3.33301 24.667 -5.66602 49 -6.99902 73s-2 49.667 -2 77v111z" />
    <glyph glyph-name="uni0E33.alt1" horiz-adv-x="467" 
d="M-317 622c-13.333 0 -25.666 2.3291 -36.999 6.99609s-21.166 11.334 -29.499 20.001s-15 18.834 -20 30.501s-7.5 24.167 -7.5 37.5s2.5 25.833 7.5 37.5s11.667 21.834 20 30.501s18.166 15.5 29.499 20.5s23.666 7.5 36.999 7.5
c26.667 0 49.334 -9.16699 68.001 -27.5s28 -41.166 28 -68.499s-9.33301 -50 -28 -68s-41.334 -27 -68.001 -27zM-317 671.996c12.667 0 23.334 4.33301 32.001 13s13 19.667 13 33s-4.33301 24.166 -13 32.499s-19.334 12.5 -32.001 12.5
c-11.333 0 -21.333 -4.16699 -30 -12.5s-13 -19.166 -13 -32.499s4.33301 -24.333 13 -33s18.667 -13 30 -13zM314 -0.00390625v363c0 48 -10.167 83.5 -30.5 106.5s-50.833 34.5 -91.5 34.5c-32 0 -62.167 -5.83301 -90.5 -17.5s-52.166 -25.167 -71.499 -40.5l-20 57
c10 8 21.833 16 35.5 24s28.667 15 45 21s33.666 11 51.999 15s36.5 6 54.5 6c64.667 0 113 -18 145 -54s48 -86.667 48 -152v-363h-76z" />
    <glyph glyph-name="uni0E4E.alt1" 
d="M-252 650c-8 -4.66699 -16.332 -7.99902 -24.999 -9.99902s-19 -3 -31 -3c-12.667 0 -25 1.66699 -37 5s-22.667 8.33301 -32 15s-16.833 15.167 -22.5 25.5s-8.5 22.833 -8.5 37.5c0 12 2 23.167 6 33.5s10.333 19.166 19 26.499s19.667 12.666 33 15.999s29.333 4 48 2
c-9.33301 9.33301 -14 21.333 -14 36c0 12.667 5.5 24 16.5 34s28.5 15 52.5 15h56l12 -50h-55c-7.33301 0 -12.833 -2.16699 -16.5 -6.5s-5.5 -9.16602 -5.5 -14.499c0 -12.667 8 -23 24 -31l-14 -37c-9.33301 2.66699 -18.5 4.66699 -27.5 6s-18.5 2 -28.5 2
c-28 0 -42 -10.667 -42 -32c0 -10 4 -18 12 -24s19.333 -9 34 -9c7.33301 0 14.666 0.333008 21.999 1s15.333 2.66699 24 6v-44z" />
    <hkern u1="&#x22;" g2="fl" k="-10" />
    <hkern u1="&#x22;" g2="fi" k="-10" />
    <hkern u1="&#x22;" u2="&#x161;" k="10" />
    <hkern u1="&#x22;" u2="&#x153;" k="20" />
    <hkern u1="&#x22;" u2="&#x152;" k="10" />
    <hkern u1="&#x22;" u2="&#xf6;" k="20" />
    <hkern u1="&#x22;" u2="&#xf5;" k="20" />
    <hkern u1="&#x22;" u2="&#xf4;" k="20" />
    <hkern u1="&#x22;" u2="&#xf3;" k="20" />
    <hkern u1="&#x22;" u2="&#xf2;" k="20" />
    <hkern u1="&#x22;" u2="&#xf0;" k="20" />
    <hkern u1="&#x22;" u2="&#xeb;" k="20" />
    <hkern u1="&#x22;" u2="&#xea;" k="20" />
    <hkern u1="&#x22;" u2="&#xe9;" k="20" />
    <hkern u1="&#x22;" u2="&#xe8;" k="20" />
    <hkern u1="&#x22;" u2="&#xe7;" k="20" />
    <hkern u1="&#x22;" u2="&#xd8;" k="10" />
    <hkern u1="&#x22;" u2="&#xd6;" k="10" />
    <hkern u1="&#x22;" u2="&#xd5;" k="10" />
    <hkern u1="&#x22;" u2="&#xd4;" k="10" />
    <hkern u1="&#x22;" u2="&#xd3;" k="10" />
    <hkern u1="&#x22;" u2="&#xd2;" k="10" />
    <hkern u1="&#x22;" u2="&#xc6;" k="67" />
    <hkern u1="&#x22;" u2="&#xc5;" k="67" />
    <hkern u1="&#x22;" u2="&#xc4;" k="67" />
    <hkern u1="&#x22;" u2="&#xc3;" k="67" />
    <hkern u1="&#x22;" u2="&#xc2;" k="67" />
    <hkern u1="&#x22;" u2="&#xc1;" k="67" />
    <hkern u1="&#x22;" u2="&#xc0;" k="67" />
    <hkern u1="&#x22;" u2="x" k="20" />
    <hkern u1="&#x22;" u2="t" k="-10" />
    <hkern u1="&#x22;" u2="s" k="10" />
    <hkern u1="&#x22;" u2="q" k="20" />
    <hkern u1="&#x22;" u2="o" k="20" />
    <hkern u1="&#x22;" u2="g" k="20" />
    <hkern u1="&#x22;" u2="f" k="-10" />
    <hkern u1="&#x22;" u2="e" k="20" />
    <hkern u1="&#x22;" u2="d" k="20" />
    <hkern u1="&#x22;" u2="c" k="20" />
    <hkern u1="&#x22;" u2="T" k="-10" />
    <hkern u1="&#x22;" u2="Q" k="10" />
    <hkern u1="&#x22;" u2="O" k="10" />
    <hkern u1="&#x22;" u2="A" k="67" />
    <hkern u1="&#x27;" g2="fl" k="-10" />
    <hkern u1="&#x27;" g2="fi" k="-10" />
    <hkern u1="&#x27;" u2="&#x161;" k="10" />
    <hkern u1="&#x27;" u2="&#x153;" k="20" />
    <hkern u1="&#x27;" u2="&#x152;" k="10" />
    <hkern u1="&#x27;" u2="&#xf6;" k="20" />
    <hkern u1="&#x27;" u2="&#xf5;" k="20" />
    <hkern u1="&#x27;" u2="&#xf4;" k="20" />
    <hkern u1="&#x27;" u2="&#xf3;" k="20" />
    <hkern u1="&#x27;" u2="&#xf2;" k="20" />
    <hkern u1="&#x27;" u2="&#xf0;" k="20" />
    <hkern u1="&#x27;" u2="&#xeb;" k="20" />
    <hkern u1="&#x27;" u2="&#xea;" k="20" />
    <hkern u1="&#x27;" u2="&#xe9;" k="20" />
    <hkern u1="&#x27;" u2="&#xe8;" k="20" />
    <hkern u1="&#x27;" u2="&#xe7;" k="20" />
    <hkern u1="&#x27;" u2="&#xd8;" k="10" />
    <hkern u1="&#x27;" u2="&#xd6;" k="10" />
    <hkern u1="&#x27;" u2="&#xd5;" k="10" />
    <hkern u1="&#x27;" u2="&#xd4;" k="10" />
    <hkern u1="&#x27;" u2="&#xd3;" k="10" />
    <hkern u1="&#x27;" u2="&#xd2;" k="10" />
    <hkern u1="&#x27;" u2="&#xc6;" k="67" />
    <hkern u1="&#x27;" u2="&#xc5;" k="67" />
    <hkern u1="&#x27;" u2="&#xc4;" k="67" />
    <hkern u1="&#x27;" u2="&#xc3;" k="67" />
    <hkern u1="&#x27;" u2="&#xc2;" k="67" />
    <hkern u1="&#x27;" u2="&#xc1;" k="67" />
    <hkern u1="&#x27;" u2="&#xc0;" k="67" />
    <hkern u1="&#x27;" u2="x" k="20" />
    <hkern u1="&#x27;" u2="t" k="-10" />
    <hkern u1="&#x27;" u2="s" k="10" />
    <hkern u1="&#x27;" u2="q" k="20" />
    <hkern u1="&#x27;" u2="o" k="20" />
    <hkern u1="&#x27;" u2="g" k="20" />
    <hkern u1="&#x27;" u2="f" k="-10" />
    <hkern u1="&#x27;" u2="e" k="20" />
    <hkern u1="&#x27;" u2="d" k="20" />
    <hkern u1="&#x27;" u2="c" k="20" />
    <hkern u1="&#x27;" u2="T" k="-10" />
    <hkern u1="&#x27;" u2="Q" k="10" />
    <hkern u1="&#x27;" u2="O" k="10" />
    <hkern u1="&#x27;" u2="A" k="67" />
    <hkern u1="A" u2="&#x201d;" k="71" />
    <hkern u1="A" u2="&#x2019;" k="71" />
    <hkern u1="A" u2="&#x178;" k="100" />
    <hkern u1="A" u2="&#x153;" k="10" />
    <hkern u1="A" u2="&#x152;" k="20" />
    <hkern u1="A" u2="&#xff;" k="10" />
    <hkern u1="A" u2="&#xfd;" k="10" />
    <hkern u1="A" u2="&#xfc;" k="10" />
    <hkern u1="A" u2="&#xfb;" k="10" />
    <hkern u1="A" u2="&#xfa;" k="10" />
    <hkern u1="A" u2="&#xf9;" k="10" />
    <hkern u1="A" u2="&#xf6;" k="10" />
    <hkern u1="A" u2="&#xf5;" k="10" />
    <hkern u1="A" u2="&#xf4;" k="10" />
    <hkern u1="A" u2="&#xf3;" k="10" />
    <hkern u1="A" u2="&#xf2;" k="10" />
    <hkern u1="A" u2="&#xf0;" k="10" />
    <hkern u1="A" u2="&#xeb;" k="10" />
    <hkern u1="A" u2="&#xea;" k="10" />
    <hkern u1="A" u2="&#xe9;" k="10" />
    <hkern u1="A" u2="&#xe8;" k="10" />
    <hkern u1="A" u2="&#xe7;" k="10" />
    <hkern u1="A" u2="&#xdd;" k="100" />
    <hkern u1="A" u2="&#xdc;" k="14" />
    <hkern u1="A" u2="&#xdb;" k="14" />
    <hkern u1="A" u2="&#xda;" k="14" />
    <hkern u1="A" u2="&#xd9;" k="14" />
    <hkern u1="A" u2="&#xd8;" k="20" />
    <hkern u1="A" u2="&#xd6;" k="20" />
    <hkern u1="A" u2="&#xd5;" k="20" />
    <hkern u1="A" u2="&#xd4;" k="20" />
    <hkern u1="A" u2="&#xd3;" k="20" />
    <hkern u1="A" u2="&#xd2;" k="20" />
    <hkern u1="A" u2="&#xc7;" k="20" />
    <hkern u1="A" u2="&#xc6;" k="-6" />
    <hkern u1="A" u2="&#xc5;" k="-6" />
    <hkern u1="A" u2="&#xc4;" k="-6" />
    <hkern u1="A" u2="&#xc3;" k="-6" />
    <hkern u1="A" u2="&#xc2;" k="-6" />
    <hkern u1="A" u2="&#xc1;" k="-6" />
    <hkern u1="A" u2="&#xc0;" k="-6" />
    <hkern u1="A" u2="y" k="10" />
    <hkern u1="A" u2="x" k="10" />
    <hkern u1="A" u2="w" k="29" />
    <hkern u1="A" u2="v" k="51" />
    <hkern u1="A" u2="u" k="10" />
    <hkern u1="A" u2="t" k="14" />
    <hkern u1="A" u2="q" k="10" />
    <hkern u1="A" u2="o" k="10" />
    <hkern u1="A" u2="g" k="10" />
    <hkern u1="A" u2="e" k="10" />
    <hkern u1="A" u2="d" k="10" />
    <hkern u1="A" u2="c" k="10" />
    <hkern u1="A" u2="Y" k="100" />
    <hkern u1="A" u2="X" k="20" />
    <hkern u1="A" u2="W" k="73" />
    <hkern u1="A" u2="V" k="112" />
    <hkern u1="A" u2="U" k="14" />
    <hkern u1="A" u2="T" k="57" />
    <hkern u1="A" u2="Q" k="20" />
    <hkern u1="A" u2="O" k="20" />
    <hkern u1="A" u2="J" k="-14" />
    <hkern u1="A" u2="G" k="20" />
    <hkern u1="A" u2="C" k="20" />
    <hkern u1="A" u2="A" k="-6" />
    <hkern u1="A" u2="&#x27;" k="67" />
    <hkern u1="A" u2="&#x22;" k="67" />
    <hkern u1="B" u2="&#x17d;" k="10" />
    <hkern u1="B" u2="&#x178;" k="20" />
    <hkern u1="B" u2="&#x160;" k="10" />
    <hkern u1="B" u2="&#xff;" k="10" />
    <hkern u1="B" u2="&#xfd;" k="10" />
    <hkern u1="B" u2="&#xfc;" k="10" />
    <hkern u1="B" u2="&#xfb;" k="10" />
    <hkern u1="B" u2="&#xfa;" k="10" />
    <hkern u1="B" u2="&#xf9;" k="10" />
    <hkern u1="B" u2="&#xdd;" k="20" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="x" k="10" />
    <hkern u1="B" u2="w" k="10" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="u" k="10" />
    <hkern u1="B" u2="Z" k="10" />
    <hkern u1="B" u2="Y" k="20" />
    <hkern u1="B" u2="X" k="14" />
    <hkern u1="B" u2="W" k="10" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="S" k="10" />
    <hkern u1="B" u2="J" k="4" />
    <hkern u1="C" u2="&#x178;" k="10" />
    <hkern u1="C" u2="&#x161;" k="10" />
    <hkern u1="C" u2="&#x153;" k="20" />
    <hkern u1="C" u2="&#x152;" k="37" />
    <hkern u1="C" u2="&#xff;" k="10" />
    <hkern u1="C" u2="&#xfd;" k="10" />
    <hkern u1="C" u2="&#xfc;" k="10" />
    <hkern u1="C" u2="&#xfb;" k="10" />
    <hkern u1="C" u2="&#xfa;" k="10" />
    <hkern u1="C" u2="&#xf9;" k="10" />
    <hkern u1="C" u2="&#xf6;" k="20" />
    <hkern u1="C" u2="&#xf5;" k="20" />
    <hkern u1="C" u2="&#xf4;" k="20" />
    <hkern u1="C" u2="&#xf3;" k="20" />
    <hkern u1="C" u2="&#xf2;" k="20" />
    <hkern u1="C" u2="&#xf0;" k="20" />
    <hkern u1="C" u2="&#xeb;" k="20" />
    <hkern u1="C" u2="&#xea;" k="20" />
    <hkern u1="C" u2="&#xe9;" k="20" />
    <hkern u1="C" u2="&#xe8;" k="20" />
    <hkern u1="C" u2="&#xe7;" k="20" />
    <hkern u1="C" u2="&#xe6;" k="10" />
    <hkern u1="C" u2="&#xe5;" k="10" />
    <hkern u1="C" u2="&#xe4;" k="10" />
    <hkern u1="C" u2="&#xe3;" k="10" />
    <hkern u1="C" u2="&#xe2;" k="10" />
    <hkern u1="C" u2="&#xe1;" k="10" />
    <hkern u1="C" u2="&#xe0;" k="10" />
    <hkern u1="C" u2="&#xdd;" k="10" />
    <hkern u1="C" u2="&#xd8;" k="37" />
    <hkern u1="C" u2="&#xd6;" k="37" />
    <hkern u1="C" u2="&#xd5;" k="37" />
    <hkern u1="C" u2="&#xd4;" k="37" />
    <hkern u1="C" u2="&#xd3;" k="37" />
    <hkern u1="C" u2="&#xd2;" k="37" />
    <hkern u1="C" u2="&#xc7;" k="20" />
    <hkern u1="C" u2="y" k="10" />
    <hkern u1="C" u2="x" k="10" />
    <hkern u1="C" u2="w" k="20" />
    <hkern u1="C" u2="v" k="45" />
    <hkern u1="C" u2="u" k="10" />
    <hkern u1="C" u2="s" k="10" />
    <hkern u1="C" u2="q" k="20" />
    <hkern u1="C" u2="o" k="20" />
    <hkern u1="C" u2="g" k="20" />
    <hkern u1="C" u2="e" k="20" />
    <hkern u1="C" u2="d" k="20" />
    <hkern u1="C" u2="c" k="20" />
    <hkern u1="C" u2="a" k="10" />
    <hkern u1="C" u2="Y" k="10" />
    <hkern u1="C" u2="X" k="14" />
    <hkern u1="C" u2="Q" k="37" />
    <hkern u1="C" u2="O" k="37" />
    <hkern u1="C" u2="J" k="-10" />
    <hkern u1="C" u2="G" k="31" />
    <hkern u1="C" u2="C" k="20" />
    <hkern u1="D" u2="&#x17d;" k="14" />
    <hkern u1="D" u2="&#x178;" k="22" />
    <hkern u1="D" u2="&#x160;" k="10" />
    <hkern u1="D" u2="&#xdd;" k="22" />
    <hkern u1="D" u2="&#xc6;" k="24" />
    <hkern u1="D" u2="&#xc5;" k="24" />
    <hkern u1="D" u2="&#xc4;" k="24" />
    <hkern u1="D" u2="&#xc3;" k="24" />
    <hkern u1="D" u2="&#xc2;" k="24" />
    <hkern u1="D" u2="&#xc1;" k="24" />
    <hkern u1="D" u2="&#xc0;" k="24" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="v" k="10" />
    <hkern u1="D" u2="Z" k="14" />
    <hkern u1="D" u2="Y" k="22" />
    <hkern u1="D" u2="X" k="24" />
    <hkern u1="D" u2="W" k="5" />
    <hkern u1="D" u2="V" k="20" />
    <hkern u1="D" u2="T" k="10" />
    <hkern u1="D" u2="S" k="10" />
    <hkern u1="D" u2="J" k="24" />
    <hkern u1="D" u2="A" k="24" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="&#x152;" k="10" />
    <hkern u1="E" u2="&#xff;" k="10" />
    <hkern u1="E" u2="&#xfd;" k="10" />
    <hkern u1="E" u2="&#xfc;" k="10" />
    <hkern u1="E" u2="&#xfb;" k="10" />
    <hkern u1="E" u2="&#xfa;" k="10" />
    <hkern u1="E" u2="&#xf9;" k="10" />
    <hkern u1="E" u2="&#xf6;" k="10" />
    <hkern u1="E" u2="&#xf5;" k="10" />
    <hkern u1="E" u2="&#xf4;" k="10" />
    <hkern u1="E" u2="&#xf3;" k="10" />
    <hkern u1="E" u2="&#xf2;" k="10" />
    <hkern u1="E" u2="&#xf0;" k="10" />
    <hkern u1="E" u2="&#xeb;" k="10" />
    <hkern u1="E" u2="&#xea;" k="10" />
    <hkern u1="E" u2="&#xe9;" k="10" />
    <hkern u1="E" u2="&#xe8;" k="10" />
    <hkern u1="E" u2="&#xe7;" k="10" />
    <hkern u1="E" u2="&#xe6;" k="10" />
    <hkern u1="E" u2="&#xe5;" k="10" />
    <hkern u1="E" u2="&#xe4;" k="10" />
    <hkern u1="E" u2="&#xe3;" k="10" />
    <hkern u1="E" u2="&#xe2;" k="10" />
    <hkern u1="E" u2="&#xe1;" k="10" />
    <hkern u1="E" u2="&#xe0;" k="10" />
    <hkern u1="E" u2="&#xd8;" k="10" />
    <hkern u1="E" u2="&#xd6;" k="10" />
    <hkern u1="E" u2="&#xd5;" k="10" />
    <hkern u1="E" u2="&#xd4;" k="10" />
    <hkern u1="E" u2="&#xd3;" k="10" />
    <hkern u1="E" u2="&#xd2;" k="10" />
    <hkern u1="E" u2="y" k="10" />
    <hkern u1="E" u2="x" k="10" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="u" k="10" />
    <hkern u1="E" u2="q" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="g" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="E" u2="a" k="10" />
    <hkern u1="E" u2="X" k="10" />
    <hkern u1="E" u2="Q" k="10" />
    <hkern u1="E" u2="O" k="10" />
    <hkern u1="E" u2="G" k="10" />
    <hkern u1="F" u2="&#x17e;" k="10" />
    <hkern u1="F" u2="&#x17d;" k="10" />
    <hkern u1="F" u2="&#x153;" k="10" />
    <hkern u1="F" u2="&#x152;" k="10" />
    <hkern u1="F" u2="&#xf6;" k="10" />
    <hkern u1="F" u2="&#xf5;" k="10" />
    <hkern u1="F" u2="&#xf4;" k="10" />
    <hkern u1="F" u2="&#xf3;" k="10" />
    <hkern u1="F" u2="&#xf2;" k="10" />
    <hkern u1="F" u2="&#xf0;" k="10" />
    <hkern u1="F" u2="&#xe7;" k="10" />
    <hkern u1="F" u2="&#xe6;" k="16" />
    <hkern u1="F" u2="&#xe5;" k="16" />
    <hkern u1="F" u2="&#xe4;" k="16" />
    <hkern u1="F" u2="&#xe3;" k="16" />
    <hkern u1="F" u2="&#xe2;" k="16" />
    <hkern u1="F" u2="&#xe1;" k="16" />
    <hkern u1="F" u2="&#xe0;" k="16" />
    <hkern u1="F" u2="&#xd8;" k="10" />
    <hkern u1="F" u2="&#xd6;" k="10" />
    <hkern u1="F" u2="&#xd5;" k="10" />
    <hkern u1="F" u2="&#xd4;" k="10" />
    <hkern u1="F" u2="&#xd3;" k="10" />
    <hkern u1="F" u2="&#xd2;" k="10" />
    <hkern u1="F" u2="&#xc6;" k="51" />
    <hkern u1="F" u2="&#xc5;" k="51" />
    <hkern u1="F" u2="&#xc4;" k="51" />
    <hkern u1="F" u2="&#xc3;" k="51" />
    <hkern u1="F" u2="&#xc2;" k="51" />
    <hkern u1="F" u2="&#xc1;" k="51" />
    <hkern u1="F" u2="&#xc0;" k="51" />
    <hkern u1="F" u2="z" k="10" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="q" k="10" />
    <hkern u1="F" u2="o" k="10" />
    <hkern u1="F" u2="g" k="10" />
    <hkern u1="F" u2="d" k="10" />
    <hkern u1="F" u2="c" k="10" />
    <hkern u1="F" u2="a" k="16" />
    <hkern u1="F" u2="Z" k="10" />
    <hkern u1="F" u2="X" k="20" />
    <hkern u1="F" u2="Q" k="10" />
    <hkern u1="F" u2="O" k="10" />
    <hkern u1="F" u2="J" k="51" />
    <hkern u1="F" u2="A" k="51" />
    <hkern u1="G" u2="x" k="10" />
    <hkern u1="G" u2="v" k="10" />
    <hkern u1="G" u2="V" k="12" />
    <hkern u1="G" u2="T" k="10" />
    <hkern u1="G" u2="&#x27;" k="16" />
    <hkern u1="G" u2="&#x22;" k="16" />
    <hkern u1="J" u2="&#xc6;" k="14" />
    <hkern u1="J" u2="&#xc5;" k="14" />
    <hkern u1="J" u2="&#xc4;" k="14" />
    <hkern u1="J" u2="&#xc3;" k="14" />
    <hkern u1="J" u2="&#xc2;" k="14" />
    <hkern u1="J" u2="&#xc1;" k="14" />
    <hkern u1="J" u2="&#xc0;" k="14" />
    <hkern u1="J" u2="A" k="14" />
    <hkern u1="K" g2="fl" k="20" />
    <hkern u1="K" g2="fi" k="20" />
    <hkern u1="K" u2="&#x160;" k="10" />
    <hkern u1="K" u2="&#x153;" k="24" />
    <hkern u1="K" u2="&#x152;" k="37" />
    <hkern u1="K" u2="&#xff;" k="20" />
    <hkern u1="K" u2="&#xfd;" k="20" />
    <hkern u1="K" u2="&#xfc;" k="20" />
    <hkern u1="K" u2="&#xfb;" k="20" />
    <hkern u1="K" u2="&#xfa;" k="20" />
    <hkern u1="K" u2="&#xf9;" k="20" />
    <hkern u1="K" u2="&#xf6;" k="24" />
    <hkern u1="K" u2="&#xf5;" k="24" />
    <hkern u1="K" u2="&#xf4;" k="24" />
    <hkern u1="K" u2="&#xf3;" k="24" />
    <hkern u1="K" u2="&#xf2;" k="24" />
    <hkern u1="K" u2="&#xf0;" k="22" />
    <hkern u1="K" u2="&#xeb;" k="22" />
    <hkern u1="K" u2="&#xea;" k="22" />
    <hkern u1="K" u2="&#xe9;" k="22" />
    <hkern u1="K" u2="&#xe8;" k="22" />
    <hkern u1="K" u2="&#xe7;" k="22" />
    <hkern u1="K" u2="&#xe6;" k="20" />
    <hkern u1="K" u2="&#xe5;" k="20" />
    <hkern u1="K" u2="&#xe4;" k="20" />
    <hkern u1="K" u2="&#xe3;" k="20" />
    <hkern u1="K" u2="&#xe2;" k="20" />
    <hkern u1="K" u2="&#xe1;" k="20" />
    <hkern u1="K" u2="&#xe0;" k="20" />
    <hkern u1="K" u2="&#xdc;" k="16" />
    <hkern u1="K" u2="&#xdb;" k="16" />
    <hkern u1="K" u2="&#xda;" k="16" />
    <hkern u1="K" u2="&#xd9;" k="16" />
    <hkern u1="K" u2="&#xd8;" k="37" />
    <hkern u1="K" u2="&#xd6;" k="37" />
    <hkern u1="K" u2="&#xd5;" k="37" />
    <hkern u1="K" u2="&#xd4;" k="37" />
    <hkern u1="K" u2="&#xd3;" k="37" />
    <hkern u1="K" u2="&#xd2;" k="37" />
    <hkern u1="K" u2="&#xc7;" k="31" />
    <hkern u1="K" u2="y" k="20" />
    <hkern u1="K" u2="x" k="20" />
    <hkern u1="K" u2="w" k="22" />
    <hkern u1="K" u2="v" k="41" />
    <hkern u1="K" u2="u" k="20" />
    <hkern u1="K" u2="q" k="22" />
    <hkern u1="K" u2="o" k="24" />
    <hkern u1="K" u2="g" k="22" />
    <hkern u1="K" u2="f" k="20" />
    <hkern u1="K" u2="e" k="22" />
    <hkern u1="K" u2="d" k="22" />
    <hkern u1="K" u2="c" k="22" />
    <hkern u1="K" u2="a" k="20" />
    <hkern u1="K" u2="X" k="10" />
    <hkern u1="K" u2="W" k="6" />
    <hkern u1="K" u2="U" k="16" />
    <hkern u1="K" u2="S" k="10" />
    <hkern u1="K" u2="Q" k="37" />
    <hkern u1="K" u2="O" k="37" />
    <hkern u1="K" u2="J" k="-10" />
    <hkern u1="K" u2="G" k="31" />
    <hkern u1="K" u2="C" k="31" />
    <hkern u1="K" u2="&#x27;" k="10" />
    <hkern u1="K" u2="&#x22;" k="10" />
    <hkern u1="L" u2="&#x201d;" k="82" />
    <hkern u1="L" u2="&#x2019;" k="82" />
    <hkern u1="L" u2="&#x178;" k="51" />
    <hkern u1="L" u2="&#x153;" k="10" />
    <hkern u1="L" u2="&#x152;" k="22" />
    <hkern u1="L" u2="&#xff;" k="20" />
    <hkern u1="L" u2="&#xfd;" k="20" />
    <hkern u1="L" u2="&#xfc;" k="20" />
    <hkern u1="L" u2="&#xfb;" k="20" />
    <hkern u1="L" u2="&#xfa;" k="20" />
    <hkern u1="L" u2="&#xf9;" k="20" />
    <hkern u1="L" u2="&#xf6;" k="10" />
    <hkern u1="L" u2="&#xf5;" k="10" />
    <hkern u1="L" u2="&#xf4;" k="10" />
    <hkern u1="L" u2="&#xf3;" k="10" />
    <hkern u1="L" u2="&#xf2;" k="10" />
    <hkern u1="L" u2="&#xf0;" k="10" />
    <hkern u1="L" u2="&#xeb;" k="10" />
    <hkern u1="L" u2="&#xea;" k="10" />
    <hkern u1="L" u2="&#xe9;" k="10" />
    <hkern u1="L" u2="&#xe8;" k="10" />
    <hkern u1="L" u2="&#xe7;" k="10" />
    <hkern u1="L" u2="&#xdd;" k="51" />
    <hkern u1="L" u2="&#xd8;" k="22" />
    <hkern u1="L" u2="&#xd6;" k="22" />
    <hkern u1="L" u2="&#xd5;" k="22" />
    <hkern u1="L" u2="&#xd4;" k="22" />
    <hkern u1="L" u2="&#xd3;" k="22" />
    <hkern u1="L" u2="&#xd2;" k="22" />
    <hkern u1="L" u2="&#xc7;" k="20" />
    <hkern u1="L" u2="&#xc6;" k="-10" />
    <hkern u1="L" u2="&#xc5;" k="-10" />
    <hkern u1="L" u2="&#xc4;" k="-10" />
    <hkern u1="L" u2="&#xc3;" k="-10" />
    <hkern u1="L" u2="&#xc2;" k="-10" />
    <hkern u1="L" u2="&#xc1;" k="-10" />
    <hkern u1="L" u2="&#xc0;" k="-10" />
    <hkern u1="L" u2="y" k="20" />
    <hkern u1="L" u2="w" k="14" />
    <hkern u1="L" u2="v" k="37" />
    <hkern u1="L" u2="u" k="20" />
    <hkern u1="L" u2="t" k="31" />
    <hkern u1="L" u2="q" k="10" />
    <hkern u1="L" u2="o" k="10" />
    <hkern u1="L" u2="g" k="10" />
    <hkern u1="L" u2="e" k="10" />
    <hkern u1="L" u2="d" k="10" />
    <hkern u1="L" u2="c" k="10" />
    <hkern u1="L" u2="Y" k="51" />
    <hkern u1="L" u2="X" k="10" />
    <hkern u1="L" u2="W" k="31" />
    <hkern u1="L" u2="V" k="47" />
    <hkern u1="L" u2="T" k="61" />
    <hkern u1="L" u2="Q" k="22" />
    <hkern u1="L" u2="O" k="22" />
    <hkern u1="L" u2="J" k="-10" />
    <hkern u1="L" u2="G" k="20" />
    <hkern u1="L" u2="C" k="20" />
    <hkern u1="L" u2="A" k="-10" />
    <hkern u1="L" u2="&#x27;" k="61" />
    <hkern u1="L" u2="&#x22;" k="61" />
    <hkern u1="O" u2="&#x17d;" k="4" />
    <hkern u1="O" u2="&#x178;" k="24" />
    <hkern u1="O" u2="&#xdd;" k="24" />
    <hkern u1="O" u2="&#xc6;" k="20" />
    <hkern u1="O" u2="&#xc5;" k="20" />
    <hkern u1="O" u2="&#xc4;" k="20" />
    <hkern u1="O" u2="&#xc3;" k="20" />
    <hkern u1="O" u2="&#xc2;" k="20" />
    <hkern u1="O" u2="&#xc1;" k="20" />
    <hkern u1="O" u2="&#xc0;" k="20" />
    <hkern u1="O" u2="Z" k="4" />
    <hkern u1="O" u2="Y" k="24" />
    <hkern u1="O" u2="X" k="27" />
    <hkern u1="O" u2="W" k="5" />
    <hkern u1="O" u2="V" k="15" />
    <hkern u1="O" u2="T" k="10" />
    <hkern u1="O" u2="J" k="10" />
    <hkern u1="O" u2="A" k="20" />
    <hkern u1="O" u2="&#x27;" k="10" />
    <hkern u1="O" u2="&#x22;" k="10" />
    <hkern u1="P" u2="&#x17e;" k="10" />
    <hkern u1="P" u2="&#x17d;" k="10" />
    <hkern u1="P" u2="&#x178;" k="10" />
    <hkern u1="P" u2="&#x161;" k="10" />
    <hkern u1="P" u2="&#x153;" k="20" />
    <hkern u1="P" u2="&#xf6;" k="20" />
    <hkern u1="P" u2="&#xf5;" k="20" />
    <hkern u1="P" u2="&#xf4;" k="20" />
    <hkern u1="P" u2="&#xf3;" k="20" />
    <hkern u1="P" u2="&#xf2;" k="20" />
    <hkern u1="P" u2="&#xf0;" k="20" />
    <hkern u1="P" u2="&#xeb;" k="20" />
    <hkern u1="P" u2="&#xea;" k="20" />
    <hkern u1="P" u2="&#xe9;" k="20" />
    <hkern u1="P" u2="&#xe8;" k="20" />
    <hkern u1="P" u2="&#xe7;" k="20" />
    <hkern u1="P" u2="&#xe6;" k="33" />
    <hkern u1="P" u2="&#xe5;" k="33" />
    <hkern u1="P" u2="&#xe4;" k="33" />
    <hkern u1="P" u2="&#xe3;" k="33" />
    <hkern u1="P" u2="&#xe2;" k="33" />
    <hkern u1="P" u2="&#xe1;" k="33" />
    <hkern u1="P" u2="&#xe0;" k="33" />
    <hkern u1="P" u2="&#xdd;" k="10" />
    <hkern u1="P" u2="&#xc7;" k="10" />
    <hkern u1="P" u2="&#xc6;" k="65" />
    <hkern u1="P" u2="&#xc5;" k="65" />
    <hkern u1="P" u2="&#xc4;" k="65" />
    <hkern u1="P" u2="&#xc3;" k="65" />
    <hkern u1="P" u2="&#xc2;" k="65" />
    <hkern u1="P" u2="&#xc1;" k="65" />
    <hkern u1="P" u2="&#xc0;" k="65" />
    <hkern u1="P" u2="z" k="10" />
    <hkern u1="P" u2="x" k="20" />
    <hkern u1="P" u2="s" k="10" />
    <hkern u1="P" u2="q" k="20" />
    <hkern u1="P" u2="o" k="20" />
    <hkern u1="P" u2="g" k="20" />
    <hkern u1="P" u2="e" k="20" />
    <hkern u1="P" u2="d" k="20" />
    <hkern u1="P" u2="c" k="20" />
    <hkern u1="P" u2="a" k="33" />
    <hkern u1="P" u2="Z" k="10" />
    <hkern u1="P" u2="Y" k="10" />
    <hkern u1="P" u2="X" k="24" />
    <hkern u1="P" u2="V" k="10" />
    <hkern u1="P" u2="J" k="71" />
    <hkern u1="P" u2="G" k="10" />
    <hkern u1="P" u2="C" k="10" />
    <hkern u1="P" u2="A" k="65" />
    <hkern u1="Q" u2="&#x17d;" k="4" />
    <hkern u1="Q" u2="&#x178;" k="24" />
    <hkern u1="Q" u2="&#xdd;" k="24" />
    <hkern u1="Q" u2="&#xc6;" k="20" />
    <hkern u1="Q" u2="&#xc5;" k="20" />
    <hkern u1="Q" u2="&#xc4;" k="20" />
    <hkern u1="Q" u2="&#xc3;" k="20" />
    <hkern u1="Q" u2="&#xc2;" k="20" />
    <hkern u1="Q" u2="&#xc1;" k="20" />
    <hkern u1="Q" u2="&#xc0;" k="20" />
    <hkern u1="Q" u2="Z" k="4" />
    <hkern u1="Q" u2="Y" k="24" />
    <hkern u1="Q" u2="X" k="27" />
    <hkern u1="Q" u2="W" k="5" />
    <hkern u1="Q" u2="V" k="15" />
    <hkern u1="Q" u2="T" k="10" />
    <hkern u1="Q" u2="J" k="10" />
    <hkern u1="Q" u2="A" k="20" />
    <hkern u1="Q" u2="&#x27;" k="10" />
    <hkern u1="Q" u2="&#x22;" k="10" />
    <hkern u1="R" g2="fl" k="-10" />
    <hkern u1="R" g2="fi" k="-10" />
    <hkern u1="R" u2="&#x178;" k="14" />
    <hkern u1="R" u2="&#x153;" k="10" />
    <hkern u1="R" u2="&#x152;" k="6" />
    <hkern u1="R" u2="&#xf6;" k="10" />
    <hkern u1="R" u2="&#xf5;" k="10" />
    <hkern u1="R" u2="&#xf4;" k="10" />
    <hkern u1="R" u2="&#xf3;" k="10" />
    <hkern u1="R" u2="&#xf2;" k="10" />
    <hkern u1="R" u2="&#xf0;" k="10" />
    <hkern u1="R" u2="&#xeb;" k="10" />
    <hkern u1="R" u2="&#xea;" k="10" />
    <hkern u1="R" u2="&#xe9;" k="10" />
    <hkern u1="R" u2="&#xe8;" k="10" />
    <hkern u1="R" u2="&#xe7;" k="10" />
    <hkern u1="R" u2="&#xe6;" k="10" />
    <hkern u1="R" u2="&#xe5;" k="10" />
    <hkern u1="R" u2="&#xe4;" k="10" />
    <hkern u1="R" u2="&#xe3;" k="10" />
    <hkern u1="R" u2="&#xe2;" k="10" />
    <hkern u1="R" u2="&#xe1;" k="10" />
    <hkern u1="R" u2="&#xe0;" k="10" />
    <hkern u1="R" u2="&#xdd;" k="14" />
    <hkern u1="R" u2="&#xd8;" k="6" />
    <hkern u1="R" u2="&#xd6;" k="6" />
    <hkern u1="R" u2="&#xd5;" k="6" />
    <hkern u1="R" u2="&#xd4;" k="6" />
    <hkern u1="R" u2="&#xd3;" k="6" />
    <hkern u1="R" u2="&#xd2;" k="6" />
    <hkern u1="R" u2="&#xc7;" k="6" />
    <hkern u1="R" u2="x" k="10" />
    <hkern u1="R" u2="q" k="10" />
    <hkern u1="R" u2="o" k="10" />
    <hkern u1="R" u2="g" k="10" />
    <hkern u1="R" u2="f" k="-10" />
    <hkern u1="R" u2="e" k="10" />
    <hkern u1="R" u2="d" k="10" />
    <hkern u1="R" u2="c" k="10" />
    <hkern u1="R" u2="a" k="10" />
    <hkern u1="R" u2="Y" k="14" />
    <hkern u1="R" u2="X" k="10" />
    <hkern u1="R" u2="V" k="6" />
    <hkern u1="R" u2="T" k="10" />
    <hkern u1="R" u2="Q" k="6" />
    <hkern u1="R" u2="O" k="6" />
    <hkern u1="R" u2="J" k="10" />
    <hkern u1="R" u2="G" k="6" />
    <hkern u1="R" u2="C" k="6" />
    <hkern u1="S" u2="&#x17e;" k="10" />
    <hkern u1="S" u2="&#x178;" k="16" />
    <hkern u1="S" u2="&#x152;" k="10" />
    <hkern u1="S" u2="&#xf0;" k="10" />
    <hkern u1="S" u2="&#xeb;" k="10" />
    <hkern u1="S" u2="&#xea;" k="10" />
    <hkern u1="S" u2="&#xe9;" k="10" />
    <hkern u1="S" u2="&#xe8;" k="10" />
    <hkern u1="S" u2="&#xe6;" k="10" />
    <hkern u1="S" u2="&#xe5;" k="10" />
    <hkern u1="S" u2="&#xe4;" k="10" />
    <hkern u1="S" u2="&#xe3;" k="10" />
    <hkern u1="S" u2="&#xe2;" k="10" />
    <hkern u1="S" u2="&#xe1;" k="10" />
    <hkern u1="S" u2="&#xe0;" k="10" />
    <hkern u1="S" u2="&#xdd;" k="16" />
    <hkern u1="S" u2="&#xd8;" k="10" />
    <hkern u1="S" u2="&#xd6;" k="10" />
    <hkern u1="S" u2="&#xd5;" k="10" />
    <hkern u1="S" u2="&#xd4;" k="10" />
    <hkern u1="S" u2="&#xd3;" k="10" />
    <hkern u1="S" u2="&#xd2;" k="10" />
    <hkern u1="S" u2="&#xc7;" k="10" />
    <hkern u1="S" u2="&#xc6;" k="10" />
    <hkern u1="S" u2="&#xc5;" k="10" />
    <hkern u1="S" u2="&#xc4;" k="10" />
    <hkern u1="S" u2="&#xc3;" k="10" />
    <hkern u1="S" u2="&#xc2;" k="10" />
    <hkern u1="S" u2="&#xc1;" k="10" />
    <hkern u1="S" u2="&#xc0;" k="10" />
    <hkern u1="S" u2="z" k="10" />
    <hkern u1="S" u2="x" k="20" />
    <hkern u1="S" u2="w" k="6" />
    <hkern u1="S" u2="v" k="24" />
    <hkern u1="S" u2="q" k="10" />
    <hkern u1="S" u2="g" k="10" />
    <hkern u1="S" u2="e" k="10" />
    <hkern u1="S" u2="d" k="10" />
    <hkern u1="S" u2="a" k="10" />
    <hkern u1="S" u2="Y" k="16" />
    <hkern u1="S" u2="X" k="12" />
    <hkern u1="S" u2="W" k="10" />
    <hkern u1="S" u2="V" k="16" />
    <hkern u1="S" u2="Q" k="10" />
    <hkern u1="S" u2="O" k="10" />
    <hkern u1="S" u2="C" k="10" />
    <hkern u1="S" u2="A" k="10" />
    <hkern u1="T" g2="fl" k="10" />
    <hkern u1="T" g2="fi" k="10" />
    <hkern u1="T" u2="&#x17e;" k="20" />
    <hkern u1="T" u2="&#x178;" k="-10" />
    <hkern u1="T" u2="&#x161;" k="20" />
    <hkern u1="T" u2="&#x153;" k="26" />
    <hkern u1="T" u2="&#x152;" k="10" />
    <hkern u1="T" u2="&#xff;" k="6" />
    <hkern u1="T" u2="&#xfd;" k="6" />
    <hkern u1="T" u2="&#xfc;" k="6" />
    <hkern u1="T" u2="&#xfb;" k="6" />
    <hkern u1="T" u2="&#xfa;" k="6" />
    <hkern u1="T" u2="&#xf9;" k="6" />
    <hkern u1="T" u2="&#xf6;" k="26" />
    <hkern u1="T" u2="&#xf5;" k="26" />
    <hkern u1="T" u2="&#xf4;" k="26" />
    <hkern u1="T" u2="&#xf3;" k="26" />
    <hkern u1="T" u2="&#xf2;" k="26" />
    <hkern u1="T" u2="&#xf0;" k="26" />
    <hkern u1="T" u2="&#xeb;" k="26" />
    <hkern u1="T" u2="&#xea;" k="26" />
    <hkern u1="T" u2="&#xe9;" k="26" />
    <hkern u1="T" u2="&#xe8;" k="26" />
    <hkern u1="T" u2="&#xe7;" k="26" />
    <hkern u1="T" u2="&#xe6;" k="22" />
    <hkern u1="T" u2="&#xe5;" k="22" />
    <hkern u1="T" u2="&#xe4;" k="22" />
    <hkern u1="T" u2="&#xe3;" k="22" />
    <hkern u1="T" u2="&#xe2;" k="22" />
    <hkern u1="T" u2="&#xe1;" k="22" />
    <hkern u1="T" u2="&#xe0;" k="22" />
    <hkern u1="T" u2="&#xdd;" k="-10" />
    <hkern u1="T" u2="&#xd8;" k="10" />
    <hkern u1="T" u2="&#xd6;" k="10" />
    <hkern u1="T" u2="&#xd5;" k="10" />
    <hkern u1="T" u2="&#xd4;" k="10" />
    <hkern u1="T" u2="&#xd3;" k="10" />
    <hkern u1="T" u2="&#xd2;" k="10" />
    <hkern u1="T" u2="&#xc7;" k="10" />
    <hkern u1="T" u2="&#xc6;" k="57" />
    <hkern u1="T" u2="&#xc5;" k="57" />
    <hkern u1="T" u2="&#xc4;" k="57" />
    <hkern u1="T" u2="&#xc3;" k="57" />
    <hkern u1="T" u2="&#xc2;" k="57" />
    <hkern u1="T" u2="&#xc1;" k="57" />
    <hkern u1="T" u2="&#xc0;" k="57" />
    <hkern u1="T" u2="z" k="20" />
    <hkern u1="T" u2="y" k="6" />
    <hkern u1="T" u2="x" k="31" />
    <hkern u1="T" u2="w" k="10" />
    <hkern u1="T" u2="v" k="15" />
    <hkern u1="T" u2="u" k="6" />
    <hkern u1="T" u2="s" k="20" />
    <hkern u1="T" u2="q" k="26" />
    <hkern u1="T" u2="o" k="26" />
    <hkern u1="T" u2="g" k="26" />
    <hkern u1="T" u2="f" k="10" />
    <hkern u1="T" u2="e" k="26" />
    <hkern u1="T" u2="d" k="26" />
    <hkern u1="T" u2="c" k="26" />
    <hkern u1="T" u2="a" k="22" />
    <hkern u1="T" u2="Y" k="-10" />
    <hkern u1="T" u2="W" k="-6" />
    <hkern u1="T" u2="V" k="-6" />
    <hkern u1="T" u2="T" k="-16" />
    <hkern u1="T" u2="Q" k="10" />
    <hkern u1="T" u2="O" k="10" />
    <hkern u1="T" u2="J" k="35" />
    <hkern u1="T" u2="G" k="10" />
    <hkern u1="T" u2="C" k="10" />
    <hkern u1="T" u2="A" k="57" />
    <hkern u1="T" u2="&#x27;" k="-10" />
    <hkern u1="T" u2="&#x22;" k="-10" />
    <hkern u1="U" u2="&#xf0;" k="6" />
    <hkern u1="U" u2="&#xeb;" k="6" />
    <hkern u1="U" u2="&#xea;" k="6" />
    <hkern u1="U" u2="&#xe9;" k="6" />
    <hkern u1="U" u2="&#xe8;" k="6" />
    <hkern u1="U" u2="&#xe6;" k="10" />
    <hkern u1="U" u2="&#xe5;" k="10" />
    <hkern u1="U" u2="&#xe4;" k="10" />
    <hkern u1="U" u2="&#xe3;" k="10" />
    <hkern u1="U" u2="&#xe2;" k="10" />
    <hkern u1="U" u2="&#xe1;" k="10" />
    <hkern u1="U" u2="&#xe0;" k="10" />
    <hkern u1="U" u2="&#xc6;" k="14" />
    <hkern u1="U" u2="&#xc5;" k="14" />
    <hkern u1="U" u2="&#xc4;" k="14" />
    <hkern u1="U" u2="&#xc3;" k="14" />
    <hkern u1="U" u2="&#xc2;" k="14" />
    <hkern u1="U" u2="&#xc1;" k="14" />
    <hkern u1="U" u2="&#xc0;" k="14" />
    <hkern u1="U" u2="x" k="14" />
    <hkern u1="U" u2="q" k="6" />
    <hkern u1="U" u2="g" k="6" />
    <hkern u1="U" u2="e" k="6" />
    <hkern u1="U" u2="d" k="6" />
    <hkern u1="U" u2="a" k="10" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="A" k="14" />
    <hkern u1="V" u2="&#x17e;" k="20" />
    <hkern u1="V" u2="&#x161;" k="5" />
    <hkern u1="V" u2="&#x153;" k="31" />
    <hkern u1="V" u2="&#x152;" k="15" />
    <hkern u1="V" u2="&#xff;" k="6" />
    <hkern u1="V" u2="&#xfd;" k="6" />
    <hkern u1="V" u2="&#xfc;" k="6" />
    <hkern u1="V" u2="&#xfb;" k="6" />
    <hkern u1="V" u2="&#xfa;" k="6" />
    <hkern u1="V" u2="&#xf9;" k="6" />
    <hkern u1="V" u2="&#xf6;" k="31" />
    <hkern u1="V" u2="&#xf5;" k="31" />
    <hkern u1="V" u2="&#xf4;" k="31" />
    <hkern u1="V" u2="&#xf3;" k="31" />
    <hkern u1="V" u2="&#xf2;" k="31" />
    <hkern u1="V" u2="&#xf0;" k="20" />
    <hkern u1="V" u2="&#xeb;" k="20" />
    <hkern u1="V" u2="&#xea;" k="20" />
    <hkern u1="V" u2="&#xe9;" k="20" />
    <hkern u1="V" u2="&#xe8;" k="20" />
    <hkern u1="V" u2="&#xe7;" k="20" />
    <hkern u1="V" u2="&#xe6;" k="20" />
    <hkern u1="V" u2="&#xe5;" k="20" />
    <hkern u1="V" u2="&#xe4;" k="20" />
    <hkern u1="V" u2="&#xe3;" k="20" />
    <hkern u1="V" u2="&#xe2;" k="20" />
    <hkern u1="V" u2="&#xe1;" k="20" />
    <hkern u1="V" u2="&#xe0;" k="20" />
    <hkern u1="V" u2="&#xd8;" k="15" />
    <hkern u1="V" u2="&#xd6;" k="15" />
    <hkern u1="V" u2="&#xd5;" k="15" />
    <hkern u1="V" u2="&#xd4;" k="15" />
    <hkern u1="V" u2="&#xd3;" k="15" />
    <hkern u1="V" u2="&#xd2;" k="15" />
    <hkern u1="V" u2="&#xc7;" k="15" />
    <hkern u1="V" u2="&#xc6;" k="112" />
    <hkern u1="V" u2="&#xc5;" k="112" />
    <hkern u1="V" u2="&#xc4;" k="112" />
    <hkern u1="V" u2="&#xc3;" k="112" />
    <hkern u1="V" u2="&#xc2;" k="112" />
    <hkern u1="V" u2="&#xc1;" k="112" />
    <hkern u1="V" u2="&#xc0;" k="112" />
    <hkern u1="V" u2="z" k="20" />
    <hkern u1="V" u2="y" k="6" />
    <hkern u1="V" u2="x" k="22" />
    <hkern u1="V" u2="w" k="5" />
    <hkern u1="V" u2="v" k="10" />
    <hkern u1="V" u2="u" k="6" />
    <hkern u1="V" u2="t" k="-4" />
    <hkern u1="V" u2="s" k="5" />
    <hkern u1="V" u2="q" k="20" />
    <hkern u1="V" u2="o" k="31" />
    <hkern u1="V" u2="g" k="20" />
    <hkern u1="V" u2="e" k="20" />
    <hkern u1="V" u2="d" k="20" />
    <hkern u1="V" u2="c" k="20" />
    <hkern u1="V" u2="a" k="20" />
    <hkern u1="V" u2="X" k="15" />
    <hkern u1="V" u2="V" k="-6" />
    <hkern u1="V" u2="T" k="-6" />
    <hkern u1="V" u2="Q" k="15" />
    <hkern u1="V" u2="O" k="15" />
    <hkern u1="V" u2="J" k="41" />
    <hkern u1="V" u2="G" k="20" />
    <hkern u1="V" u2="C" k="15" />
    <hkern u1="V" u2="A" k="112" />
    <hkern u1="W" u2="&#x17e;" k="6" />
    <hkern u1="W" u2="&#x17d;" k="5" />
    <hkern u1="W" u2="&#x161;" k="10" />
    <hkern u1="W" u2="&#x153;" k="10" />
    <hkern u1="W" u2="&#x152;" k="5" />
    <hkern u1="W" u2="&#xf6;" k="10" />
    <hkern u1="W" u2="&#xf5;" k="10" />
    <hkern u1="W" u2="&#xf4;" k="10" />
    <hkern u1="W" u2="&#xf3;" k="10" />
    <hkern u1="W" u2="&#xf2;" k="10" />
    <hkern u1="W" u2="&#xf0;" k="10" />
    <hkern u1="W" u2="&#xeb;" k="10" />
    <hkern u1="W" u2="&#xea;" k="10" />
    <hkern u1="W" u2="&#xe9;" k="10" />
    <hkern u1="W" u2="&#xe8;" k="10" />
    <hkern u1="W" u2="&#xe7;" k="10" />
    <hkern u1="W" u2="&#xe6;" k="20" />
    <hkern u1="W" u2="&#xe5;" k="20" />
    <hkern u1="W" u2="&#xe4;" k="20" />
    <hkern u1="W" u2="&#xe3;" k="20" />
    <hkern u1="W" u2="&#xe2;" k="20" />
    <hkern u1="W" u2="&#xe1;" k="20" />
    <hkern u1="W" u2="&#xe0;" k="20" />
    <hkern u1="W" u2="&#xd8;" k="5" />
    <hkern u1="W" u2="&#xd6;" k="5" />
    <hkern u1="W" u2="&#xd5;" k="5" />
    <hkern u1="W" u2="&#xd4;" k="5" />
    <hkern u1="W" u2="&#xd3;" k="5" />
    <hkern u1="W" u2="&#xd2;" k="5" />
    <hkern u1="W" u2="&#xc7;" k="5" />
    <hkern u1="W" u2="&#xc6;" k="73" />
    <hkern u1="W" u2="&#xc5;" k="73" />
    <hkern u1="W" u2="&#xc4;" k="73" />
    <hkern u1="W" u2="&#xc3;" k="73" />
    <hkern u1="W" u2="&#xc2;" k="73" />
    <hkern u1="W" u2="&#xc1;" k="73" />
    <hkern u1="W" u2="&#xc0;" k="73" />
    <hkern u1="W" u2="z" k="6" />
    <hkern u1="W" u2="x" k="15" />
    <hkern u1="W" u2="t" k="-10" />
    <hkern u1="W" u2="s" k="10" />
    <hkern u1="W" u2="q" k="10" />
    <hkern u1="W" u2="o" k="10" />
    <hkern u1="W" u2="g" k="10" />
    <hkern u1="W" u2="e" k="10" />
    <hkern u1="W" u2="d" k="10" />
    <hkern u1="W" u2="c" k="10" />
    <hkern u1="W" u2="a" k="20" />
    <hkern u1="W" u2="Z" k="5" />
    <hkern u1="W" u2="X" k="10" />
    <hkern u1="W" u2="T" k="-6" />
    <hkern u1="W" u2="Q" k="5" />
    <hkern u1="W" u2="O" k="5" />
    <hkern u1="W" u2="J" k="31" />
    <hkern u1="W" u2="G" k="5" />
    <hkern u1="W" u2="C" k="5" />
    <hkern u1="W" u2="A" k="73" />
    <hkern u1="X" g2="fl" k="10" />
    <hkern u1="X" g2="fi" k="10" />
    <hkern u1="X" u2="&#x178;" k="15" />
    <hkern u1="X" u2="&#x153;" k="26" />
    <hkern u1="X" u2="&#x152;" k="27" />
    <hkern u1="X" u2="&#xff;" k="5" />
    <hkern u1="X" u2="&#xfd;" k="5" />
    <hkern u1="X" u2="&#xfc;" k="5" />
    <hkern u1="X" u2="&#xfb;" k="5" />
    <hkern u1="X" u2="&#xfa;" k="5" />
    <hkern u1="X" u2="&#xf9;" k="5" />
    <hkern u1="X" u2="&#xf6;" k="26" />
    <hkern u1="X" u2="&#xf5;" k="26" />
    <hkern u1="X" u2="&#xf4;" k="26" />
    <hkern u1="X" u2="&#xf3;" k="26" />
    <hkern u1="X" u2="&#xf2;" k="26" />
    <hkern u1="X" u2="&#xf0;" k="20" />
    <hkern u1="X" u2="&#xeb;" k="20" />
    <hkern u1="X" u2="&#xea;" k="20" />
    <hkern u1="X" u2="&#xe9;" k="20" />
    <hkern u1="X" u2="&#xe8;" k="20" />
    <hkern u1="X" u2="&#xe7;" k="20" />
    <hkern u1="X" u2="&#xe6;" k="10" />
    <hkern u1="X" u2="&#xe5;" k="10" />
    <hkern u1="X" u2="&#xe4;" k="10" />
    <hkern u1="X" u2="&#xe3;" k="10" />
    <hkern u1="X" u2="&#xe2;" k="10" />
    <hkern u1="X" u2="&#xe1;" k="10" />
    <hkern u1="X" u2="&#xe0;" k="10" />
    <hkern u1="X" u2="&#xdd;" k="15" />
    <hkern u1="X" u2="&#xdc;" k="10" />
    <hkern u1="X" u2="&#xdb;" k="10" />
    <hkern u1="X" u2="&#xda;" k="10" />
    <hkern u1="X" u2="&#xd9;" k="10" />
    <hkern u1="X" u2="&#xd8;" k="27" />
    <hkern u1="X" u2="&#xd6;" k="27" />
    <hkern u1="X" u2="&#xd5;" k="27" />
    <hkern u1="X" u2="&#xd4;" k="27" />
    <hkern u1="X" u2="&#xd3;" k="27" />
    <hkern u1="X" u2="&#xd2;" k="27" />
    <hkern u1="X" u2="&#xc7;" k="20" />
    <hkern u1="X" u2="&#xc6;" k="20" />
    <hkern u1="X" u2="&#xc5;" k="20" />
    <hkern u1="X" u2="&#xc4;" k="20" />
    <hkern u1="X" u2="&#xc3;" k="20" />
    <hkern u1="X" u2="&#xc2;" k="20" />
    <hkern u1="X" u2="&#xc1;" k="20" />
    <hkern u1="X" u2="&#xc0;" k="20" />
    <hkern u1="X" u2="y" k="5" />
    <hkern u1="X" u2="x" k="15" />
    <hkern u1="X" u2="w" k="15" />
    <hkern u1="X" u2="v" k="31" />
    <hkern u1="X" u2="u" k="5" />
    <hkern u1="X" u2="t" k="10" />
    <hkern u1="X" u2="q" k="20" />
    <hkern u1="X" u2="o" k="26" />
    <hkern u1="X" u2="g" k="20" />
    <hkern u1="X" u2="f" k="10" />
    <hkern u1="X" u2="e" k="20" />
    <hkern u1="X" u2="d" k="20" />
    <hkern u1="X" u2="c" k="20" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="Y" k="15" />
    <hkern u1="X" u2="W" k="10" />
    <hkern u1="X" u2="V" k="15" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="Q" k="27" />
    <hkern u1="X" u2="O" k="27" />
    <hkern u1="X" u2="G" k="20" />
    <hkern u1="X" u2="C" k="20" />
    <hkern u1="X" u2="A" k="20" />
    <hkern u1="Y" g2="fl" k="10" />
    <hkern u1="Y" g2="fi" k="10" />
    <hkern u1="Y" u2="&#x17e;" k="20" />
    <hkern u1="Y" u2="&#x178;" k="-10" />
    <hkern u1="Y" u2="&#x161;" k="31" />
    <hkern u1="Y" u2="&#x160;" k="10" />
    <hkern u1="Y" u2="&#x153;" k="61" />
    <hkern u1="Y" u2="&#x152;" k="24" />
    <hkern u1="Y" u2="&#xff;" k="20" />
    <hkern u1="Y" u2="&#xfd;" k="20" />
    <hkern u1="Y" u2="&#xfc;" k="20" />
    <hkern u1="Y" u2="&#xfb;" k="20" />
    <hkern u1="Y" u2="&#xfa;" k="20" />
    <hkern u1="Y" u2="&#xf9;" k="20" />
    <hkern u1="Y" u2="&#xf6;" k="61" />
    <hkern u1="Y" u2="&#xf5;" k="61" />
    <hkern u1="Y" u2="&#xf4;" k="61" />
    <hkern u1="Y" u2="&#xf3;" k="61" />
    <hkern u1="Y" u2="&#xf2;" k="61" />
    <hkern u1="Y" u2="&#xf0;" k="61" />
    <hkern u1="Y" u2="&#xeb;" k="61" />
    <hkern u1="Y" u2="&#xea;" k="61" />
    <hkern u1="Y" u2="&#xe9;" k="61" />
    <hkern u1="Y" u2="&#xe8;" k="61" />
    <hkern u1="Y" u2="&#xe7;" k="61" />
    <hkern u1="Y" u2="&#xe6;" k="51" />
    <hkern u1="Y" u2="&#xe5;" k="51" />
    <hkern u1="Y" u2="&#xe4;" k="51" />
    <hkern u1="Y" u2="&#xe3;" k="51" />
    <hkern u1="Y" u2="&#xe2;" k="51" />
    <hkern u1="Y" u2="&#xe1;" k="51" />
    <hkern u1="Y" u2="&#xe0;" k="51" />
    <hkern u1="Y" u2="&#xdd;" k="-10" />
    <hkern u1="Y" u2="&#xd8;" k="24" />
    <hkern u1="Y" u2="&#xd6;" k="24" />
    <hkern u1="Y" u2="&#xd5;" k="24" />
    <hkern u1="Y" u2="&#xd4;" k="24" />
    <hkern u1="Y" u2="&#xd3;" k="24" />
    <hkern u1="Y" u2="&#xd2;" k="24" />
    <hkern u1="Y" u2="&#xc7;" k="14" />
    <hkern u1="Y" u2="&#xc6;" k="100" />
    <hkern u1="Y" u2="&#xc5;" k="100" />
    <hkern u1="Y" u2="&#xc4;" k="100" />
    <hkern u1="Y" u2="&#xc3;" k="100" />
    <hkern u1="Y" u2="&#xc2;" k="100" />
    <hkern u1="Y" u2="&#xc1;" k="100" />
    <hkern u1="Y" u2="&#xc0;" k="100" />
    <hkern u1="Y" u2="z" k="20" />
    <hkern u1="Y" u2="y" k="20" />
    <hkern u1="Y" u2="x" k="41" />
    <hkern u1="Y" u2="w" k="10" />
    <hkern u1="Y" u2="v" k="20" />
    <hkern u1="Y" u2="u" k="20" />
    <hkern u1="Y" u2="t" k="10" />
    <hkern u1="Y" u2="s" k="31" />
    <hkern u1="Y" u2="q" k="61" />
    <hkern u1="Y" u2="o" k="61" />
    <hkern u1="Y" u2="g" k="61" />
    <hkern u1="Y" u2="f" k="10" />
    <hkern u1="Y" u2="e" k="61" />
    <hkern u1="Y" u2="d" k="61" />
    <hkern u1="Y" u2="c" k="61" />
    <hkern u1="Y" u2="a" k="51" />
    <hkern u1="Y" u2="Y" k="-10" />
    <hkern u1="Y" u2="X" k="15" />
    <hkern u1="Y" u2="T" k="-10" />
    <hkern u1="Y" u2="S" k="10" />
    <hkern u1="Y" u2="Q" k="24" />
    <hkern u1="Y" u2="O" k="24" />
    <hkern u1="Y" u2="J" k="73" />
    <hkern u1="Y" u2="G" k="14" />
    <hkern u1="Y" u2="C" k="14" />
    <hkern u1="Y" u2="A" k="100" />
    <hkern u1="Z" u2="&#x153;" k="10" />
    <hkern u1="Z" u2="&#x152;" k="12" />
    <hkern u1="Z" u2="&#xff;" k="5" />
    <hkern u1="Z" u2="&#xfd;" k="5" />
    <hkern u1="Z" u2="&#xfc;" k="5" />
    <hkern u1="Z" u2="&#xfb;" k="5" />
    <hkern u1="Z" u2="&#xfa;" k="5" />
    <hkern u1="Z" u2="&#xf9;" k="5" />
    <hkern u1="Z" u2="&#xf6;" k="10" />
    <hkern u1="Z" u2="&#xf5;" k="10" />
    <hkern u1="Z" u2="&#xf4;" k="10" />
    <hkern u1="Z" u2="&#xf3;" k="10" />
    <hkern u1="Z" u2="&#xf2;" k="10" />
    <hkern u1="Z" u2="&#xf0;" k="10" />
    <hkern u1="Z" u2="&#xeb;" k="10" />
    <hkern u1="Z" u2="&#xea;" k="10" />
    <hkern u1="Z" u2="&#xe9;" k="10" />
    <hkern u1="Z" u2="&#xe8;" k="10" />
    <hkern u1="Z" u2="&#xe7;" k="10" />
    <hkern u1="Z" u2="&#xe6;" k="6" />
    <hkern u1="Z" u2="&#xe5;" k="6" />
    <hkern u1="Z" u2="&#xe4;" k="6" />
    <hkern u1="Z" u2="&#xe3;" k="6" />
    <hkern u1="Z" u2="&#xe2;" k="6" />
    <hkern u1="Z" u2="&#xe1;" k="6" />
    <hkern u1="Z" u2="&#xe0;" k="6" />
    <hkern u1="Z" u2="&#xd8;" k="12" />
    <hkern u1="Z" u2="&#xd6;" k="12" />
    <hkern u1="Z" u2="&#xd5;" k="12" />
    <hkern u1="Z" u2="&#xd4;" k="12" />
    <hkern u1="Z" u2="&#xd3;" k="12" />
    <hkern u1="Z" u2="&#xd2;" k="12" />
    <hkern u1="Z" u2="&#xc7;" k="10" />
    <hkern u1="Z" u2="y" k="5" />
    <hkern u1="Z" u2="w" k="10" />
    <hkern u1="Z" u2="u" k="5" />
    <hkern u1="Z" u2="q" k="10" />
    <hkern u1="Z" u2="o" k="10" />
    <hkern u1="Z" u2="g" k="10" />
    <hkern u1="Z" u2="e" k="10" />
    <hkern u1="Z" u2="d" k="10" />
    <hkern u1="Z" u2="c" k="10" />
    <hkern u1="Z" u2="a" k="6" />
    <hkern u1="Z" u2="X" k="12" />
    <hkern u1="Z" u2="W" k="5" />
    <hkern u1="Z" u2="T" k="10" />
    <hkern u1="Z" u2="Q" k="12" />
    <hkern u1="Z" u2="O" k="12" />
    <hkern u1="Z" u2="J" k="-10" />
    <hkern u1="Z" u2="C" k="10" />
    <hkern u1="a" u2="&#x201d;" k="27" />
    <hkern u1="a" u2="&#x2019;" k="27" />
    <hkern u1="a" u2="&#x178;" k="31" />
    <hkern u1="a" u2="&#x160;" k="6" />
    <hkern u1="a" u2="&#xdd;" k="31" />
    <hkern u1="a" u2="x" k="5" />
    <hkern u1="a" u2="v" k="5" />
    <hkern u1="a" u2="Y" k="31" />
    <hkern u1="a" u2="W" k="20" />
    <hkern u1="a" u2="V" k="20" />
    <hkern u1="a" u2="T" k="20" />
    <hkern u1="a" u2="S" k="6" />
    <hkern u1="a" u2="&#x27;" k="20" />
    <hkern u1="a" u2="&#x22;" k="20" />
    <hkern u1="b" u2="&#x201d;" k="31" />
    <hkern u1="b" u2="&#x2019;" k="31" />
    <hkern u1="b" u2="&#x17e;" k="10" />
    <hkern u1="b" u2="&#x178;" k="61" />
    <hkern u1="b" u2="&#x160;" k="10" />
    <hkern u1="b" u2="&#xe6;" k="6" />
    <hkern u1="b" u2="&#xe5;" k="6" />
    <hkern u1="b" u2="&#xe4;" k="6" />
    <hkern u1="b" u2="&#xe3;" k="6" />
    <hkern u1="b" u2="&#xe2;" k="6" />
    <hkern u1="b" u2="&#xe1;" k="6" />
    <hkern u1="b" u2="&#xe0;" k="6" />
    <hkern u1="b" u2="&#xdd;" k="61" />
    <hkern u1="b" u2="&#xdc;" k="6" />
    <hkern u1="b" u2="&#xdb;" k="6" />
    <hkern u1="b" u2="&#xda;" k="6" />
    <hkern u1="b" u2="&#xd9;" k="6" />
    <hkern u1="b" u2="&#xc6;" k="10" />
    <hkern u1="b" u2="&#xc5;" k="10" />
    <hkern u1="b" u2="&#xc4;" k="10" />
    <hkern u1="b" u2="&#xc3;" k="10" />
    <hkern u1="b" u2="&#xc2;" k="10" />
    <hkern u1="b" u2="&#xc1;" k="10" />
    <hkern u1="b" u2="&#xc0;" k="10" />
    <hkern u1="b" u2="z" k="10" />
    <hkern u1="b" u2="x" k="16" />
    <hkern u1="b" u2="v" k="10" />
    <hkern u1="b" u2="a" k="6" />
    <hkern u1="b" u2="Y" k="61" />
    <hkern u1="b" u2="X" k="20" />
    <hkern u1="b" u2="W" k="10" />
    <hkern u1="b" u2="V" k="20" />
    <hkern u1="b" u2="U" k="6" />
    <hkern u1="b" u2="T" k="26" />
    <hkern u1="b" u2="S" k="10" />
    <hkern u1="b" u2="J" k="10" />
    <hkern u1="b" u2="A" k="10" />
    <hkern u1="b" u2="&#x27;" k="20" />
    <hkern u1="b" u2="&#x22;" k="20" />
    <hkern u1="c" u2="&#x17e;" k="10" />
    <hkern u1="c" u2="&#x178;" k="41" />
    <hkern u1="c" u2="&#x153;" k="27" />
    <hkern u1="c" u2="&#xf6;" k="27" />
    <hkern u1="c" u2="&#xf5;" k="27" />
    <hkern u1="c" u2="&#xf4;" k="27" />
    <hkern u1="c" u2="&#xf3;" k="27" />
    <hkern u1="c" u2="&#xf2;" k="27" />
    <hkern u1="c" u2="&#xf0;" k="24" />
    <hkern u1="c" u2="&#xe7;" k="20" />
    <hkern u1="c" u2="&#xe6;" k="6" />
    <hkern u1="c" u2="&#xe5;" k="6" />
    <hkern u1="c" u2="&#xe4;" k="6" />
    <hkern u1="c" u2="&#xe3;" k="6" />
    <hkern u1="c" u2="&#xe2;" k="6" />
    <hkern u1="c" u2="&#xe1;" k="6" />
    <hkern u1="c" u2="&#xe0;" k="6" />
    <hkern u1="c" u2="&#xdd;" k="41" />
    <hkern u1="c" u2="&#xdc;" k="14" />
    <hkern u1="c" u2="&#xdb;" k="14" />
    <hkern u1="c" u2="&#xda;" k="14" />
    <hkern u1="c" u2="&#xd9;" k="14" />
    <hkern u1="c" u2="&#xc7;" k="10" />
    <hkern u1="c" u2="z" k="10" />
    <hkern u1="c" u2="v" k="5" />
    <hkern u1="c" u2="q" k="24" />
    <hkern u1="c" u2="o" k="27" />
    <hkern u1="c" u2="g" k="24" />
    <hkern u1="c" u2="d" k="24" />
    <hkern u1="c" u2="c" k="20" />
    <hkern u1="c" u2="a" k="6" />
    <hkern u1="c" u2="Y" k="41" />
    <hkern u1="c" u2="X" k="20" />
    <hkern u1="c" u2="W" k="10" />
    <hkern u1="c" u2="V" k="10" />
    <hkern u1="c" u2="U" k="14" />
    <hkern u1="c" u2="T" k="20" />
    <hkern u1="c" u2="C" k="10" />
    <hkern u1="e" u2="&#x201d;" k="27" />
    <hkern u1="e" u2="&#x2019;" k="27" />
    <hkern u1="e" u2="&#x17e;" k="10" />
    <hkern u1="e" u2="&#x17d;" k="10" />
    <hkern u1="e" u2="&#x178;" k="61" />
    <hkern u1="e" u2="&#x153;" k="10" />
    <hkern u1="e" u2="&#xf6;" k="10" />
    <hkern u1="e" u2="&#xf5;" k="10" />
    <hkern u1="e" u2="&#xf4;" k="10" />
    <hkern u1="e" u2="&#xf3;" k="10" />
    <hkern u1="e" u2="&#xf2;" k="10" />
    <hkern u1="e" u2="&#xe6;" k="6" />
    <hkern u1="e" u2="&#xe5;" k="6" />
    <hkern u1="e" u2="&#xe4;" k="6" />
    <hkern u1="e" u2="&#xe3;" k="6" />
    <hkern u1="e" u2="&#xe2;" k="6" />
    <hkern u1="e" u2="&#xe1;" k="6" />
    <hkern u1="e" u2="&#xe0;" k="6" />
    <hkern u1="e" u2="&#xdd;" k="61" />
    <hkern u1="e" u2="&#xdc;" k="6" />
    <hkern u1="e" u2="&#xdb;" k="6" />
    <hkern u1="e" u2="&#xda;" k="6" />
    <hkern u1="e" u2="&#xd9;" k="6" />
    <hkern u1="e" u2="z" k="10" />
    <hkern u1="e" u2="x" k="12" />
    <hkern u1="e" u2="v" k="10" />
    <hkern u1="e" u2="o" k="10" />
    <hkern u1="e" u2="a" k="6" />
    <hkern u1="e" u2="Z" k="10" />
    <hkern u1="e" u2="Y" k="61" />
    <hkern u1="e" u2="X" k="20" />
    <hkern u1="e" u2="W" k="10" />
    <hkern u1="e" u2="V" k="20" />
    <hkern u1="e" u2="U" k="6" />
    <hkern u1="e" u2="T" k="20" />
    <hkern u1="e" u2="&#x27;" k="20" />
    <hkern u1="e" u2="&#x22;" k="20" />
    <hkern u1="f" g2="fl" k="-10" />
    <hkern u1="f" g2="fi" k="-10" />
    <hkern u1="f" u2="&#x201d;" k="-37" />
    <hkern u1="f" u2="&#x2019;" k="-37" />
    <hkern u1="f" u2="&#x17d;" k="-10" />
    <hkern u1="f" u2="&#x178;" k="-20" />
    <hkern u1="f" u2="&#x160;" k="-20" />
    <hkern u1="f" u2="&#x153;" k="16" />
    <hkern u1="f" u2="&#x152;" k="-10" />
    <hkern u1="f" u2="&#xf6;" k="16" />
    <hkern u1="f" u2="&#xf5;" k="16" />
    <hkern u1="f" u2="&#xf4;" k="16" />
    <hkern u1="f" u2="&#xf3;" k="16" />
    <hkern u1="f" u2="&#xf2;" k="16" />
    <hkern u1="f" u2="&#xf0;" k="10" />
    <hkern u1="f" u2="&#xeb;" k="10" />
    <hkern u1="f" u2="&#xea;" k="10" />
    <hkern u1="f" u2="&#xe9;" k="10" />
    <hkern u1="f" u2="&#xe8;" k="10" />
    <hkern u1="f" u2="&#xe7;" k="10" />
    <hkern u1="f" u2="&#xe6;" k="10" />
    <hkern u1="f" u2="&#xe5;" k="10" />
    <hkern u1="f" u2="&#xe4;" k="10" />
    <hkern u1="f" u2="&#xe3;" k="10" />
    <hkern u1="f" u2="&#xe2;" k="10" />
    <hkern u1="f" u2="&#xe1;" k="10" />
    <hkern u1="f" u2="&#xe0;" k="10" />
    <hkern u1="f" u2="&#xdd;" k="-20" />
    <hkern u1="f" u2="&#xd8;" k="-10" />
    <hkern u1="f" u2="&#xd6;" k="-10" />
    <hkern u1="f" u2="&#xd5;" k="-10" />
    <hkern u1="f" u2="&#xd4;" k="-10" />
    <hkern u1="f" u2="&#xd3;" k="-10" />
    <hkern u1="f" u2="&#xd2;" k="-10" />
    <hkern u1="f" u2="&#xc6;" k="45" />
    <hkern u1="f" u2="&#xc5;" k="45" />
    <hkern u1="f" u2="&#xc4;" k="45" />
    <hkern u1="f" u2="&#xc3;" k="45" />
    <hkern u1="f" u2="&#xc2;" k="45" />
    <hkern u1="f" u2="&#xc1;" k="45" />
    <hkern u1="f" u2="&#xc0;" k="45" />
    <hkern u1="f" u2="x" k="10" />
    <hkern u1="f" u2="t" k="-10" />
    <hkern u1="f" u2="q" k="10" />
    <hkern u1="f" u2="o" k="16" />
    <hkern u1="f" u2="g" k="10" />
    <hkern u1="f" u2="f" k="-10" />
    <hkern u1="f" u2="e" k="10" />
    <hkern u1="f" u2="d" k="10" />
    <hkern u1="f" u2="c" k="10" />
    <hkern u1="f" u2="a" k="10" />
    <hkern u1="f" u2="Z" k="-10" />
    <hkern u1="f" u2="Y" k="-20" />
    <hkern u1="f" u2="X" k="-10" />
    <hkern u1="f" u2="W" k="-20" />
    <hkern u1="f" u2="V" k="-20" />
    <hkern u1="f" u2="T" k="-41" />
    <hkern u1="f" u2="S" k="-20" />
    <hkern u1="f" u2="Q" k="-10" />
    <hkern u1="f" u2="O" k="-10" />
    <hkern u1="f" u2="J" k="61" />
    <hkern u1="f" u2="A" k="45" />
    <hkern u1="f" u2="&#x27;" k="-39" />
    <hkern u1="f" u2="&#x22;" k="-39" />
    <hkern u1="h" u2="&#x201d;" k="27" />
    <hkern u1="h" u2="&#x2019;" k="27" />
    <hkern u1="h" u2="&#x178;" k="31" />
    <hkern u1="h" u2="&#x160;" k="6" />
    <hkern u1="h" u2="&#xdd;" k="31" />
    <hkern u1="h" u2="x" k="5" />
    <hkern u1="h" u2="v" k="5" />
    <hkern u1="h" u2="Y" k="31" />
    <hkern u1="h" u2="W" k="20" />
    <hkern u1="h" u2="V" k="20" />
    <hkern u1="h" u2="T" k="20" />
    <hkern u1="h" u2="S" k="6" />
    <hkern u1="h" u2="&#x27;" k="20" />
    <hkern u1="h" u2="&#x22;" k="20" />
    <hkern u1="j" u2="j" k="-10" />
    <hkern u1="k" u2="&#x201d;" k="10" />
    <hkern u1="k" u2="&#x2019;" k="10" />
    <hkern u1="k" u2="&#x178;" k="31" />
    <hkern u1="k" u2="&#x161;" k="10" />
    <hkern u1="k" u2="&#x153;" k="20" />
    <hkern u1="k" u2="&#xff;" k="27" />
    <hkern u1="k" u2="&#xfd;" k="27" />
    <hkern u1="k" u2="&#xfc;" k="27" />
    <hkern u1="k" u2="&#xfb;" k="27" />
    <hkern u1="k" u2="&#xfa;" k="27" />
    <hkern u1="k" u2="&#xf9;" k="27" />
    <hkern u1="k" u2="&#xf6;" k="20" />
    <hkern u1="k" u2="&#xf5;" k="20" />
    <hkern u1="k" u2="&#xf4;" k="20" />
    <hkern u1="k" u2="&#xf3;" k="20" />
    <hkern u1="k" u2="&#xf2;" k="20" />
    <hkern u1="k" u2="&#xf0;" k="18" />
    <hkern u1="k" u2="&#xeb;" k="20" />
    <hkern u1="k" u2="&#xea;" k="20" />
    <hkern u1="k" u2="&#xe9;" k="20" />
    <hkern u1="k" u2="&#xe8;" k="20" />
    <hkern u1="k" u2="&#xe7;" k="18" />
    <hkern u1="k" u2="&#xe6;" k="20" />
    <hkern u1="k" u2="&#xe5;" k="20" />
    <hkern u1="k" u2="&#xe4;" k="20" />
    <hkern u1="k" u2="&#xe3;" k="20" />
    <hkern u1="k" u2="&#xe2;" k="20" />
    <hkern u1="k" u2="&#xe1;" k="20" />
    <hkern u1="k" u2="&#xe0;" k="20" />
    <hkern u1="k" u2="&#xdd;" k="31" />
    <hkern u1="k" u2="&#xdc;" k="20" />
    <hkern u1="k" u2="&#xdb;" k="20" />
    <hkern u1="k" u2="&#xda;" k="20" />
    <hkern u1="k" u2="&#xd9;" k="20" />
    <hkern u1="k" u2="y" k="27" />
    <hkern u1="k" u2="x" k="10" />
    <hkern u1="k" u2="w" k="16" />
    <hkern u1="k" u2="u" k="27" />
    <hkern u1="k" u2="t" k="16" />
    <hkern u1="k" u2="s" k="10" />
    <hkern u1="k" u2="q" k="18" />
    <hkern u1="k" u2="o" k="20" />
    <hkern u1="k" u2="g" k="18" />
    <hkern u1="k" u2="e" k="20" />
    <hkern u1="k" u2="d" k="18" />
    <hkern u1="k" u2="c" k="18" />
    <hkern u1="k" u2="a" k="20" />
    <hkern u1="k" u2="Y" k="31" />
    <hkern u1="k" u2="W" k="5" />
    <hkern u1="k" u2="V" k="31" />
    <hkern u1="k" u2="U" k="20" />
    <hkern u1="k" u2="T" k="20" />
    <hkern u1="k" u2="&#x27;" k="20" />
    <hkern u1="k" u2="&#x22;" k="20" />
    <hkern u1="m" u2="&#x201d;" k="27" />
    <hkern u1="m" u2="&#x2019;" k="27" />
    <hkern u1="m" u2="&#x178;" k="31" />
    <hkern u1="m" u2="&#x160;" k="6" />
    <hkern u1="m" u2="&#xdd;" k="31" />
    <hkern u1="m" u2="x" k="5" />
    <hkern u1="m" u2="v" k="5" />
    <hkern u1="m" u2="Y" k="31" />
    <hkern u1="m" u2="W" k="20" />
    <hkern u1="m" u2="V" k="20" />
    <hkern u1="m" u2="T" k="20" />
    <hkern u1="m" u2="S" k="6" />
    <hkern u1="m" u2="&#x27;" k="20" />
    <hkern u1="m" u2="&#x22;" k="20" />
    <hkern u1="n" u2="&#x201d;" k="27" />
    <hkern u1="n" u2="&#x2019;" k="27" />
    <hkern u1="n" u2="&#x178;" k="31" />
    <hkern u1="n" u2="&#x160;" k="6" />
    <hkern u1="n" u2="&#xdd;" k="31" />
    <hkern u1="n" u2="x" k="5" />
    <hkern u1="n" u2="v" k="5" />
    <hkern u1="n" u2="Y" k="31" />
    <hkern u1="n" u2="W" k="20" />
    <hkern u1="n" u2="V" k="20" />
    <hkern u1="n" u2="T" k="20" />
    <hkern u1="n" u2="S" k="6" />
    <hkern u1="n" u2="&#x27;" k="20" />
    <hkern u1="n" u2="&#x22;" k="20" />
    <hkern u1="o" u2="&#x201d;" k="31" />
    <hkern u1="o" u2="&#x2019;" k="31" />
    <hkern u1="o" u2="&#x17e;" k="10" />
    <hkern u1="o" u2="&#x17d;" k="10" />
    <hkern u1="o" u2="&#x178;" k="61" />
    <hkern u1="o" u2="&#x160;" k="20" />
    <hkern u1="o" u2="&#xe6;" k="6" />
    <hkern u1="o" u2="&#xe5;" k="6" />
    <hkern u1="o" u2="&#xe4;" k="6" />
    <hkern u1="o" u2="&#xe3;" k="6" />
    <hkern u1="o" u2="&#xe2;" k="6" />
    <hkern u1="o" u2="&#xe1;" k="6" />
    <hkern u1="o" u2="&#xe0;" k="6" />
    <hkern u1="o" u2="&#xdd;" k="61" />
    <hkern u1="o" u2="&#xc6;" k="10" />
    <hkern u1="o" u2="&#xc5;" k="10" />
    <hkern u1="o" u2="&#xc4;" k="10" />
    <hkern u1="o" u2="&#xc3;" k="10" />
    <hkern u1="o" u2="&#xc2;" k="10" />
    <hkern u1="o" u2="&#xc1;" k="10" />
    <hkern u1="o" u2="&#xc0;" k="10" />
    <hkern u1="o" u2="z" k="10" />
    <hkern u1="o" u2="x" k="18" />
    <hkern u1="o" u2="v" k="10" />
    <hkern u1="o" u2="a" k="6" />
    <hkern u1="o" u2="Z" k="10" />
    <hkern u1="o" u2="Y" k="61" />
    <hkern u1="o" u2="X" k="26" />
    <hkern u1="o" u2="W" k="10" />
    <hkern u1="o" u2="V" k="31" />
    <hkern u1="o" u2="T" k="26" />
    <hkern u1="o" u2="S" k="20" />
    <hkern u1="o" u2="J" k="16" />
    <hkern u1="o" u2="A" k="10" />
    <hkern u1="o" u2="&#x27;" k="20" />
    <hkern u1="o" u2="&#x22;" k="20" />
    <hkern u1="p" u2="&#x201d;" k="31" />
    <hkern u1="p" u2="&#x2019;" k="31" />
    <hkern u1="p" u2="&#x17e;" k="10" />
    <hkern u1="p" u2="&#x178;" k="61" />
    <hkern u1="p" u2="&#x160;" k="10" />
    <hkern u1="p" u2="&#xe6;" k="6" />
    <hkern u1="p" u2="&#xe5;" k="6" />
    <hkern u1="p" u2="&#xe4;" k="6" />
    <hkern u1="p" u2="&#xe3;" k="6" />
    <hkern u1="p" u2="&#xe2;" k="6" />
    <hkern u1="p" u2="&#xe1;" k="6" />
    <hkern u1="p" u2="&#xe0;" k="6" />
    <hkern u1="p" u2="&#xdd;" k="61" />
    <hkern u1="p" u2="&#xdc;" k="6" />
    <hkern u1="p" u2="&#xdb;" k="6" />
    <hkern u1="p" u2="&#xda;" k="6" />
    <hkern u1="p" u2="&#xd9;" k="6" />
    <hkern u1="p" u2="&#xc6;" k="10" />
    <hkern u1="p" u2="&#xc5;" k="10" />
    <hkern u1="p" u2="&#xc4;" k="10" />
    <hkern u1="p" u2="&#xc3;" k="10" />
    <hkern u1="p" u2="&#xc2;" k="10" />
    <hkern u1="p" u2="&#xc1;" k="10" />
    <hkern u1="p" u2="&#xc0;" k="10" />
    <hkern u1="p" u2="z" k="10" />
    <hkern u1="p" u2="x" k="16" />
    <hkern u1="p" u2="v" k="10" />
    <hkern u1="p" u2="a" k="6" />
    <hkern u1="p" u2="Y" k="61" />
    <hkern u1="p" u2="X" k="20" />
    <hkern u1="p" u2="W" k="10" />
    <hkern u1="p" u2="V" k="20" />
    <hkern u1="p" u2="U" k="6" />
    <hkern u1="p" u2="T" k="26" />
    <hkern u1="p" u2="S" k="10" />
    <hkern u1="p" u2="J" k="10" />
    <hkern u1="p" u2="A" k="10" />
    <hkern u1="p" u2="&#x27;" k="20" />
    <hkern u1="p" u2="&#x22;" k="20" />
    <hkern u1="r" g2="fl" k="-20" />
    <hkern u1="r" g2="fi" k="-20" />
    <hkern u1="r" u2="&#x178;" k="10" />
    <hkern u1="r" u2="&#x153;" k="10" />
    <hkern u1="r" u2="&#xf6;" k="10" />
    <hkern u1="r" u2="&#xf5;" k="10" />
    <hkern u1="r" u2="&#xf4;" k="10" />
    <hkern u1="r" u2="&#xf3;" k="10" />
    <hkern u1="r" u2="&#xf2;" k="10" />
    <hkern u1="r" u2="&#xf0;" k="10" />
    <hkern u1="r" u2="&#xeb;" k="10" />
    <hkern u1="r" u2="&#xea;" k="10" />
    <hkern u1="r" u2="&#xe9;" k="10" />
    <hkern u1="r" u2="&#xe8;" k="10" />
    <hkern u1="r" u2="&#xe7;" k="10" />
    <hkern u1="r" u2="&#xe6;" k="20" />
    <hkern u1="r" u2="&#xe5;" k="20" />
    <hkern u1="r" u2="&#xe4;" k="20" />
    <hkern u1="r" u2="&#xe3;" k="20" />
    <hkern u1="r" u2="&#xe2;" k="20" />
    <hkern u1="r" u2="&#xe1;" k="20" />
    <hkern u1="r" u2="&#xe0;" k="20" />
    <hkern u1="r" u2="&#xdd;" k="10" />
    <hkern u1="r" u2="&#xc6;" k="51" />
    <hkern u1="r" u2="&#xc5;" k="51" />
    <hkern u1="r" u2="&#xc4;" k="51" />
    <hkern u1="r" u2="&#xc3;" k="51" />
    <hkern u1="r" u2="&#xc2;" k="51" />
    <hkern u1="r" u2="&#xc1;" k="51" />
    <hkern u1="r" u2="&#xc0;" k="51" />
    <hkern u1="r" u2="x" k="20" />
    <hkern u1="r" u2="w" k="-10" />
    <hkern u1="r" u2="v" k="-5" />
    <hkern u1="r" u2="t" k="-10" />
    <hkern u1="r" u2="q" k="10" />
    <hkern u1="r" u2="o" k="10" />
    <hkern u1="r" u2="g" k="10" />
    <hkern u1="r" u2="f" k="-20" />
    <hkern u1="r" u2="e" k="10" />
    <hkern u1="r" u2="d" k="10" />
    <hkern u1="r" u2="c" k="10" />
    <hkern u1="r" u2="a" k="20" />
    <hkern u1="r" u2="Y" k="10" />
    <hkern u1="r" u2="X" k="20" />
    <hkern u1="r" u2="W" k="10" />
    <hkern u1="r" u2="V" k="20" />
    <hkern u1="r" u2="T" k="20" />
    <hkern u1="r" u2="J" k="51" />
    <hkern u1="r" u2="A" k="51" />
    <hkern u1="s" u2="&#x201d;" k="10" />
    <hkern u1="s" u2="&#x2019;" k="10" />
    <hkern u1="s" u2="&#x178;" k="31" />
    <hkern u1="s" u2="&#xe6;" k="4" />
    <hkern u1="s" u2="&#xe5;" k="4" />
    <hkern u1="s" u2="&#xe4;" k="4" />
    <hkern u1="s" u2="&#xe3;" k="4" />
    <hkern u1="s" u2="&#xe2;" k="4" />
    <hkern u1="s" u2="&#xe1;" k="4" />
    <hkern u1="s" u2="&#xe0;" k="4" />
    <hkern u1="s" u2="&#xdd;" k="31" />
    <hkern u1="s" u2="&#xdc;" k="14" />
    <hkern u1="s" u2="&#xdb;" k="14" />
    <hkern u1="s" u2="&#xda;" k="14" />
    <hkern u1="s" u2="&#xd9;" k="14" />
    <hkern u1="s" u2="&#xc6;" k="12" />
    <hkern u1="s" u2="&#xc5;" k="12" />
    <hkern u1="s" u2="&#xc4;" k="12" />
    <hkern u1="s" u2="&#xc3;" k="12" />
    <hkern u1="s" u2="&#xc2;" k="12" />
    <hkern u1="s" u2="&#xc1;" k="12" />
    <hkern u1="s" u2="&#xc0;" k="12" />
    <hkern u1="s" u2="x" k="10" />
    <hkern u1="s" u2="w" k="5" />
    <hkern u1="s" u2="a" k="4" />
    <hkern u1="s" u2="Y" k="31" />
    <hkern u1="s" u2="X" k="6" />
    <hkern u1="s" u2="W" k="10" />
    <hkern u1="s" u2="V" k="10" />
    <hkern u1="s" u2="U" k="14" />
    <hkern u1="s" u2="T" k="20" />
    <hkern u1="s" u2="A" k="12" />
    <hkern u1="s" u2="&#x27;" k="10" />
    <hkern u1="s" u2="&#x22;" k="10" />
    <hkern u1="t" u2="&#x17e;" k="-6" />
    <hkern u1="t" u2="&#x17d;" k="-10" />
    <hkern u1="t" u2="&#x178;" k="20" />
    <hkern u1="t" u2="&#x153;" k="10" />
    <hkern u1="t" u2="&#xff;" k="10" />
    <hkern u1="t" u2="&#xfd;" k="10" />
    <hkern u1="t" u2="&#xfc;" k="10" />
    <hkern u1="t" u2="&#xfb;" k="10" />
    <hkern u1="t" u2="&#xfa;" k="10" />
    <hkern u1="t" u2="&#xf9;" k="10" />
    <hkern u1="t" u2="&#xf6;" k="10" />
    <hkern u1="t" u2="&#xf5;" k="10" />
    <hkern u1="t" u2="&#xf4;" k="10" />
    <hkern u1="t" u2="&#xf3;" k="10" />
    <hkern u1="t" u2="&#xf2;" k="10" />
    <hkern u1="t" u2="&#xdd;" k="20" />
    <hkern u1="t" u2="&#xdc;" k="10" />
    <hkern u1="t" u2="&#xdb;" k="10" />
    <hkern u1="t" u2="&#xda;" k="10" />
    <hkern u1="t" u2="&#xd9;" k="10" />
    <hkern u1="t" u2="&#xc6;" k="-10" />
    <hkern u1="t" u2="&#xc5;" k="-10" />
    <hkern u1="t" u2="&#xc4;" k="-10" />
    <hkern u1="t" u2="&#xc3;" k="-10" />
    <hkern u1="t" u2="&#xc2;" k="-10" />
    <hkern u1="t" u2="&#xc1;" k="-10" />
    <hkern u1="t" u2="&#xc0;" k="-10" />
    <hkern u1="t" u2="z" k="-6" />
    <hkern u1="t" u2="y" k="10" />
    <hkern u1="t" u2="w" k="-4" />
    <hkern u1="t" u2="v" k="-6" />
    <hkern u1="t" u2="u" k="10" />
    <hkern u1="t" u2="t" k="-10" />
    <hkern u1="t" u2="o" k="10" />
    <hkern u1="t" u2="Z" k="-10" />
    <hkern u1="t" u2="Y" k="20" />
    <hkern u1="t" u2="X" k="-10" />
    <hkern u1="t" u2="V" k="20" />
    <hkern u1="t" u2="U" k="10" />
    <hkern u1="t" u2="J" k="-20" />
    <hkern u1="t" u2="A" k="-10" />
    <hkern u1="t" u2="&#x27;" k="-10" />
    <hkern u1="t" u2="&#x22;" k="-10" />
    <hkern u1="v" u2="&#x178;" k="20" />
    <hkern u1="v" u2="&#x153;" k="10" />
    <hkern u1="v" u2="&#xf6;" k="10" />
    <hkern u1="v" u2="&#xf5;" k="10" />
    <hkern u1="v" u2="&#xf4;" k="10" />
    <hkern u1="v" u2="&#xf3;" k="10" />
    <hkern u1="v" u2="&#xf2;" k="10" />
    <hkern u1="v" u2="&#xf0;" k="10" />
    <hkern u1="v" u2="&#xeb;" k="5" />
    <hkern u1="v" u2="&#xea;" k="5" />
    <hkern u1="v" u2="&#xe9;" k="5" />
    <hkern u1="v" u2="&#xe8;" k="5" />
    <hkern u1="v" u2="&#xe7;" k="10" />
    <hkern u1="v" u2="&#xe6;" k="10" />
    <hkern u1="v" u2="&#xe5;" k="10" />
    <hkern u1="v" u2="&#xe4;" k="10" />
    <hkern u1="v" u2="&#xe3;" k="10" />
    <hkern u1="v" u2="&#xe2;" k="10" />
    <hkern u1="v" u2="&#xe1;" k="10" />
    <hkern u1="v" u2="&#xe0;" k="10" />
    <hkern u1="v" u2="&#xdd;" k="20" />
    <hkern u1="v" u2="&#xc6;" k="51" />
    <hkern u1="v" u2="&#xc5;" k="51" />
    <hkern u1="v" u2="&#xc4;" k="51" />
    <hkern u1="v" u2="&#xc3;" k="51" />
    <hkern u1="v" u2="&#xc2;" k="51" />
    <hkern u1="v" u2="&#xc1;" k="51" />
    <hkern u1="v" u2="&#xc0;" k="51" />
    <hkern u1="v" u2="x" k="10" />
    <hkern u1="v" u2="v" k="-4" />
    <hkern u1="v" u2="t" k="-12" />
    <hkern u1="v" u2="q" k="10" />
    <hkern u1="v" u2="o" k="10" />
    <hkern u1="v" u2="g" k="10" />
    <hkern u1="v" u2="e" k="5" />
    <hkern u1="v" u2="d" k="10" />
    <hkern u1="v" u2="c" k="10" />
    <hkern u1="v" u2="a" k="10" />
    <hkern u1="v" u2="Y" k="20" />
    <hkern u1="v" u2="X" k="31" />
    <hkern u1="v" u2="V" k="10" />
    <hkern u1="v" u2="T" k="15" />
    <hkern u1="v" u2="J" k="41" />
    <hkern u1="v" u2="A" k="51" />
    <hkern u1="w" u2="&#x17e;" k="10" />
    <hkern u1="w" u2="&#x17d;" k="16" />
    <hkern u1="w" u2="&#x178;" k="10" />
    <hkern u1="w" u2="&#x161;" k="5" />
    <hkern u1="w" u2="&#xdd;" k="10" />
    <hkern u1="w" u2="&#xc6;" k="29" />
    <hkern u1="w" u2="&#xc5;" k="29" />
    <hkern u1="w" u2="&#xc4;" k="29" />
    <hkern u1="w" u2="&#xc3;" k="29" />
    <hkern u1="w" u2="&#xc2;" k="29" />
    <hkern u1="w" u2="&#xc1;" k="29" />
    <hkern u1="w" u2="&#xc0;" k="29" />
    <hkern u1="w" u2="z" k="10" />
    <hkern u1="w" u2="x" k="5" />
    <hkern u1="w" u2="s" k="5" />
    <hkern u1="w" u2="Z" k="16" />
    <hkern u1="w" u2="Y" k="10" />
    <hkern u1="w" u2="X" k="15" />
    <hkern u1="w" u2="V" k="5" />
    <hkern u1="w" u2="T" k="10" />
    <hkern u1="w" u2="J" k="16" />
    <hkern u1="w" u2="A" k="29" />
    <hkern u1="x" u2="&#x178;" k="41" />
    <hkern u1="x" u2="&#x161;" k="5" />
    <hkern u1="x" u2="&#x153;" k="18" />
    <hkern u1="x" u2="&#xff;" k="5" />
    <hkern u1="x" u2="&#xfd;" k="5" />
    <hkern u1="x" u2="&#xfc;" k="5" />
    <hkern u1="x" u2="&#xfb;" k="5" />
    <hkern u1="x" u2="&#xfa;" k="5" />
    <hkern u1="x" u2="&#xf9;" k="5" />
    <hkern u1="x" u2="&#xf6;" k="18" />
    <hkern u1="x" u2="&#xf5;" k="18" />
    <hkern u1="x" u2="&#xf4;" k="18" />
    <hkern u1="x" u2="&#xf3;" k="18" />
    <hkern u1="x" u2="&#xf2;" k="18" />
    <hkern u1="x" u2="&#xf0;" k="16" />
    <hkern u1="x" u2="&#xeb;" k="16" />
    <hkern u1="x" u2="&#xea;" k="16" />
    <hkern u1="x" u2="&#xe9;" k="16" />
    <hkern u1="x" u2="&#xe8;" k="16" />
    <hkern u1="x" u2="&#xe7;" k="16" />
    <hkern u1="x" u2="&#xe6;" k="10" />
    <hkern u1="x" u2="&#xe5;" k="10" />
    <hkern u1="x" u2="&#xe4;" k="10" />
    <hkern u1="x" u2="&#xe3;" k="10" />
    <hkern u1="x" u2="&#xe2;" k="10" />
    <hkern u1="x" u2="&#xe1;" k="10" />
    <hkern u1="x" u2="&#xe0;" k="10" />
    <hkern u1="x" u2="&#xdd;" k="41" />
    <hkern u1="x" u2="&#xdc;" k="14" />
    <hkern u1="x" u2="&#xdb;" k="14" />
    <hkern u1="x" u2="&#xda;" k="14" />
    <hkern u1="x" u2="&#xd9;" k="14" />
    <hkern u1="x" u2="&#xc6;" k="10" />
    <hkern u1="x" u2="&#xc5;" k="10" />
    <hkern u1="x" u2="&#xc4;" k="10" />
    <hkern u1="x" u2="&#xc3;" k="10" />
    <hkern u1="x" u2="&#xc2;" k="10" />
    <hkern u1="x" u2="&#xc1;" k="10" />
    <hkern u1="x" u2="&#xc0;" k="10" />
    <hkern u1="x" u2="y" k="5" />
    <hkern u1="x" u2="x" k="5" />
    <hkern u1="x" u2="w" k="5" />
    <hkern u1="x" u2="v" k="10" />
    <hkern u1="x" u2="u" k="5" />
    <hkern u1="x" u2="s" k="5" />
    <hkern u1="x" u2="q" k="16" />
    <hkern u1="x" u2="o" k="18" />
    <hkern u1="x" u2="g" k="16" />
    <hkern u1="x" u2="e" k="16" />
    <hkern u1="x" u2="d" k="16" />
    <hkern u1="x" u2="c" k="16" />
    <hkern u1="x" u2="a" k="10" />
    <hkern u1="x" u2="Y" k="41" />
    <hkern u1="x" u2="X" k="15" />
    <hkern u1="x" u2="W" k="15" />
    <hkern u1="x" u2="V" k="22" />
    <hkern u1="x" u2="U" k="14" />
    <hkern u1="x" u2="T" k="31" />
    <hkern u1="x" u2="A" k="10" />
    <hkern u1="x" u2="&#x27;" k="20" />
    <hkern u1="x" u2="&#x22;" k="20" />
    <hkern u1="z" g2="fl" k="-10" />
    <hkern u1="z" g2="fi" k="-10" />
    <hkern u1="z" u2="&#x178;" k="20" />
    <hkern u1="z" u2="&#x153;" k="10" />
    <hkern u1="z" u2="&#xff;" k="10" />
    <hkern u1="z" u2="&#xfd;" k="10" />
    <hkern u1="z" u2="&#xfc;" k="10" />
    <hkern u1="z" u2="&#xfb;" k="10" />
    <hkern u1="z" u2="&#xfa;" k="10" />
    <hkern u1="z" u2="&#xf9;" k="10" />
    <hkern u1="z" u2="&#xf6;" k="10" />
    <hkern u1="z" u2="&#xf5;" k="10" />
    <hkern u1="z" u2="&#xf4;" k="10" />
    <hkern u1="z" u2="&#xf3;" k="10" />
    <hkern u1="z" u2="&#xf2;" k="10" />
    <hkern u1="z" u2="&#xf0;" k="10" />
    <hkern u1="z" u2="&#xeb;" k="10" />
    <hkern u1="z" u2="&#xea;" k="10" />
    <hkern u1="z" u2="&#xe9;" k="10" />
    <hkern u1="z" u2="&#xe8;" k="10" />
    <hkern u1="z" u2="&#xe7;" k="10" />
    <hkern u1="z" u2="&#xe6;" k="5" />
    <hkern u1="z" u2="&#xe5;" k="5" />
    <hkern u1="z" u2="&#xe4;" k="5" />
    <hkern u1="z" u2="&#xe3;" k="5" />
    <hkern u1="z" u2="&#xe2;" k="5" />
    <hkern u1="z" u2="&#xe1;" k="5" />
    <hkern u1="z" u2="&#xe0;" k="5" />
    <hkern u1="z" u2="&#xdd;" k="20" />
    <hkern u1="z" u2="y" k="10" />
    <hkern u1="z" u2="x" k="5" />
    <hkern u1="z" u2="w" k="10" />
    <hkern u1="z" u2="u" k="10" />
    <hkern u1="z" u2="t" k="-10" />
    <hkern u1="z" u2="q" k="10" />
    <hkern u1="z" u2="o" k="10" />
    <hkern u1="z" u2="g" k="10" />
    <hkern u1="z" u2="f" k="-10" />
    <hkern u1="z" u2="e" k="10" />
    <hkern u1="z" u2="d" k="10" />
    <hkern u1="z" u2="c" k="10" />
    <hkern u1="z" u2="a" k="5" />
    <hkern u1="z" u2="Y" k="20" />
    <hkern u1="z" u2="W" k="6" />
    <hkern u1="z" u2="V" k="20" />
    <hkern u1="z" u2="T" k="20" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc0;" u2="&#x178;" k="100" />
    <hkern u1="&#xc0;" u2="&#x153;" k="10" />
    <hkern u1="&#xc0;" u2="&#x152;" k="20" />
    <hkern u1="&#xc0;" u2="&#xff;" k="10" />
    <hkern u1="&#xc0;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc0;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc0;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc0;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc0;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc0;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc0;" u2="&#xea;" k="10" />
    <hkern u1="&#xc0;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc0;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc0;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc0;" u2="&#xda;" k="14" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc0;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc0;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc0;" u2="y" k="10" />
    <hkern u1="&#xc0;" u2="x" k="10" />
    <hkern u1="&#xc0;" u2="w" k="29" />
    <hkern u1="&#xc0;" u2="v" k="51" />
    <hkern u1="&#xc0;" u2="u" k="10" />
    <hkern u1="&#xc0;" u2="t" k="14" />
    <hkern u1="&#xc0;" u2="q" k="10" />
    <hkern u1="&#xc0;" u2="o" k="10" />
    <hkern u1="&#xc0;" u2="g" k="10" />
    <hkern u1="&#xc0;" u2="e" k="10" />
    <hkern u1="&#xc0;" u2="d" k="10" />
    <hkern u1="&#xc0;" u2="c" k="10" />
    <hkern u1="&#xc0;" u2="Y" k="100" />
    <hkern u1="&#xc0;" u2="X" k="20" />
    <hkern u1="&#xc0;" u2="W" k="73" />
    <hkern u1="&#xc0;" u2="V" k="112" />
    <hkern u1="&#xc0;" u2="U" k="14" />
    <hkern u1="&#xc0;" u2="T" k="57" />
    <hkern u1="&#xc0;" u2="Q" k="20" />
    <hkern u1="&#xc0;" u2="O" k="20" />
    <hkern u1="&#xc0;" u2="J" k="-14" />
    <hkern u1="&#xc0;" u2="G" k="20" />
    <hkern u1="&#xc0;" u2="C" k="20" />
    <hkern u1="&#xc0;" u2="A" k="-6" />
    <hkern u1="&#xc0;" u2="&#x27;" k="67" />
    <hkern u1="&#xc0;" u2="&#x22;" k="67" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc1;" u2="&#x178;" k="100" />
    <hkern u1="&#xc1;" u2="&#x153;" k="10" />
    <hkern u1="&#xc1;" u2="&#x152;" k="20" />
    <hkern u1="&#xc1;" u2="&#xff;" k="10" />
    <hkern u1="&#xc1;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc1;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc1;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc1;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc1;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc1;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc1;" u2="&#xea;" k="10" />
    <hkern u1="&#xc1;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc1;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc1;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc1;" u2="&#xda;" k="14" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc1;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc1;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc1;" u2="y" k="10" />
    <hkern u1="&#xc1;" u2="x" k="10" />
    <hkern u1="&#xc1;" u2="w" k="29" />
    <hkern u1="&#xc1;" u2="v" k="51" />
    <hkern u1="&#xc1;" u2="u" k="10" />
    <hkern u1="&#xc1;" u2="t" k="14" />
    <hkern u1="&#xc1;" u2="q" k="10" />
    <hkern u1="&#xc1;" u2="o" k="10" />
    <hkern u1="&#xc1;" u2="g" k="10" />
    <hkern u1="&#xc1;" u2="e" k="10" />
    <hkern u1="&#xc1;" u2="d" k="10" />
    <hkern u1="&#xc1;" u2="c" k="10" />
    <hkern u1="&#xc1;" u2="Y" k="100" />
    <hkern u1="&#xc1;" u2="X" k="20" />
    <hkern u1="&#xc1;" u2="W" k="73" />
    <hkern u1="&#xc1;" u2="V" k="112" />
    <hkern u1="&#xc1;" u2="U" k="14" />
    <hkern u1="&#xc1;" u2="T" k="57" />
    <hkern u1="&#xc1;" u2="Q" k="20" />
    <hkern u1="&#xc1;" u2="O" k="20" />
    <hkern u1="&#xc1;" u2="J" k="-14" />
    <hkern u1="&#xc1;" u2="G" k="20" />
    <hkern u1="&#xc1;" u2="C" k="20" />
    <hkern u1="&#xc1;" u2="A" k="-6" />
    <hkern u1="&#xc1;" u2="&#x27;" k="67" />
    <hkern u1="&#xc1;" u2="&#x22;" k="67" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc2;" u2="&#x178;" k="100" />
    <hkern u1="&#xc2;" u2="&#x153;" k="10" />
    <hkern u1="&#xc2;" u2="&#x152;" k="20" />
    <hkern u1="&#xc2;" u2="&#xff;" k="10" />
    <hkern u1="&#xc2;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc2;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc2;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc2;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc2;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc2;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc2;" u2="&#xea;" k="10" />
    <hkern u1="&#xc2;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc2;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc2;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc2;" u2="&#xda;" k="14" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc2;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc2;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc2;" u2="y" k="10" />
    <hkern u1="&#xc2;" u2="x" k="10" />
    <hkern u1="&#xc2;" u2="w" k="29" />
    <hkern u1="&#xc2;" u2="v" k="51" />
    <hkern u1="&#xc2;" u2="u" k="10" />
    <hkern u1="&#xc2;" u2="t" k="14" />
    <hkern u1="&#xc2;" u2="q" k="10" />
    <hkern u1="&#xc2;" u2="o" k="10" />
    <hkern u1="&#xc2;" u2="g" k="10" />
    <hkern u1="&#xc2;" u2="e" k="10" />
    <hkern u1="&#xc2;" u2="d" k="10" />
    <hkern u1="&#xc2;" u2="c" k="10" />
    <hkern u1="&#xc2;" u2="Y" k="100" />
    <hkern u1="&#xc2;" u2="X" k="20" />
    <hkern u1="&#xc2;" u2="W" k="73" />
    <hkern u1="&#xc2;" u2="V" k="112" />
    <hkern u1="&#xc2;" u2="U" k="14" />
    <hkern u1="&#xc2;" u2="T" k="57" />
    <hkern u1="&#xc2;" u2="Q" k="20" />
    <hkern u1="&#xc2;" u2="O" k="20" />
    <hkern u1="&#xc2;" u2="J" k="-14" />
    <hkern u1="&#xc2;" u2="G" k="20" />
    <hkern u1="&#xc2;" u2="C" k="20" />
    <hkern u1="&#xc2;" u2="A" k="-6" />
    <hkern u1="&#xc2;" u2="&#x27;" k="67" />
    <hkern u1="&#xc2;" u2="&#x22;" k="67" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc3;" u2="&#x178;" k="100" />
    <hkern u1="&#xc3;" u2="&#x153;" k="10" />
    <hkern u1="&#xc3;" u2="&#x152;" k="20" />
    <hkern u1="&#xc3;" u2="&#xff;" k="10" />
    <hkern u1="&#xc3;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc3;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc3;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc3;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc3;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc3;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc3;" u2="&#xea;" k="10" />
    <hkern u1="&#xc3;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc3;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc3;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc3;" u2="&#xda;" k="14" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc3;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc3;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc3;" u2="y" k="10" />
    <hkern u1="&#xc3;" u2="x" k="10" />
    <hkern u1="&#xc3;" u2="w" k="29" />
    <hkern u1="&#xc3;" u2="v" k="51" />
    <hkern u1="&#xc3;" u2="u" k="10" />
    <hkern u1="&#xc3;" u2="t" k="14" />
    <hkern u1="&#xc3;" u2="q" k="10" />
    <hkern u1="&#xc3;" u2="o" k="10" />
    <hkern u1="&#xc3;" u2="g" k="10" />
    <hkern u1="&#xc3;" u2="e" k="10" />
    <hkern u1="&#xc3;" u2="d" k="10" />
    <hkern u1="&#xc3;" u2="c" k="10" />
    <hkern u1="&#xc3;" u2="Y" k="100" />
    <hkern u1="&#xc3;" u2="X" k="20" />
    <hkern u1="&#xc3;" u2="W" k="73" />
    <hkern u1="&#xc3;" u2="V" k="112" />
    <hkern u1="&#xc3;" u2="U" k="14" />
    <hkern u1="&#xc3;" u2="T" k="57" />
    <hkern u1="&#xc3;" u2="Q" k="20" />
    <hkern u1="&#xc3;" u2="O" k="20" />
    <hkern u1="&#xc3;" u2="J" k="-14" />
    <hkern u1="&#xc3;" u2="G" k="20" />
    <hkern u1="&#xc3;" u2="C" k="20" />
    <hkern u1="&#xc3;" u2="A" k="-6" />
    <hkern u1="&#xc3;" u2="&#x27;" k="67" />
    <hkern u1="&#xc3;" u2="&#x22;" k="67" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc4;" u2="&#x178;" k="100" />
    <hkern u1="&#xc4;" u2="&#x153;" k="10" />
    <hkern u1="&#xc4;" u2="&#x152;" k="20" />
    <hkern u1="&#xc4;" u2="&#xff;" k="10" />
    <hkern u1="&#xc4;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc4;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc4;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc4;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc4;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc4;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc4;" u2="&#xea;" k="10" />
    <hkern u1="&#xc4;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc4;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc4;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc4;" u2="&#xda;" k="14" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc4;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc4;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc4;" u2="y" k="10" />
    <hkern u1="&#xc4;" u2="x" k="10" />
    <hkern u1="&#xc4;" u2="w" k="29" />
    <hkern u1="&#xc4;" u2="v" k="51" />
    <hkern u1="&#xc4;" u2="u" k="10" />
    <hkern u1="&#xc4;" u2="t" k="14" />
    <hkern u1="&#xc4;" u2="q" k="10" />
    <hkern u1="&#xc4;" u2="o" k="10" />
    <hkern u1="&#xc4;" u2="g" k="10" />
    <hkern u1="&#xc4;" u2="e" k="10" />
    <hkern u1="&#xc4;" u2="d" k="10" />
    <hkern u1="&#xc4;" u2="c" k="10" />
    <hkern u1="&#xc4;" u2="Y" k="100" />
    <hkern u1="&#xc4;" u2="X" k="20" />
    <hkern u1="&#xc4;" u2="W" k="73" />
    <hkern u1="&#xc4;" u2="V" k="112" />
    <hkern u1="&#xc4;" u2="U" k="14" />
    <hkern u1="&#xc4;" u2="T" k="57" />
    <hkern u1="&#xc4;" u2="Q" k="20" />
    <hkern u1="&#xc4;" u2="O" k="20" />
    <hkern u1="&#xc4;" u2="J" k="-14" />
    <hkern u1="&#xc4;" u2="G" k="20" />
    <hkern u1="&#xc4;" u2="C" k="20" />
    <hkern u1="&#xc4;" u2="A" k="-6" />
    <hkern u1="&#xc4;" u2="&#x27;" k="67" />
    <hkern u1="&#xc4;" u2="&#x22;" k="67" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="71" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="71" />
    <hkern u1="&#xc5;" u2="&#x178;" k="100" />
    <hkern u1="&#xc5;" u2="&#x153;" k="10" />
    <hkern u1="&#xc5;" u2="&#x152;" k="20" />
    <hkern u1="&#xc5;" u2="&#xff;" k="10" />
    <hkern u1="&#xc5;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc5;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc5;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc5;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc5;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc5;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc5;" u2="&#xea;" k="10" />
    <hkern u1="&#xc5;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc5;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc5;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="100" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="14" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="14" />
    <hkern u1="&#xc5;" u2="&#xda;" k="14" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="14" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="20" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="20" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="20" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="20" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="20" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="20" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc5;" u2="&#xc6;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc5;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc4;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc3;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc2;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc1;" k="-6" />
    <hkern u1="&#xc5;" u2="&#xc0;" k="-6" />
    <hkern u1="&#xc5;" u2="y" k="10" />
    <hkern u1="&#xc5;" u2="x" k="10" />
    <hkern u1="&#xc5;" u2="w" k="29" />
    <hkern u1="&#xc5;" u2="v" k="51" />
    <hkern u1="&#xc5;" u2="u" k="10" />
    <hkern u1="&#xc5;" u2="t" k="14" />
    <hkern u1="&#xc5;" u2="q" k="10" />
    <hkern u1="&#xc5;" u2="o" k="10" />
    <hkern u1="&#xc5;" u2="g" k="10" />
    <hkern u1="&#xc5;" u2="e" k="10" />
    <hkern u1="&#xc5;" u2="d" k="10" />
    <hkern u1="&#xc5;" u2="c" k="10" />
    <hkern u1="&#xc5;" u2="Y" k="100" />
    <hkern u1="&#xc5;" u2="X" k="20" />
    <hkern u1="&#xc5;" u2="W" k="73" />
    <hkern u1="&#xc5;" u2="V" k="112" />
    <hkern u1="&#xc5;" u2="U" k="14" />
    <hkern u1="&#xc5;" u2="T" k="57" />
    <hkern u1="&#xc5;" u2="Q" k="20" />
    <hkern u1="&#xc5;" u2="O" k="20" />
    <hkern u1="&#xc5;" u2="J" k="-14" />
    <hkern u1="&#xc5;" u2="G" k="20" />
    <hkern u1="&#xc5;" u2="C" k="20" />
    <hkern u1="&#xc5;" u2="A" k="-6" />
    <hkern u1="&#xc5;" u2="&#x27;" k="67" />
    <hkern u1="&#xc5;" u2="&#x22;" k="67" />
    <hkern u1="&#xc6;" u2="&#x153;" k="10" />
    <hkern u1="&#xc6;" u2="&#x152;" k="10" />
    <hkern u1="&#xc6;" u2="&#xff;" k="10" />
    <hkern u1="&#xc6;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc6;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc6;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc6;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc6;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc6;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc6;" u2="&#xea;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe6;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe5;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe4;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe3;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe2;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe1;" k="10" />
    <hkern u1="&#xc6;" u2="&#xe0;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd8;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd6;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd5;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd4;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd3;" k="10" />
    <hkern u1="&#xc6;" u2="&#xd2;" k="10" />
    <hkern u1="&#xc6;" u2="y" k="10" />
    <hkern u1="&#xc6;" u2="x" k="10" />
    <hkern u1="&#xc6;" u2="v" k="10" />
    <hkern u1="&#xc6;" u2="u" k="10" />
    <hkern u1="&#xc6;" u2="q" k="10" />
    <hkern u1="&#xc6;" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="g" k="10" />
    <hkern u1="&#xc6;" u2="e" k="10" />
    <hkern u1="&#xc6;" u2="d" k="10" />
    <hkern u1="&#xc6;" u2="c" k="10" />
    <hkern u1="&#xc6;" u2="a" k="10" />
    <hkern u1="&#xc6;" u2="X" k="10" />
    <hkern u1="&#xc6;" u2="Q" k="10" />
    <hkern u1="&#xc6;" u2="O" k="10" />
    <hkern u1="&#xc6;" u2="G" k="10" />
    <hkern u1="&#xc7;" u2="&#x178;" k="10" />
    <hkern u1="&#xc7;" u2="&#x161;" k="10" />
    <hkern u1="&#xc7;" u2="&#x153;" k="20" />
    <hkern u1="&#xc7;" u2="&#x152;" k="37" />
    <hkern u1="&#xc7;" u2="&#xff;" k="10" />
    <hkern u1="&#xc7;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc7;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc7;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc7;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc7;" u2="&#xf6;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf5;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf4;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf3;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf2;" k="20" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="20" />
    <hkern u1="&#xc7;" u2="&#xeb;" k="20" />
    <hkern u1="&#xc7;" u2="&#xea;" k="20" />
    <hkern u1="&#xc7;" u2="&#xe9;" k="20" />
    <hkern u1="&#xc7;" u2="&#xe8;" k="20" />
    <hkern u1="&#xc7;" u2="&#xe7;" k="20" />
    <hkern u1="&#xc7;" u2="&#xe6;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe5;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe4;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe3;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe2;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe1;" k="10" />
    <hkern u1="&#xc7;" u2="&#xe0;" k="10" />
    <hkern u1="&#xc7;" u2="&#xdd;" k="10" />
    <hkern u1="&#xc7;" u2="&#xd8;" k="37" />
    <hkern u1="&#xc7;" u2="&#xd6;" k="37" />
    <hkern u1="&#xc7;" u2="&#xd5;" k="37" />
    <hkern u1="&#xc7;" u2="&#xd4;" k="37" />
    <hkern u1="&#xc7;" u2="&#xd3;" k="37" />
    <hkern u1="&#xc7;" u2="&#xd2;" k="37" />
    <hkern u1="&#xc7;" u2="&#xc7;" k="20" />
    <hkern u1="&#xc7;" u2="y" k="10" />
    <hkern u1="&#xc7;" u2="x" k="10" />
    <hkern u1="&#xc7;" u2="w" k="20" />
    <hkern u1="&#xc7;" u2="v" k="45" />
    <hkern u1="&#xc7;" u2="u" k="10" />
    <hkern u1="&#xc7;" u2="s" k="10" />
    <hkern u1="&#xc7;" u2="q" k="20" />
    <hkern u1="&#xc7;" u2="o" k="20" />
    <hkern u1="&#xc7;" u2="g" k="20" />
    <hkern u1="&#xc7;" u2="e" k="20" />
    <hkern u1="&#xc7;" u2="d" k="20" />
    <hkern u1="&#xc7;" u2="c" k="20" />
    <hkern u1="&#xc7;" u2="a" k="10" />
    <hkern u1="&#xc7;" u2="Y" k="10" />
    <hkern u1="&#xc7;" u2="X" k="14" />
    <hkern u1="&#xc7;" u2="Q" k="37" />
    <hkern u1="&#xc7;" u2="O" k="37" />
    <hkern u1="&#xc7;" u2="J" k="-10" />
    <hkern u1="&#xc7;" u2="G" k="31" />
    <hkern u1="&#xc7;" u2="C" k="20" />
    <hkern u1="&#xc8;" u2="&#x153;" k="10" />
    <hkern u1="&#xc8;" u2="&#x152;" k="10" />
    <hkern u1="&#xc8;" u2="&#xff;" k="10" />
    <hkern u1="&#xc8;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc8;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc8;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc8;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc8;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc8;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc8;" u2="&#xea;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe6;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe5;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe4;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe3;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe2;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe1;" k="10" />
    <hkern u1="&#xc8;" u2="&#xe0;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd8;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd6;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd5;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd4;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd3;" k="10" />
    <hkern u1="&#xc8;" u2="&#xd2;" k="10" />
    <hkern u1="&#xc8;" u2="y" k="10" />
    <hkern u1="&#xc8;" u2="x" k="10" />
    <hkern u1="&#xc8;" u2="v" k="10" />
    <hkern u1="&#xc8;" u2="u" k="10" />
    <hkern u1="&#xc8;" u2="q" k="10" />
    <hkern u1="&#xc8;" u2="o" k="10" />
    <hkern u1="&#xc8;" u2="g" k="10" />
    <hkern u1="&#xc8;" u2="e" k="10" />
    <hkern u1="&#xc8;" u2="d" k="10" />
    <hkern u1="&#xc8;" u2="c" k="10" />
    <hkern u1="&#xc8;" u2="a" k="10" />
    <hkern u1="&#xc8;" u2="X" k="10" />
    <hkern u1="&#xc8;" u2="Q" k="10" />
    <hkern u1="&#xc8;" u2="O" k="10" />
    <hkern u1="&#xc8;" u2="G" k="10" />
    <hkern u1="&#xc9;" u2="&#x153;" k="10" />
    <hkern u1="&#xc9;" u2="&#x152;" k="10" />
    <hkern u1="&#xc9;" u2="&#xff;" k="10" />
    <hkern u1="&#xc9;" u2="&#xfd;" k="10" />
    <hkern u1="&#xc9;" u2="&#xfc;" k="10" />
    <hkern u1="&#xc9;" u2="&#xfb;" k="10" />
    <hkern u1="&#xc9;" u2="&#xfa;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf9;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf6;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf5;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf4;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf3;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf2;" k="10" />
    <hkern u1="&#xc9;" u2="&#xf0;" k="10" />
    <hkern u1="&#xc9;" u2="&#xeb;" k="10" />
    <hkern u1="&#xc9;" u2="&#xea;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe9;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe8;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe7;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe6;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe5;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe4;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe3;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe2;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe1;" k="10" />
    <hkern u1="&#xc9;" u2="&#xe0;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd8;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd6;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd5;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd4;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd3;" k="10" />
    <hkern u1="&#xc9;" u2="&#xd2;" k="10" />
    <hkern u1="&#xc9;" u2="y" k="10" />
    <hkern u1="&#xc9;" u2="x" k="10" />
    <hkern u1="&#xc9;" u2="v" k="10" />
    <hkern u1="&#xc9;" u2="u" k="10" />
    <hkern u1="&#xc9;" u2="q" k="10" />
    <hkern u1="&#xc9;" u2="o" k="10" />
    <hkern u1="&#xc9;" u2="g" k="10" />
    <hkern u1="&#xc9;" u2="e" k="10" />
    <hkern u1="&#xc9;" u2="d" k="10" />
    <hkern u1="&#xc9;" u2="c" k="10" />
    <hkern u1="&#xc9;" u2="a" k="10" />
    <hkern u1="&#xc9;" u2="X" k="10" />
    <hkern u1="&#xc9;" u2="Q" k="10" />
    <hkern u1="&#xc9;" u2="O" k="10" />
    <hkern u1="&#xc9;" u2="G" k="10" />
    <hkern u1="&#xca;" u2="&#x153;" k="10" />
    <hkern u1="&#xca;" u2="&#x152;" k="10" />
    <hkern u1="&#xca;" u2="&#xff;" k="10" />
    <hkern u1="&#xca;" u2="&#xfd;" k="10" />
    <hkern u1="&#xca;" u2="&#xfc;" k="10" />
    <hkern u1="&#xca;" u2="&#xfb;" k="10" />
    <hkern u1="&#xca;" u2="&#xfa;" k="10" />
    <hkern u1="&#xca;" u2="&#xf9;" k="10" />
    <hkern u1="&#xca;" u2="&#xf6;" k="10" />
    <hkern u1="&#xca;" u2="&#xf5;" k="10" />
    <hkern u1="&#xca;" u2="&#xf4;" k="10" />
    <hkern u1="&#xca;" u2="&#xf3;" k="10" />
    <hkern u1="&#xca;" u2="&#xf2;" k="10" />
    <hkern u1="&#xca;" u2="&#xf0;" k="10" />
    <hkern u1="&#xca;" u2="&#xeb;" k="10" />
    <hkern u1="&#xca;" u2="&#xea;" k="10" />
    <hkern u1="&#xca;" u2="&#xe9;" k="10" />
    <hkern u1="&#xca;" u2="&#xe8;" k="10" />
    <hkern u1="&#xca;" u2="&#xe7;" k="10" />
    <hkern u1="&#xca;" u2="&#xe6;" k="10" />
    <hkern u1="&#xca;" u2="&#xe5;" k="10" />
    <hkern u1="&#xca;" u2="&#xe4;" k="10" />
    <hkern u1="&#xca;" u2="&#xe3;" k="10" />
    <hkern u1="&#xca;" u2="&#xe2;" k="10" />
    <hkern u1="&#xca;" u2="&#xe1;" k="10" />
    <hkern u1="&#xca;" u2="&#xe0;" k="10" />
    <hkern u1="&#xca;" u2="&#xd8;" k="10" />
    <hkern u1="&#xca;" u2="&#xd6;" k="10" />
    <hkern u1="&#xca;" u2="&#xd5;" k="10" />
    <hkern u1="&#xca;" u2="&#xd4;" k="10" />
    <hkern u1="&#xca;" u2="&#xd3;" k="10" />
    <hkern u1="&#xca;" u2="&#xd2;" k="10" />
    <hkern u1="&#xca;" u2="y" k="10" />
    <hkern u1="&#xca;" u2="x" k="10" />
    <hkern u1="&#xca;" u2="v" k="10" />
    <hkern u1="&#xca;" u2="u" k="10" />
    <hkern u1="&#xca;" u2="q" k="10" />
    <hkern u1="&#xca;" u2="o" k="10" />
    <hkern u1="&#xca;" u2="g" k="10" />
    <hkern u1="&#xca;" u2="e" k="10" />
    <hkern u1="&#xca;" u2="d" k="10" />
    <hkern u1="&#xca;" u2="c" k="10" />
    <hkern u1="&#xca;" u2="a" k="10" />
    <hkern u1="&#xca;" u2="X" k="10" />
    <hkern u1="&#xca;" u2="Q" k="10" />
    <hkern u1="&#xca;" u2="O" k="10" />
    <hkern u1="&#xca;" u2="G" k="10" />
    <hkern u1="&#xcb;" u2="&#x153;" k="10" />
    <hkern u1="&#xcb;" u2="&#x152;" k="10" />
    <hkern u1="&#xcb;" u2="&#xff;" k="10" />
    <hkern u1="&#xcb;" u2="&#xfd;" k="10" />
    <hkern u1="&#xcb;" u2="&#xfc;" k="10" />
    <hkern u1="&#xcb;" u2="&#xfb;" k="10" />
    <hkern u1="&#xcb;" u2="&#xfa;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf9;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf6;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf5;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf4;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf3;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf2;" k="10" />
    <hkern u1="&#xcb;" u2="&#xf0;" k="10" />
    <hkern u1="&#xcb;" u2="&#xeb;" k="10" />
    <hkern u1="&#xcb;" u2="&#xea;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe9;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe8;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe7;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe6;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe5;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe4;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe3;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe2;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe1;" k="10" />
    <hkern u1="&#xcb;" u2="&#xe0;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd8;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd6;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd5;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd4;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd3;" k="10" />
    <hkern u1="&#xcb;" u2="&#xd2;" k="10" />
    <hkern u1="&#xcb;" u2="y" k="10" />
    <hkern u1="&#xcb;" u2="x" k="10" />
    <hkern u1="&#xcb;" u2="v" k="10" />
    <hkern u1="&#xcb;" u2="u" k="10" />
    <hkern u1="&#xcb;" u2="q" k="10" />
    <hkern u1="&#xcb;" u2="o" k="10" />
    <hkern u1="&#xcb;" u2="g" k="10" />
    <hkern u1="&#xcb;" u2="e" k="10" />
    <hkern u1="&#xcb;" u2="d" k="10" />
    <hkern u1="&#xcb;" u2="c" k="10" />
    <hkern u1="&#xcb;" u2="a" k="10" />
    <hkern u1="&#xcb;" u2="X" k="10" />
    <hkern u1="&#xcb;" u2="Q" k="10" />
    <hkern u1="&#xcb;" u2="O" k="10" />
    <hkern u1="&#xcb;" u2="G" k="10" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="14" />
    <hkern u1="&#xd0;" u2="&#x178;" k="22" />
    <hkern u1="&#xd0;" u2="&#x160;" k="10" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="22" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="24" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="24" />
    <hkern u1="&#xd0;" u2="x" k="10" />
    <hkern u1="&#xd0;" u2="v" k="10" />
    <hkern u1="&#xd0;" u2="Z" k="14" />
    <hkern u1="&#xd0;" u2="Y" k="22" />
    <hkern u1="&#xd0;" u2="X" k="24" />
    <hkern u1="&#xd0;" u2="W" k="5" />
    <hkern u1="&#xd0;" u2="V" k="20" />
    <hkern u1="&#xd0;" u2="T" k="10" />
    <hkern u1="&#xd0;" u2="S" k="10" />
    <hkern u1="&#xd0;" u2="J" k="24" />
    <hkern u1="&#xd0;" u2="A" k="24" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd2;" u2="&#x178;" k="24" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd2;" u2="Z" k="4" />
    <hkern u1="&#xd2;" u2="Y" k="24" />
    <hkern u1="&#xd2;" u2="X" k="27" />
    <hkern u1="&#xd2;" u2="W" k="5" />
    <hkern u1="&#xd2;" u2="V" k="15" />
    <hkern u1="&#xd2;" u2="T" k="10" />
    <hkern u1="&#xd2;" u2="J" k="10" />
    <hkern u1="&#xd2;" u2="A" k="20" />
    <hkern u1="&#xd2;" u2="&#x27;" k="10" />
    <hkern u1="&#xd2;" u2="&#x22;" k="10" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd3;" u2="&#x178;" k="24" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd3;" u2="Z" k="4" />
    <hkern u1="&#xd3;" u2="Y" k="24" />
    <hkern u1="&#xd3;" u2="X" k="27" />
    <hkern u1="&#xd3;" u2="W" k="5" />
    <hkern u1="&#xd3;" u2="V" k="15" />
    <hkern u1="&#xd3;" u2="T" k="10" />
    <hkern u1="&#xd3;" u2="J" k="10" />
    <hkern u1="&#xd3;" u2="A" k="20" />
    <hkern u1="&#xd3;" u2="&#x27;" k="10" />
    <hkern u1="&#xd3;" u2="&#x22;" k="10" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd4;" u2="&#x178;" k="24" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd4;" u2="Z" k="4" />
    <hkern u1="&#xd4;" u2="Y" k="24" />
    <hkern u1="&#xd4;" u2="X" k="27" />
    <hkern u1="&#xd4;" u2="W" k="5" />
    <hkern u1="&#xd4;" u2="V" k="15" />
    <hkern u1="&#xd4;" u2="T" k="10" />
    <hkern u1="&#xd4;" u2="J" k="10" />
    <hkern u1="&#xd4;" u2="A" k="20" />
    <hkern u1="&#xd4;" u2="&#x27;" k="10" />
    <hkern u1="&#xd4;" u2="&#x22;" k="10" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd5;" u2="&#x178;" k="24" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd5;" u2="Z" k="4" />
    <hkern u1="&#xd5;" u2="Y" k="24" />
    <hkern u1="&#xd5;" u2="X" k="27" />
    <hkern u1="&#xd5;" u2="W" k="5" />
    <hkern u1="&#xd5;" u2="V" k="15" />
    <hkern u1="&#xd5;" u2="T" k="10" />
    <hkern u1="&#xd5;" u2="J" k="10" />
    <hkern u1="&#xd5;" u2="A" k="20" />
    <hkern u1="&#xd5;" u2="&#x27;" k="10" />
    <hkern u1="&#xd5;" u2="&#x22;" k="10" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd6;" u2="&#x178;" k="24" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd6;" u2="Z" k="4" />
    <hkern u1="&#xd6;" u2="Y" k="24" />
    <hkern u1="&#xd6;" u2="X" k="27" />
    <hkern u1="&#xd6;" u2="W" k="5" />
    <hkern u1="&#xd6;" u2="V" k="15" />
    <hkern u1="&#xd6;" u2="T" k="10" />
    <hkern u1="&#xd6;" u2="J" k="10" />
    <hkern u1="&#xd6;" u2="A" k="20" />
    <hkern u1="&#xd6;" u2="&#x27;" k="10" />
    <hkern u1="&#xd6;" u2="&#x22;" k="10" />
    <hkern u1="&#xd8;" u2="&#x17d;" k="4" />
    <hkern u1="&#xd8;" u2="&#x178;" k="24" />
    <hkern u1="&#xd8;" u2="&#xdd;" k="24" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc5;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc4;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc3;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc2;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc1;" k="20" />
    <hkern u1="&#xd8;" u2="&#xc0;" k="20" />
    <hkern u1="&#xd8;" u2="Z" k="4" />
    <hkern u1="&#xd8;" u2="Y" k="24" />
    <hkern u1="&#xd8;" u2="X" k="27" />
    <hkern u1="&#xd8;" u2="W" k="5" />
    <hkern u1="&#xd8;" u2="V" k="15" />
    <hkern u1="&#xd8;" u2="T" k="10" />
    <hkern u1="&#xd8;" u2="J" k="10" />
    <hkern u1="&#xd8;" u2="A" k="20" />
    <hkern u1="&#xd8;" u2="&#x27;" k="10" />
    <hkern u1="&#xd8;" u2="&#x22;" k="10" />
    <hkern u1="&#xd9;" u2="&#xf0;" k="6" />
    <hkern u1="&#xd9;" u2="&#xeb;" k="6" />
    <hkern u1="&#xd9;" u2="&#xea;" k="6" />
    <hkern u1="&#xd9;" u2="&#xe9;" k="6" />
    <hkern u1="&#xd9;" u2="&#xe8;" k="6" />
    <hkern u1="&#xd9;" u2="&#xe6;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe5;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe4;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe3;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe2;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe1;" k="10" />
    <hkern u1="&#xd9;" u2="&#xe0;" k="10" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="14" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="14" />
    <hkern u1="&#xd9;" u2="x" k="14" />
    <hkern u1="&#xd9;" u2="q" k="6" />
    <hkern u1="&#xd9;" u2="g" k="6" />
    <hkern u1="&#xd9;" u2="e" k="6" />
    <hkern u1="&#xd9;" u2="d" k="6" />
    <hkern u1="&#xd9;" u2="a" k="10" />
    <hkern u1="&#xd9;" u2="X" k="10" />
    <hkern u1="&#xd9;" u2="A" k="14" />
    <hkern u1="&#xda;" u2="&#xf0;" k="6" />
    <hkern u1="&#xda;" u2="&#xeb;" k="6" />
    <hkern u1="&#xda;" u2="&#xea;" k="6" />
    <hkern u1="&#xda;" u2="&#xe9;" k="6" />
    <hkern u1="&#xda;" u2="&#xe8;" k="6" />
    <hkern u1="&#xda;" u2="&#xe6;" k="10" />
    <hkern u1="&#xda;" u2="&#xe5;" k="10" />
    <hkern u1="&#xda;" u2="&#xe4;" k="10" />
    <hkern u1="&#xda;" u2="&#xe3;" k="10" />
    <hkern u1="&#xda;" u2="&#xe2;" k="10" />
    <hkern u1="&#xda;" u2="&#xe1;" k="10" />
    <hkern u1="&#xda;" u2="&#xe0;" k="10" />
    <hkern u1="&#xda;" u2="&#xc6;" k="14" />
    <hkern u1="&#xda;" u2="&#xc5;" k="14" />
    <hkern u1="&#xda;" u2="&#xc4;" k="14" />
    <hkern u1="&#xda;" u2="&#xc3;" k="14" />
    <hkern u1="&#xda;" u2="&#xc2;" k="14" />
    <hkern u1="&#xda;" u2="&#xc1;" k="14" />
    <hkern u1="&#xda;" u2="&#xc0;" k="14" />
    <hkern u1="&#xda;" u2="x" k="14" />
    <hkern u1="&#xda;" u2="q" k="6" />
    <hkern u1="&#xda;" u2="g" k="6" />
    <hkern u1="&#xda;" u2="e" k="6" />
    <hkern u1="&#xda;" u2="d" k="6" />
    <hkern u1="&#xda;" u2="a" k="10" />
    <hkern u1="&#xda;" u2="X" k="10" />
    <hkern u1="&#xda;" u2="A" k="14" />
    <hkern u1="&#xdb;" u2="&#xf0;" k="6" />
    <hkern u1="&#xdb;" u2="&#xeb;" k="6" />
    <hkern u1="&#xdb;" u2="&#xea;" k="6" />
    <hkern u1="&#xdb;" u2="&#xe9;" k="6" />
    <hkern u1="&#xdb;" u2="&#xe8;" k="6" />
    <hkern u1="&#xdb;" u2="&#xe6;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe5;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe4;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe3;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe2;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe1;" k="10" />
    <hkern u1="&#xdb;" u2="&#xe0;" k="10" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="14" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="14" />
    <hkern u1="&#xdb;" u2="x" k="14" />
    <hkern u1="&#xdb;" u2="q" k="6" />
    <hkern u1="&#xdb;" u2="g" k="6" />
    <hkern u1="&#xdb;" u2="e" k="6" />
    <hkern u1="&#xdb;" u2="d" k="6" />
    <hkern u1="&#xdb;" u2="a" k="10" />
    <hkern u1="&#xdb;" u2="X" k="10" />
    <hkern u1="&#xdb;" u2="A" k="14" />
    <hkern u1="&#xdc;" u2="&#xf0;" k="6" />
    <hkern u1="&#xdc;" u2="&#xeb;" k="6" />
    <hkern u1="&#xdc;" u2="&#xea;" k="6" />
    <hkern u1="&#xdc;" u2="&#xe9;" k="6" />
    <hkern u1="&#xdc;" u2="&#xe8;" k="6" />
    <hkern u1="&#xdc;" u2="&#xe6;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe5;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe4;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe3;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe2;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe1;" k="10" />
    <hkern u1="&#xdc;" u2="&#xe0;" k="10" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="14" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="14" />
    <hkern u1="&#xdc;" u2="x" k="14" />
    <hkern u1="&#xdc;" u2="q" k="6" />
    <hkern u1="&#xdc;" u2="g" k="6" />
    <hkern u1="&#xdc;" u2="e" k="6" />
    <hkern u1="&#xdc;" u2="d" k="6" />
    <hkern u1="&#xdc;" u2="a" k="10" />
    <hkern u1="&#xdc;" u2="X" k="10" />
    <hkern u1="&#xdc;" u2="A" k="14" />
    <hkern u1="&#xdd;" g2="fl" k="10" />
    <hkern u1="&#xdd;" g2="fi" k="10" />
    <hkern u1="&#xdd;" u2="&#x17e;" k="20" />
    <hkern u1="&#xdd;" u2="&#x178;" k="-10" />
    <hkern u1="&#xdd;" u2="&#x161;" k="31" />
    <hkern u1="&#xdd;" u2="&#x160;" k="10" />
    <hkern u1="&#xdd;" u2="&#x153;" k="61" />
    <hkern u1="&#xdd;" u2="&#x152;" k="24" />
    <hkern u1="&#xdd;" u2="&#xff;" k="20" />
    <hkern u1="&#xdd;" u2="&#xfd;" k="20" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="20" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="20" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="20" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="20" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="61" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="61" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="61" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="61" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="61" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="61" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="61" />
    <hkern u1="&#xdd;" u2="&#xea;" k="61" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="61" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="61" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="61" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="51" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="51" />
    <hkern u1="&#xdd;" u2="&#xdd;" k="-10" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="24" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="24" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="24" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="24" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="24" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="24" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="14" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="100" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="100" />
    <hkern u1="&#xdd;" u2="z" k="20" />
    <hkern u1="&#xdd;" u2="y" k="20" />
    <hkern u1="&#xdd;" u2="x" k="41" />
    <hkern u1="&#xdd;" u2="w" k="10" />
    <hkern u1="&#xdd;" u2="v" k="20" />
    <hkern u1="&#xdd;" u2="u" k="20" />
    <hkern u1="&#xdd;" u2="t" k="10" />
    <hkern u1="&#xdd;" u2="s" k="31" />
    <hkern u1="&#xdd;" u2="q" k="61" />
    <hkern u1="&#xdd;" u2="o" k="61" />
    <hkern u1="&#xdd;" u2="g" k="61" />
    <hkern u1="&#xdd;" u2="f" k="10" />
    <hkern u1="&#xdd;" u2="e" k="61" />
    <hkern u1="&#xdd;" u2="d" k="61" />
    <hkern u1="&#xdd;" u2="c" k="61" />
    <hkern u1="&#xdd;" u2="a" k="51" />
    <hkern u1="&#xdd;" u2="Y" k="-10" />
    <hkern u1="&#xdd;" u2="X" k="15" />
    <hkern u1="&#xdd;" u2="T" k="-10" />
    <hkern u1="&#xdd;" u2="S" k="10" />
    <hkern u1="&#xdd;" u2="Q" k="24" />
    <hkern u1="&#xdd;" u2="O" k="24" />
    <hkern u1="&#xdd;" u2="J" k="73" />
    <hkern u1="&#xdd;" u2="G" k="14" />
    <hkern u1="&#xdd;" u2="C" k="14" />
    <hkern u1="&#xdd;" u2="A" k="100" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="27" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="27" />
    <hkern u1="&#xe6;" u2="&#x17e;" k="10" />
    <hkern u1="&#xe6;" u2="&#x17d;" k="10" />
    <hkern u1="&#xe6;" u2="&#x178;" k="61" />
    <hkern u1="&#xe6;" u2="&#x153;" k="10" />
    <hkern u1="&#xe6;" u2="&#xf6;" k="10" />
    <hkern u1="&#xe6;" u2="&#xf5;" k="10" />
    <hkern u1="&#xe6;" u2="&#xf4;" k="10" />
    <hkern u1="&#xe6;" u2="&#xf3;" k="10" />
    <hkern u1="&#xe6;" u2="&#xf2;" k="10" />
    <hkern u1="&#xe6;" u2="&#xe6;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe5;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe4;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe3;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe2;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe1;" k="6" />
    <hkern u1="&#xe6;" u2="&#xe0;" k="6" />
    <hkern u1="&#xe6;" u2="&#xdd;" k="61" />
    <hkern u1="&#xe6;" u2="&#xdc;" k="6" />
    <hkern u1="&#xe6;" u2="&#xdb;" k="6" />
    <hkern u1="&#xe6;" u2="&#xda;" k="6" />
    <hkern u1="&#xe6;" u2="&#xd9;" k="6" />
    <hkern u1="&#xe6;" u2="z" k="10" />
    <hkern u1="&#xe6;" u2="x" k="12" />
    <hkern u1="&#xe6;" u2="v" k="10" />
    <hkern u1="&#xe6;" u2="o" k="10" />
    <hkern u1="&#xe6;" u2="a" k="6" />
    <hkern u1="&#xe6;" u2="Z" k="10" />
    <hkern u1="&#xe6;" u2="Y" k="61" />
    <hkern u1="&#xe6;" u2="X" k="20" />
    <hkern u1="&#xe6;" u2="W" k="10" />
    <hkern u1="&#xe6;" u2="V" k="20" />
    <hkern u1="&#xe6;" u2="U" k="6" />
    <hkern u1="&#xe6;" u2="T" k="20" />
    <hkern u1="&#xe6;" u2="&#x27;" k="20" />
    <hkern u1="&#xe6;" u2="&#x22;" k="20" />
    <hkern u1="&#xe7;" u2="&#x17e;" k="10" />
    <hkern u1="&#xe7;" u2="&#x178;" k="41" />
    <hkern u1="&#xe7;" u2="&#x153;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf6;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf5;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf4;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf3;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf2;" k="27" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="24" />
    <hkern u1="&#xe7;" u2="&#xe7;" k="20" />
    <hkern u1="&#xe7;" u2="&#xe6;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe5;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe4;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe3;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe2;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe1;" k="6" />
    <hkern u1="&#xe7;" u2="&#xe0;" k="6" />
    <hkern u1="&#xe7;" u2="&#xdd;" k="41" />
    <hkern u1="&#xe7;" u2="&#xdc;" k="14" />
    <hkern u1="&#xe7;" u2="&#xdb;" k="14" />
    <hkern u1="&#xe7;" u2="&#xda;" k="14" />
    <hkern u1="&#xe7;" u2="&#xd9;" k="14" />
    <hkern u1="&#xe7;" u2="&#xc7;" k="10" />
    <hkern u1="&#xe7;" u2="z" k="10" />
    <hkern u1="&#xe7;" u2="v" k="5" />
    <hkern u1="&#xe7;" u2="q" k="24" />
    <hkern u1="&#xe7;" u2="o" k="27" />
    <hkern u1="&#xe7;" u2="g" k="24" />
    <hkern u1="&#xe7;" u2="d" k="24" />
    <hkern u1="&#xe7;" u2="c" k="20" />
    <hkern u1="&#xe7;" u2="a" k="6" />
    <hkern u1="&#xe7;" u2="Y" k="41" />
    <hkern u1="&#xe7;" u2="X" k="20" />
    <hkern u1="&#xe7;" u2="W" k="10" />
    <hkern u1="&#xe7;" u2="V" k="10" />
    <hkern u1="&#xe7;" u2="U" k="14" />
    <hkern u1="&#xe7;" u2="T" k="20" />
    <hkern u1="&#xe7;" u2="C" k="10" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="27" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="27" />
    <hkern u1="&#xe8;" u2="&#x17e;" k="10" />
    <hkern u1="&#xe8;" u2="&#x17d;" k="10" />
    <hkern u1="&#xe8;" u2="&#x178;" k="61" />
    <hkern u1="&#xe8;" u2="&#x153;" k="10" />
    <hkern u1="&#xe8;" u2="&#xf6;" k="10" />
    <hkern u1="&#xe8;" u2="&#xf5;" k="10" />
    <hkern u1="&#xe8;" u2="&#xf4;" k="10" />
    <hkern u1="&#xe8;" u2="&#xf3;" k="10" />
    <hkern u1="&#xe8;" u2="&#xf2;" k="10" />
    <hkern u1="&#xe8;" u2="&#xe6;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe5;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe4;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe3;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe2;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe1;" k="6" />
    <hkern u1="&#xe8;" u2="&#xe0;" k="6" />
    <hkern u1="&#xe8;" u2="&#xdd;" k="61" />
    <hkern u1="&#xe8;" u2="&#xdc;" k="6" />
    <hkern u1="&#xe8;" u2="&#xdb;" k="6" />
    <hkern u1="&#xe8;" u2="&#xda;" k="6" />
    <hkern u1="&#xe8;" u2="&#xd9;" k="6" />
    <hkern u1="&#xe8;" u2="z" k="10" />
    <hkern u1="&#xe8;" u2="x" k="12" />
    <hkern u1="&#xe8;" u2="v" k="10" />
    <hkern u1="&#xe8;" u2="o" k="10" />
    <hkern u1="&#xe8;" u2="a" k="6" />
    <hkern u1="&#xe8;" u2="Z" k="10" />
    <hkern u1="&#xe8;" u2="Y" k="61" />
    <hkern u1="&#xe8;" u2="X" k="20" />
    <hkern u1="&#xe8;" u2="W" k="10" />
    <hkern u1="&#xe8;" u2="V" k="20" />
    <hkern u1="&#xe8;" u2="U" k="6" />
    <hkern u1="&#xe8;" u2="T" k="20" />
    <hkern u1="&#xe8;" u2="&#x27;" k="20" />
    <hkern u1="&#xe8;" u2="&#x22;" k="20" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="27" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="27" />
    <hkern u1="&#xe9;" u2="&#x17e;" k="10" />
    <hkern u1="&#xe9;" u2="&#x17d;" k="10" />
    <hkern u1="&#xe9;" u2="&#x178;" k="61" />
    <hkern u1="&#xe9;" u2="&#x153;" k="10" />
    <hkern u1="&#xe9;" u2="&#xf6;" k="10" />
    <hkern u1="&#xe9;" u2="&#xf5;" k="10" />
    <hkern u1="&#xe9;" u2="&#xf4;" k="10" />
    <hkern u1="&#xe9;" u2="&#xf3;" k="10" />
    <hkern u1="&#xe9;" u2="&#xf2;" k="10" />
    <hkern u1="&#xe9;" u2="&#xe6;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe5;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe4;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe3;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe2;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe1;" k="6" />
    <hkern u1="&#xe9;" u2="&#xe0;" k="6" />
    <hkern u1="&#xe9;" u2="&#xdd;" k="61" />
    <hkern u1="&#xe9;" u2="&#xdc;" k="6" />
    <hkern u1="&#xe9;" u2="&#xdb;" k="6" />
    <hkern u1="&#xe9;" u2="&#xda;" k="6" />
    <hkern u1="&#xe9;" u2="&#xd9;" k="6" />
    <hkern u1="&#xe9;" u2="z" k="10" />
    <hkern u1="&#xe9;" u2="x" k="12" />
    <hkern u1="&#xe9;" u2="v" k="10" />
    <hkern u1="&#xe9;" u2="o" k="10" />
    <hkern u1="&#xe9;" u2="a" k="6" />
    <hkern u1="&#xe9;" u2="Z" k="10" />
    <hkern u1="&#xe9;" u2="Y" k="61" />
    <hkern u1="&#xe9;" u2="X" k="20" />
    <hkern u1="&#xe9;" u2="W" k="10" />
    <hkern u1="&#xe9;" u2="V" k="20" />
    <hkern u1="&#xe9;" u2="U" k="6" />
    <hkern u1="&#xe9;" u2="T" k="20" />
    <hkern u1="&#xe9;" u2="&#x27;" k="20" />
    <hkern u1="&#xe9;" u2="&#x22;" k="20" />
    <hkern u1="&#xea;" u2="&#x201d;" k="27" />
    <hkern u1="&#xea;" u2="&#x2019;" k="27" />
    <hkern u1="&#xea;" u2="&#x17e;" k="10" />
    <hkern u1="&#xea;" u2="&#x17d;" k="10" />
    <hkern u1="&#xea;" u2="&#x178;" k="61" />
    <hkern u1="&#xea;" u2="&#x153;" k="10" />
    <hkern u1="&#xea;" u2="&#xf6;" k="10" />
    <hkern u1="&#xea;" u2="&#xf5;" k="10" />
    <hkern u1="&#xea;" u2="&#xf4;" k="10" />
    <hkern u1="&#xea;" u2="&#xf3;" k="10" />
    <hkern u1="&#xea;" u2="&#xf2;" k="10" />
    <hkern u1="&#xea;" u2="&#xe6;" k="6" />
    <hkern u1="&#xea;" u2="&#xe5;" k="6" />
    <hkern u1="&#xea;" u2="&#xe4;" k="6" />
    <hkern u1="&#xea;" u2="&#xe3;" k="6" />
    <hkern u1="&#xea;" u2="&#xe2;" k="6" />
    <hkern u1="&#xea;" u2="&#xe1;" k="6" />
    <hkern u1="&#xea;" u2="&#xe0;" k="6" />
    <hkern u1="&#xea;" u2="&#xdd;" k="61" />
    <hkern u1="&#xea;" u2="&#xdc;" k="6" />
    <hkern u1="&#xea;" u2="&#xdb;" k="6" />
    <hkern u1="&#xea;" u2="&#xda;" k="6" />
    <hkern u1="&#xea;" u2="&#xd9;" k="6" />
    <hkern u1="&#xea;" u2="z" k="10" />
    <hkern u1="&#xea;" u2="x" k="12" />
    <hkern u1="&#xea;" u2="v" k="10" />
    <hkern u1="&#xea;" u2="o" k="10" />
    <hkern u1="&#xea;" u2="a" k="6" />
    <hkern u1="&#xea;" u2="Z" k="10" />
    <hkern u1="&#xea;" u2="Y" k="61" />
    <hkern u1="&#xea;" u2="X" k="20" />
    <hkern u1="&#xea;" u2="W" k="10" />
    <hkern u1="&#xea;" u2="V" k="20" />
    <hkern u1="&#xea;" u2="U" k="6" />
    <hkern u1="&#xea;" u2="T" k="20" />
    <hkern u1="&#xea;" u2="&#x27;" k="20" />
    <hkern u1="&#xea;" u2="&#x22;" k="20" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="27" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="27" />
    <hkern u1="&#xeb;" u2="&#x17e;" k="10" />
    <hkern u1="&#xeb;" u2="&#x17d;" k="10" />
    <hkern u1="&#xeb;" u2="&#x178;" k="61" />
    <hkern u1="&#xeb;" u2="&#x153;" k="10" />
    <hkern u1="&#xeb;" u2="&#xf6;" k="10" />
    <hkern u1="&#xeb;" u2="&#xf5;" k="10" />
    <hkern u1="&#xeb;" u2="&#xf4;" k="10" />
    <hkern u1="&#xeb;" u2="&#xf3;" k="10" />
    <hkern u1="&#xeb;" u2="&#xf2;" k="10" />
    <hkern u1="&#xeb;" u2="&#xe6;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe5;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe4;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe3;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe2;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe1;" k="6" />
    <hkern u1="&#xeb;" u2="&#xe0;" k="6" />
    <hkern u1="&#xeb;" u2="&#xdd;" k="61" />
    <hkern u1="&#xeb;" u2="&#xdc;" k="6" />
    <hkern u1="&#xeb;" u2="&#xdb;" k="6" />
    <hkern u1="&#xeb;" u2="&#xda;" k="6" />
    <hkern u1="&#xeb;" u2="&#xd9;" k="6" />
    <hkern u1="&#xeb;" u2="z" k="10" />
    <hkern u1="&#xeb;" u2="x" k="12" />
    <hkern u1="&#xeb;" u2="v" k="10" />
    <hkern u1="&#xeb;" u2="o" k="10" />
    <hkern u1="&#xeb;" u2="a" k="6" />
    <hkern u1="&#xeb;" u2="Z" k="10" />
    <hkern u1="&#xeb;" u2="Y" k="61" />
    <hkern u1="&#xeb;" u2="X" k="20" />
    <hkern u1="&#xeb;" u2="W" k="10" />
    <hkern u1="&#xeb;" u2="V" k="20" />
    <hkern u1="&#xeb;" u2="U" k="6" />
    <hkern u1="&#xeb;" u2="T" k="20" />
    <hkern u1="&#xeb;" u2="&#x27;" k="20" />
    <hkern u1="&#xeb;" u2="&#x22;" k="20" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="31" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="31" />
    <hkern u1="&#xf2;" u2="&#x17e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x17d;" k="10" />
    <hkern u1="&#xf2;" u2="&#x178;" k="61" />
    <hkern u1="&#xf2;" u2="&#x160;" k="20" />
    <hkern u1="&#xf2;" u2="&#xe6;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe5;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe4;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe3;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe2;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe1;" k="6" />
    <hkern u1="&#xf2;" u2="&#xe0;" k="6" />
    <hkern u1="&#xf2;" u2="&#xdd;" k="61" />
    <hkern u1="&#xf2;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc5;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc4;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc3;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc2;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc1;" k="10" />
    <hkern u1="&#xf2;" u2="&#xc0;" k="10" />
    <hkern u1="&#xf2;" u2="z" k="10" />
    <hkern u1="&#xf2;" u2="x" k="18" />
    <hkern u1="&#xf2;" u2="v" k="10" />
    <hkern u1="&#xf2;" u2="a" k="6" />
    <hkern u1="&#xf2;" u2="Z" k="10" />
    <hkern u1="&#xf2;" u2="Y" k="61" />
    <hkern u1="&#xf2;" u2="X" k="26" />
    <hkern u1="&#xf2;" u2="W" k="10" />
    <hkern u1="&#xf2;" u2="V" k="31" />
    <hkern u1="&#xf2;" u2="T" k="26" />
    <hkern u1="&#xf2;" u2="S" k="20" />
    <hkern u1="&#xf2;" u2="J" k="16" />
    <hkern u1="&#xf2;" u2="A" k="10" />
    <hkern u1="&#xf2;" u2="&#x27;" k="20" />
    <hkern u1="&#xf2;" u2="&#x22;" k="20" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="31" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="31" />
    <hkern u1="&#xf3;" u2="&#x17e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x17d;" k="10" />
    <hkern u1="&#xf3;" u2="&#x178;" k="61" />
    <hkern u1="&#xf3;" u2="&#x160;" k="20" />
    <hkern u1="&#xf3;" u2="&#xe6;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe5;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe4;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe3;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe2;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe1;" k="6" />
    <hkern u1="&#xf3;" u2="&#xe0;" k="6" />
    <hkern u1="&#xf3;" u2="&#xdd;" k="61" />
    <hkern u1="&#xf3;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc5;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc4;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc3;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc2;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc1;" k="10" />
    <hkern u1="&#xf3;" u2="&#xc0;" k="10" />
    <hkern u1="&#xf3;" u2="z" k="10" />
    <hkern u1="&#xf3;" u2="x" k="18" />
    <hkern u1="&#xf3;" u2="v" k="10" />
    <hkern u1="&#xf3;" u2="a" k="6" />
    <hkern u1="&#xf3;" u2="Z" k="10" />
    <hkern u1="&#xf3;" u2="Y" k="61" />
    <hkern u1="&#xf3;" u2="X" k="26" />
    <hkern u1="&#xf3;" u2="W" k="10" />
    <hkern u1="&#xf3;" u2="V" k="31" />
    <hkern u1="&#xf3;" u2="T" k="26" />
    <hkern u1="&#xf3;" u2="S" k="20" />
    <hkern u1="&#xf3;" u2="J" k="16" />
    <hkern u1="&#xf3;" u2="A" k="10" />
    <hkern u1="&#xf3;" u2="&#x27;" k="20" />
    <hkern u1="&#xf3;" u2="&#x22;" k="20" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="31" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="31" />
    <hkern u1="&#xf4;" u2="&#x17e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x17d;" k="10" />
    <hkern u1="&#xf4;" u2="&#x178;" k="61" />
    <hkern u1="&#xf4;" u2="&#x160;" k="20" />
    <hkern u1="&#xf4;" u2="&#xe6;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe5;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe4;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe3;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe2;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe1;" k="6" />
    <hkern u1="&#xf4;" u2="&#xe0;" k="6" />
    <hkern u1="&#xf4;" u2="&#xdd;" k="61" />
    <hkern u1="&#xf4;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc5;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc4;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc3;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc2;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc1;" k="10" />
    <hkern u1="&#xf4;" u2="&#xc0;" k="10" />
    <hkern u1="&#xf4;" u2="z" k="10" />
    <hkern u1="&#xf4;" u2="x" k="18" />
    <hkern u1="&#xf4;" u2="v" k="10" />
    <hkern u1="&#xf4;" u2="a" k="6" />
    <hkern u1="&#xf4;" u2="Z" k="10" />
    <hkern u1="&#xf4;" u2="Y" k="61" />
    <hkern u1="&#xf4;" u2="X" k="26" />
    <hkern u1="&#xf4;" u2="W" k="10" />
    <hkern u1="&#xf4;" u2="V" k="31" />
    <hkern u1="&#xf4;" u2="T" k="26" />
    <hkern u1="&#xf4;" u2="S" k="20" />
    <hkern u1="&#xf4;" u2="J" k="16" />
    <hkern u1="&#xf4;" u2="A" k="10" />
    <hkern u1="&#xf4;" u2="&#x27;" k="20" />
    <hkern u1="&#xf4;" u2="&#x22;" k="20" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="31" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="31" />
    <hkern u1="&#xf5;" u2="&#x17e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x17d;" k="10" />
    <hkern u1="&#xf5;" u2="&#x178;" k="61" />
    <hkern u1="&#xf5;" u2="&#x160;" k="20" />
    <hkern u1="&#xf5;" u2="&#xe6;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe5;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe4;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe3;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe2;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe1;" k="6" />
    <hkern u1="&#xf5;" u2="&#xe0;" k="6" />
    <hkern u1="&#xf5;" u2="&#xdd;" k="61" />
    <hkern u1="&#xf5;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc5;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc4;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc3;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc2;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc1;" k="10" />
    <hkern u1="&#xf5;" u2="&#xc0;" k="10" />
    <hkern u1="&#xf5;" u2="z" k="10" />
    <hkern u1="&#xf5;" u2="x" k="18" />
    <hkern u1="&#xf5;" u2="v" k="10" />
    <hkern u1="&#xf5;" u2="a" k="6" />
    <hkern u1="&#xf5;" u2="Z" k="10" />
    <hkern u1="&#xf5;" u2="Y" k="61" />
    <hkern u1="&#xf5;" u2="X" k="26" />
    <hkern u1="&#xf5;" u2="W" k="10" />
    <hkern u1="&#xf5;" u2="V" k="31" />
    <hkern u1="&#xf5;" u2="T" k="26" />
    <hkern u1="&#xf5;" u2="S" k="20" />
    <hkern u1="&#xf5;" u2="J" k="16" />
    <hkern u1="&#xf5;" u2="A" k="10" />
    <hkern u1="&#xf5;" u2="&#x27;" k="20" />
    <hkern u1="&#xf5;" u2="&#x22;" k="20" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="31" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="31" />
    <hkern u1="&#xf6;" u2="&#x17e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x17d;" k="10" />
    <hkern u1="&#xf6;" u2="&#x178;" k="61" />
    <hkern u1="&#xf6;" u2="&#x160;" k="20" />
    <hkern u1="&#xf6;" u2="&#xe6;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe5;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe4;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe3;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe2;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe1;" k="6" />
    <hkern u1="&#xf6;" u2="&#xe0;" k="6" />
    <hkern u1="&#xf6;" u2="&#xdd;" k="61" />
    <hkern u1="&#xf6;" u2="&#xc6;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc5;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc4;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc3;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc2;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc1;" k="10" />
    <hkern u1="&#xf6;" u2="&#xc0;" k="10" />
    <hkern u1="&#xf6;" u2="z" k="10" />
    <hkern u1="&#xf6;" u2="x" k="18" />
    <hkern u1="&#xf6;" u2="v" k="10" />
    <hkern u1="&#xf6;" u2="a" k="6" />
    <hkern u1="&#xf6;" u2="Z" k="10" />
    <hkern u1="&#xf6;" u2="Y" k="61" />
    <hkern u1="&#xf6;" u2="X" k="26" />
    <hkern u1="&#xf6;" u2="W" k="10" />
    <hkern u1="&#xf6;" u2="V" k="31" />
    <hkern u1="&#xf6;" u2="T" k="26" />
    <hkern u1="&#xf6;" u2="S" k="20" />
    <hkern u1="&#xf6;" u2="J" k="16" />
    <hkern u1="&#xf6;" u2="A" k="10" />
    <hkern u1="&#xf6;" u2="&#x27;" k="20" />
    <hkern u1="&#xf6;" u2="&#x22;" k="20" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="31" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="31" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="10" />
    <hkern u1="&#xfe;" u2="&#x178;" k="61" />
    <hkern u1="&#xfe;" u2="&#x160;" k="10" />
    <hkern u1="&#xfe;" u2="&#xe6;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe5;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe4;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe3;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe2;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe1;" k="6" />
    <hkern u1="&#xfe;" u2="&#xe0;" k="6" />
    <hkern u1="&#xfe;" u2="&#xdd;" k="61" />
    <hkern u1="&#xfe;" u2="&#xdc;" k="6" />
    <hkern u1="&#xfe;" u2="&#xdb;" k="6" />
    <hkern u1="&#xfe;" u2="&#xda;" k="6" />
    <hkern u1="&#xfe;" u2="&#xd9;" k="6" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc5;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc4;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc3;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc2;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc1;" k="10" />
    <hkern u1="&#xfe;" u2="&#xc0;" k="10" />
    <hkern u1="&#xfe;" u2="z" k="10" />
    <hkern u1="&#xfe;" u2="x" k="16" />
    <hkern u1="&#xfe;" u2="v" k="10" />
    <hkern u1="&#xfe;" u2="a" k="6" />
    <hkern u1="&#xfe;" u2="Y" k="61" />
    <hkern u1="&#xfe;" u2="X" k="20" />
    <hkern u1="&#xfe;" u2="W" k="10" />
    <hkern u1="&#xfe;" u2="V" k="20" />
    <hkern u1="&#xfe;" u2="U" k="6" />
    <hkern u1="&#xfe;" u2="T" k="26" />
    <hkern u1="&#xfe;" u2="S" k="10" />
    <hkern u1="&#xfe;" u2="J" k="10" />
    <hkern u1="&#xfe;" u2="A" k="10" />
    <hkern u1="&#xfe;" u2="&#x27;" k="20" />
    <hkern u1="&#xfe;" u2="&#x22;" k="20" />
    <hkern u1="&#x141;" u2="&#x201d;" k="82" />
    <hkern u1="&#x141;" u2="&#x2019;" k="82" />
    <hkern u1="&#x141;" u2="&#x178;" k="51" />
    <hkern u1="&#x141;" u2="&#x153;" k="10" />
    <hkern u1="&#x141;" u2="&#x152;" k="22" />
    <hkern u1="&#x141;" u2="&#xff;" k="20" />
    <hkern u1="&#x141;" u2="&#xfd;" k="20" />
    <hkern u1="&#x141;" u2="&#xfc;" k="20" />
    <hkern u1="&#x141;" u2="&#xfb;" k="20" />
    <hkern u1="&#x141;" u2="&#xfa;" k="20" />
    <hkern u1="&#x141;" u2="&#xf9;" k="20" />
    <hkern u1="&#x141;" u2="&#xf6;" k="10" />
    <hkern u1="&#x141;" u2="&#xf5;" k="10" />
    <hkern u1="&#x141;" u2="&#xf4;" k="10" />
    <hkern u1="&#x141;" u2="&#xf3;" k="10" />
    <hkern u1="&#x141;" u2="&#xf2;" k="10" />
    <hkern u1="&#x141;" u2="&#xf0;" k="10" />
    <hkern u1="&#x141;" u2="&#xeb;" k="10" />
    <hkern u1="&#x141;" u2="&#xea;" k="10" />
    <hkern u1="&#x141;" u2="&#xe9;" k="10" />
    <hkern u1="&#x141;" u2="&#xe8;" k="10" />
    <hkern u1="&#x141;" u2="&#xe7;" k="10" />
    <hkern u1="&#x141;" u2="&#xdd;" k="51" />
    <hkern u1="&#x141;" u2="&#xd8;" k="22" />
    <hkern u1="&#x141;" u2="&#xd6;" k="22" />
    <hkern u1="&#x141;" u2="&#xd5;" k="22" />
    <hkern u1="&#x141;" u2="&#xd4;" k="22" />
    <hkern u1="&#x141;" u2="&#xd3;" k="22" />
    <hkern u1="&#x141;" u2="&#xd2;" k="22" />
    <hkern u1="&#x141;" u2="&#xc7;" k="20" />
    <hkern u1="&#x141;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x141;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x141;" u2="y" k="20" />
    <hkern u1="&#x141;" u2="w" k="14" />
    <hkern u1="&#x141;" u2="v" k="37" />
    <hkern u1="&#x141;" u2="u" k="20" />
    <hkern u1="&#x141;" u2="t" k="31" />
    <hkern u1="&#x141;" u2="q" k="10" />
    <hkern u1="&#x141;" u2="o" k="10" />
    <hkern u1="&#x141;" u2="g" k="10" />
    <hkern u1="&#x141;" u2="e" k="10" />
    <hkern u1="&#x141;" u2="d" k="10" />
    <hkern u1="&#x141;" u2="c" k="10" />
    <hkern u1="&#x141;" u2="Y" k="51" />
    <hkern u1="&#x141;" u2="X" k="10" />
    <hkern u1="&#x141;" u2="W" k="31" />
    <hkern u1="&#x141;" u2="V" k="47" />
    <hkern u1="&#x141;" u2="T" k="61" />
    <hkern u1="&#x141;" u2="Q" k="22" />
    <hkern u1="&#x141;" u2="O" k="22" />
    <hkern u1="&#x141;" u2="J" k="-10" />
    <hkern u1="&#x141;" u2="G" k="20" />
    <hkern u1="&#x141;" u2="C" k="20" />
    <hkern u1="&#x141;" u2="A" k="-10" />
    <hkern u1="&#x141;" u2="&#x27;" k="61" />
    <hkern u1="&#x141;" u2="&#x22;" k="61" />
    <hkern u1="&#x152;" u2="&#x153;" k="10" />
    <hkern u1="&#x152;" u2="&#x152;" k="10" />
    <hkern u1="&#x152;" u2="&#xff;" k="10" />
    <hkern u1="&#x152;" u2="&#xfd;" k="10" />
    <hkern u1="&#x152;" u2="&#xfc;" k="10" />
    <hkern u1="&#x152;" u2="&#xfb;" k="10" />
    <hkern u1="&#x152;" u2="&#xfa;" k="10" />
    <hkern u1="&#x152;" u2="&#xf9;" k="10" />
    <hkern u1="&#x152;" u2="&#xf6;" k="10" />
    <hkern u1="&#x152;" u2="&#xf5;" k="10" />
    <hkern u1="&#x152;" u2="&#xf4;" k="10" />
    <hkern u1="&#x152;" u2="&#xf3;" k="10" />
    <hkern u1="&#x152;" u2="&#xf2;" k="10" />
    <hkern u1="&#x152;" u2="&#xf0;" k="10" />
    <hkern u1="&#x152;" u2="&#xeb;" k="10" />
    <hkern u1="&#x152;" u2="&#xea;" k="10" />
    <hkern u1="&#x152;" u2="&#xe9;" k="10" />
    <hkern u1="&#x152;" u2="&#xe8;" k="10" />
    <hkern u1="&#x152;" u2="&#xe7;" k="10" />
    <hkern u1="&#x152;" u2="&#xe6;" k="10" />
    <hkern u1="&#x152;" u2="&#xe5;" k="10" />
    <hkern u1="&#x152;" u2="&#xe4;" k="10" />
    <hkern u1="&#x152;" u2="&#xe3;" k="10" />
    <hkern u1="&#x152;" u2="&#xe2;" k="10" />
    <hkern u1="&#x152;" u2="&#xe1;" k="10" />
    <hkern u1="&#x152;" u2="&#xe0;" k="10" />
    <hkern u1="&#x152;" u2="&#xd8;" k="10" />
    <hkern u1="&#x152;" u2="&#xd6;" k="10" />
    <hkern u1="&#x152;" u2="&#xd5;" k="10" />
    <hkern u1="&#x152;" u2="&#xd4;" k="10" />
    <hkern u1="&#x152;" u2="&#xd3;" k="10" />
    <hkern u1="&#x152;" u2="&#xd2;" k="10" />
    <hkern u1="&#x152;" u2="y" k="10" />
    <hkern u1="&#x152;" u2="x" k="10" />
    <hkern u1="&#x152;" u2="v" k="10" />
    <hkern u1="&#x152;" u2="u" k="10" />
    <hkern u1="&#x152;" u2="q" k="10" />
    <hkern u1="&#x152;" u2="o" k="10" />
    <hkern u1="&#x152;" u2="g" k="10" />
    <hkern u1="&#x152;" u2="e" k="10" />
    <hkern u1="&#x152;" u2="d" k="10" />
    <hkern u1="&#x152;" u2="c" k="10" />
    <hkern u1="&#x152;" u2="a" k="10" />
    <hkern u1="&#x152;" u2="X" k="10" />
    <hkern u1="&#x152;" u2="Q" k="10" />
    <hkern u1="&#x152;" u2="O" k="10" />
    <hkern u1="&#x152;" u2="G" k="10" />
    <hkern u1="&#x153;" u2="&#x201d;" k="27" />
    <hkern u1="&#x153;" u2="&#x2019;" k="27" />
    <hkern u1="&#x153;" u2="&#x17e;" k="10" />
    <hkern u1="&#x153;" u2="&#x17d;" k="10" />
    <hkern u1="&#x153;" u2="&#x178;" k="61" />
    <hkern u1="&#x153;" u2="&#x153;" k="10" />
    <hkern u1="&#x153;" u2="&#xf6;" k="10" />
    <hkern u1="&#x153;" u2="&#xf5;" k="10" />
    <hkern u1="&#x153;" u2="&#xf4;" k="10" />
    <hkern u1="&#x153;" u2="&#xf3;" k="10" />
    <hkern u1="&#x153;" u2="&#xf2;" k="10" />
    <hkern u1="&#x153;" u2="&#xe6;" k="6" />
    <hkern u1="&#x153;" u2="&#xe5;" k="6" />
    <hkern u1="&#x153;" u2="&#xe4;" k="6" />
    <hkern u1="&#x153;" u2="&#xe3;" k="6" />
    <hkern u1="&#x153;" u2="&#xe2;" k="6" />
    <hkern u1="&#x153;" u2="&#xe1;" k="6" />
    <hkern u1="&#x153;" u2="&#xe0;" k="6" />
    <hkern u1="&#x153;" u2="&#xdd;" k="61" />
    <hkern u1="&#x153;" u2="&#xdc;" k="6" />
    <hkern u1="&#x153;" u2="&#xdb;" k="6" />
    <hkern u1="&#x153;" u2="&#xda;" k="6" />
    <hkern u1="&#x153;" u2="&#xd9;" k="6" />
    <hkern u1="&#x153;" u2="z" k="10" />
    <hkern u1="&#x153;" u2="x" k="12" />
    <hkern u1="&#x153;" u2="v" k="10" />
    <hkern u1="&#x153;" u2="o" k="10" />
    <hkern u1="&#x153;" u2="a" k="6" />
    <hkern u1="&#x153;" u2="Z" k="10" />
    <hkern u1="&#x153;" u2="Y" k="61" />
    <hkern u1="&#x153;" u2="X" k="20" />
    <hkern u1="&#x153;" u2="W" k="10" />
    <hkern u1="&#x153;" u2="V" k="20" />
    <hkern u1="&#x153;" u2="U" k="6" />
    <hkern u1="&#x153;" u2="T" k="20" />
    <hkern u1="&#x153;" u2="&#x27;" k="20" />
    <hkern u1="&#x153;" u2="&#x22;" k="20" />
    <hkern u1="&#x160;" u2="&#x17e;" k="10" />
    <hkern u1="&#x160;" u2="&#x178;" k="16" />
    <hkern u1="&#x160;" u2="&#x152;" k="10" />
    <hkern u1="&#x160;" u2="&#xf0;" k="10" />
    <hkern u1="&#x160;" u2="&#xeb;" k="10" />
    <hkern u1="&#x160;" u2="&#xea;" k="10" />
    <hkern u1="&#x160;" u2="&#xe9;" k="10" />
    <hkern u1="&#x160;" u2="&#xe8;" k="10" />
    <hkern u1="&#x160;" u2="&#xe6;" k="10" />
    <hkern u1="&#x160;" u2="&#xe5;" k="10" />
    <hkern u1="&#x160;" u2="&#xe4;" k="10" />
    <hkern u1="&#x160;" u2="&#xe3;" k="10" />
    <hkern u1="&#x160;" u2="&#xe2;" k="10" />
    <hkern u1="&#x160;" u2="&#xe1;" k="10" />
    <hkern u1="&#x160;" u2="&#xe0;" k="10" />
    <hkern u1="&#x160;" u2="&#xdd;" k="16" />
    <hkern u1="&#x160;" u2="&#xd8;" k="10" />
    <hkern u1="&#x160;" u2="&#xd6;" k="10" />
    <hkern u1="&#x160;" u2="&#xd5;" k="10" />
    <hkern u1="&#x160;" u2="&#xd4;" k="10" />
    <hkern u1="&#x160;" u2="&#xd3;" k="10" />
    <hkern u1="&#x160;" u2="&#xd2;" k="10" />
    <hkern u1="&#x160;" u2="&#xc7;" k="10" />
    <hkern u1="&#x160;" u2="&#xc6;" k="10" />
    <hkern u1="&#x160;" u2="&#xc5;" k="10" />
    <hkern u1="&#x160;" u2="&#xc4;" k="10" />
    <hkern u1="&#x160;" u2="&#xc3;" k="10" />
    <hkern u1="&#x160;" u2="&#xc2;" k="10" />
    <hkern u1="&#x160;" u2="&#xc1;" k="10" />
    <hkern u1="&#x160;" u2="&#xc0;" k="10" />
    <hkern u1="&#x160;" u2="z" k="10" />
    <hkern u1="&#x160;" u2="x" k="20" />
    <hkern u1="&#x160;" u2="w" k="6" />
    <hkern u1="&#x160;" u2="v" k="24" />
    <hkern u1="&#x160;" u2="q" k="10" />
    <hkern u1="&#x160;" u2="g" k="10" />
    <hkern u1="&#x160;" u2="e" k="10" />
    <hkern u1="&#x160;" u2="d" k="10" />
    <hkern u1="&#x160;" u2="a" k="10" />
    <hkern u1="&#x160;" u2="Y" k="16" />
    <hkern u1="&#x160;" u2="X" k="12" />
    <hkern u1="&#x160;" u2="W" k="10" />
    <hkern u1="&#x160;" u2="V" k="16" />
    <hkern u1="&#x160;" u2="Q" k="10" />
    <hkern u1="&#x160;" u2="O" k="10" />
    <hkern u1="&#x160;" u2="C" k="10" />
    <hkern u1="&#x160;" u2="A" k="10" />
    <hkern u1="&#x161;" u2="&#x201d;" k="10" />
    <hkern u1="&#x161;" u2="&#x2019;" k="10" />
    <hkern u1="&#x161;" u2="&#x178;" k="31" />
    <hkern u1="&#x161;" u2="&#xe6;" k="4" />
    <hkern u1="&#x161;" u2="&#xe5;" k="4" />
    <hkern u1="&#x161;" u2="&#xe4;" k="4" />
    <hkern u1="&#x161;" u2="&#xe3;" k="4" />
    <hkern u1="&#x161;" u2="&#xe2;" k="4" />
    <hkern u1="&#x161;" u2="&#xe1;" k="4" />
    <hkern u1="&#x161;" u2="&#xe0;" k="4" />
    <hkern u1="&#x161;" u2="&#xdd;" k="31" />
    <hkern u1="&#x161;" u2="&#xdc;" k="14" />
    <hkern u1="&#x161;" u2="&#xdb;" k="14" />
    <hkern u1="&#x161;" u2="&#xda;" k="14" />
    <hkern u1="&#x161;" u2="&#xd9;" k="14" />
    <hkern u1="&#x161;" u2="&#xc6;" k="12" />
    <hkern u1="&#x161;" u2="&#xc5;" k="12" />
    <hkern u1="&#x161;" u2="&#xc4;" k="12" />
    <hkern u1="&#x161;" u2="&#xc3;" k="12" />
    <hkern u1="&#x161;" u2="&#xc2;" k="12" />
    <hkern u1="&#x161;" u2="&#xc1;" k="12" />
    <hkern u1="&#x161;" u2="&#xc0;" k="12" />
    <hkern u1="&#x161;" u2="x" k="10" />
    <hkern u1="&#x161;" u2="w" k="5" />
    <hkern u1="&#x161;" u2="a" k="4" />
    <hkern u1="&#x161;" u2="Y" k="31" />
    <hkern u1="&#x161;" u2="X" k="6" />
    <hkern u1="&#x161;" u2="W" k="10" />
    <hkern u1="&#x161;" u2="V" k="10" />
    <hkern u1="&#x161;" u2="U" k="14" />
    <hkern u1="&#x161;" u2="T" k="20" />
    <hkern u1="&#x161;" u2="A" k="12" />
    <hkern u1="&#x161;" u2="&#x27;" k="10" />
    <hkern u1="&#x161;" u2="&#x22;" k="10" />
    <hkern u1="&#x178;" g2="fl" k="10" />
    <hkern u1="&#x178;" g2="fi" k="10" />
    <hkern u1="&#x178;" u2="&#x17e;" k="20" />
    <hkern u1="&#x178;" u2="&#x178;" k="-10" />
    <hkern u1="&#x178;" u2="&#x161;" k="31" />
    <hkern u1="&#x178;" u2="&#x160;" k="10" />
    <hkern u1="&#x178;" u2="&#x153;" k="61" />
    <hkern u1="&#x178;" u2="&#x152;" k="24" />
    <hkern u1="&#x178;" u2="&#xff;" k="20" />
    <hkern u1="&#x178;" u2="&#xfd;" k="20" />
    <hkern u1="&#x178;" u2="&#xfc;" k="20" />
    <hkern u1="&#x178;" u2="&#xfb;" k="20" />
    <hkern u1="&#x178;" u2="&#xfa;" k="20" />
    <hkern u1="&#x178;" u2="&#xf9;" k="20" />
    <hkern u1="&#x178;" u2="&#xf6;" k="61" />
    <hkern u1="&#x178;" u2="&#xf5;" k="61" />
    <hkern u1="&#x178;" u2="&#xf4;" k="61" />
    <hkern u1="&#x178;" u2="&#xf3;" k="61" />
    <hkern u1="&#x178;" u2="&#xf2;" k="61" />
    <hkern u1="&#x178;" u2="&#xf0;" k="61" />
    <hkern u1="&#x178;" u2="&#xeb;" k="61" />
    <hkern u1="&#x178;" u2="&#xea;" k="61" />
    <hkern u1="&#x178;" u2="&#xe9;" k="61" />
    <hkern u1="&#x178;" u2="&#xe8;" k="61" />
    <hkern u1="&#x178;" u2="&#xe7;" k="61" />
    <hkern u1="&#x178;" u2="&#xe6;" k="51" />
    <hkern u1="&#x178;" u2="&#xe5;" k="51" />
    <hkern u1="&#x178;" u2="&#xe4;" k="51" />
    <hkern u1="&#x178;" u2="&#xe3;" k="51" />
    <hkern u1="&#x178;" u2="&#xe2;" k="51" />
    <hkern u1="&#x178;" u2="&#xe1;" k="51" />
    <hkern u1="&#x178;" u2="&#xe0;" k="51" />
    <hkern u1="&#x178;" u2="&#xdd;" k="-10" />
    <hkern u1="&#x178;" u2="&#xd8;" k="24" />
    <hkern u1="&#x178;" u2="&#xd6;" k="24" />
    <hkern u1="&#x178;" u2="&#xd5;" k="24" />
    <hkern u1="&#x178;" u2="&#xd4;" k="24" />
    <hkern u1="&#x178;" u2="&#xd3;" k="24" />
    <hkern u1="&#x178;" u2="&#xd2;" k="24" />
    <hkern u1="&#x178;" u2="&#xc7;" k="14" />
    <hkern u1="&#x178;" u2="&#xc6;" k="100" />
    <hkern u1="&#x178;" u2="&#xc5;" k="100" />
    <hkern u1="&#x178;" u2="&#xc4;" k="100" />
    <hkern u1="&#x178;" u2="&#xc3;" k="100" />
    <hkern u1="&#x178;" u2="&#xc2;" k="100" />
    <hkern u1="&#x178;" u2="&#xc1;" k="100" />
    <hkern u1="&#x178;" u2="&#xc0;" k="100" />
    <hkern u1="&#x178;" u2="z" k="20" />
    <hkern u1="&#x178;" u2="y" k="20" />
    <hkern u1="&#x178;" u2="x" k="41" />
    <hkern u1="&#x178;" u2="w" k="10" />
    <hkern u1="&#x178;" u2="v" k="20" />
    <hkern u1="&#x178;" u2="u" k="20" />
    <hkern u1="&#x178;" u2="t" k="10" />
    <hkern u1="&#x178;" u2="s" k="31" />
    <hkern u1="&#x178;" u2="q" k="61" />
    <hkern u1="&#x178;" u2="o" k="61" />
    <hkern u1="&#x178;" u2="g" k="61" />
    <hkern u1="&#x178;" u2="f" k="10" />
    <hkern u1="&#x178;" u2="e" k="61" />
    <hkern u1="&#x178;" u2="d" k="61" />
    <hkern u1="&#x178;" u2="c" k="61" />
    <hkern u1="&#x178;" u2="a" k="51" />
    <hkern u1="&#x178;" u2="Y" k="-10" />
    <hkern u1="&#x178;" u2="X" k="15" />
    <hkern u1="&#x178;" u2="T" k="-10" />
    <hkern u1="&#x178;" u2="S" k="10" />
    <hkern u1="&#x178;" u2="Q" k="24" />
    <hkern u1="&#x178;" u2="O" k="24" />
    <hkern u1="&#x178;" u2="J" k="73" />
    <hkern u1="&#x178;" u2="G" k="14" />
    <hkern u1="&#x178;" u2="C" k="14" />
    <hkern u1="&#x178;" u2="A" k="100" />
    <hkern u1="&#x17d;" u2="&#x153;" k="10" />
    <hkern u1="&#x17d;" u2="&#x152;" k="12" />
    <hkern u1="&#x17d;" u2="&#xff;" k="5" />
    <hkern u1="&#x17d;" u2="&#xfd;" k="5" />
    <hkern u1="&#x17d;" u2="&#xfc;" k="5" />
    <hkern u1="&#x17d;" u2="&#xfb;" k="5" />
    <hkern u1="&#x17d;" u2="&#xfa;" k="5" />
    <hkern u1="&#x17d;" u2="&#xf9;" k="5" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="10" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="10" />
    <hkern u1="&#x17d;" u2="&#xea;" k="10" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="10" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="10" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x17d;" u2="&#xe6;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe5;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe4;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe3;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe2;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe1;" k="6" />
    <hkern u1="&#x17d;" u2="&#xe0;" k="6" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="12" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="12" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="12" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="12" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="12" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="12" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x17d;" u2="y" k="5" />
    <hkern u1="&#x17d;" u2="w" k="10" />
    <hkern u1="&#x17d;" u2="u" k="5" />
    <hkern u1="&#x17d;" u2="q" k="10" />
    <hkern u1="&#x17d;" u2="o" k="10" />
    <hkern u1="&#x17d;" u2="g" k="10" />
    <hkern u1="&#x17d;" u2="e" k="10" />
    <hkern u1="&#x17d;" u2="d" k="10" />
    <hkern u1="&#x17d;" u2="c" k="10" />
    <hkern u1="&#x17d;" u2="a" k="6" />
    <hkern u1="&#x17d;" u2="X" k="12" />
    <hkern u1="&#x17d;" u2="W" k="5" />
    <hkern u1="&#x17d;" u2="T" k="10" />
    <hkern u1="&#x17d;" u2="Q" k="12" />
    <hkern u1="&#x17d;" u2="O" k="12" />
    <hkern u1="&#x17d;" u2="J" k="-10" />
    <hkern u1="&#x17d;" u2="C" k="10" />
    <hkern u1="&#x17e;" g2="fl" k="-10" />
    <hkern u1="&#x17e;" g2="fi" k="-10" />
    <hkern u1="&#x17e;" u2="&#x178;" k="20" />
    <hkern u1="&#x17e;" u2="&#x153;" k="10" />
    <hkern u1="&#x17e;" u2="&#xff;" k="10" />
    <hkern u1="&#x17e;" u2="&#xfd;" k="10" />
    <hkern u1="&#x17e;" u2="&#xfc;" k="10" />
    <hkern u1="&#x17e;" u2="&#xfb;" k="10" />
    <hkern u1="&#x17e;" u2="&#xfa;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf9;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x17e;" u2="&#xf0;" k="10" />
    <hkern u1="&#x17e;" u2="&#xeb;" k="10" />
    <hkern u1="&#x17e;" u2="&#xea;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe9;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe8;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x17e;" u2="&#xe6;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe5;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe4;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe3;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe2;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe1;" k="5" />
    <hkern u1="&#x17e;" u2="&#xe0;" k="5" />
    <hkern u1="&#x17e;" u2="&#xdd;" k="20" />
    <hkern u1="&#x17e;" u2="y" k="10" />
    <hkern u1="&#x17e;" u2="x" k="5" />
    <hkern u1="&#x17e;" u2="w" k="10" />
    <hkern u1="&#x17e;" u2="u" k="10" />
    <hkern u1="&#x17e;" u2="t" k="-10" />
    <hkern u1="&#x17e;" u2="q" k="10" />
    <hkern u1="&#x17e;" u2="o" k="10" />
    <hkern u1="&#x17e;" u2="g" k="10" />
    <hkern u1="&#x17e;" u2="f" k="-10" />
    <hkern u1="&#x17e;" u2="e" k="10" />
    <hkern u1="&#x17e;" u2="d" k="10" />
    <hkern u1="&#x17e;" u2="c" k="10" />
    <hkern u1="&#x17e;" u2="a" k="5" />
    <hkern u1="&#x17e;" u2="Y" k="20" />
    <hkern u1="&#x17e;" u2="W" k="6" />
    <hkern u1="&#x17e;" u2="V" k="20" />
    <hkern u1="&#x17e;" u2="T" k="20" />
    <hkern u1="&#x2018;" u2="&#x153;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="31" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="31" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="31" />
    <hkern u1="&#x2018;" u2="&#xea;" k="31" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="31" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="31" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="31" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="71" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="71" />
    <hkern u1="&#x2018;" u2="x" k="10" />
    <hkern u1="&#x2018;" u2="t" k="-20" />
    <hkern u1="&#x2018;" u2="q" k="31" />
    <hkern u1="&#x2018;" u2="o" k="31" />
    <hkern u1="&#x2018;" u2="g" k="31" />
    <hkern u1="&#x2018;" u2="e" k="31" />
    <hkern u1="&#x2018;" u2="d" k="31" />
    <hkern u1="&#x2018;" u2="c" k="31" />
    <hkern u1="&#x2018;" u2="a" k="10" />
    <hkern u1="&#x2018;" u2="X" k="20" />
    <hkern u1="&#x2018;" u2="W" k="-10" />
    <hkern u1="&#x2018;" u2="T" k="-10" />
    <hkern u1="&#x2018;" u2="A" k="71" />
    <hkern u1="&#x201c;" u2="&#x153;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="31" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="31" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="31" />
    <hkern u1="&#x201c;" u2="&#xea;" k="31" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="31" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="31" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="31" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="71" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="71" />
    <hkern u1="&#x201c;" u2="x" k="10" />
    <hkern u1="&#x201c;" u2="t" k="-20" />
    <hkern u1="&#x201c;" u2="q" k="31" />
    <hkern u1="&#x201c;" u2="o" k="31" />
    <hkern u1="&#x201c;" u2="g" k="31" />
    <hkern u1="&#x201c;" u2="e" k="31" />
    <hkern u1="&#x201c;" u2="d" k="31" />
    <hkern u1="&#x201c;" u2="c" k="31" />
    <hkern u1="&#x201c;" u2="a" k="10" />
    <hkern u1="&#x201c;" u2="X" k="20" />
    <hkern u1="&#x201c;" u2="W" k="-10" />
    <hkern u1="&#x201c;" u2="T" k="-10" />
    <hkern u1="&#x201c;" u2="A" k="71" />
  </font>
</defs></svg>
