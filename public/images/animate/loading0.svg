<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" width="200" height="200" style="shape-rendering: auto; display: block; background: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink"><g><g transform="rotate(0 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.6944444444444444s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(30 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.6313131313131313s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(60 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.5681818181818182s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(90 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.5050505050505051s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(120 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.44191919191919193s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(150 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.3787878787878788s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(180 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.31565656565656564s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(210 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.25252525252525254s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(240 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.1893939393939394s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(270 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.12626262626262627s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(300 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="-0.06313131313131314s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g transform="rotate(330 50 50)">
  <rect fill="#e6e6e6" height="12" width="5" ry="6" rx="2.5" y="24" x="47.5">
    <animate repeatCount="indefinite" begin="0s" dur="0.7575757575757576s" keyTimes="0;1" values="1;0" attributeName="opacity"></animate>
  </rect>
</g><g></g></g><!-- [ldio] generated by https://loading.io --></svg>